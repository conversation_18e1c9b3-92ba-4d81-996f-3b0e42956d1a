# NestJS ERP Project Context

## Project Overview

This document provides the overall context for the NestJS-based ERP system, including its architecture, technologies, and development approach.

## Architecture

The project follows a modular architecture based on NestJS framework with clearly separated responsibilities:

### Directory Structure

- **`src/apps/`**: Domain-specific business modules
  - Each represents a bounded context in the domain
  - Contains controllers, services, entities, DTOs, and resolvers
  
- **`src/core/`**: Core functionality 
  - Authentication and authorization
  - Prisma ORM configuration and service
  - Logging services
  
- **`src/graphql/`**: GraphQL-specific code
  - Schema definition
  - Common GraphQL types
  - GraphQL configuration
  
- **`src/i18n/`**: Internationalization resources
  - Translation files
  - i18n configuration
  
- **`src/infra/`**: Infrastructure services
  - MongoDB connection and services
  - RabbitMQ configuration
  - Redis caching 
  - Other external services
  
- **`src/shared/`**: Shared utilities
  - Common interceptors
  - Validation pipes
  - Utility functions
  - Shared types and interfaces

## Technology Stack

The project utilizes a comprehensive stack of technologies:

### Databases
- **PostgreSQL**: Primary database (source of truth)
- **MongoDB**: Secondary database (for fast reads and analytics)

### ORM & Data Access
- **Prisma ORM**: Modern database toolkit for PostgreSQL
- **Mongoose**: MongoDB object modeling

### API Layers
- **GraphQL**: Primary API (code-first approach with NestJS)
- **REST**: Secondary API with Swagger documentation

### Caching
- **Redis**: For caching, session management, and rate limiting

### Message Processing
- **RabbitMQ**: For asynchronous message handling

### Real-time Communications
- **WebSockets**: For real-time updates

### Other Technologies
- **decimal.js**: For precise financial calculations
- **class-validator & class-transformer**: For DTO validation
- **AWS S3**: For file storage
- **i18n**: For internationalization

## Test-Driven Development Approach

The project follows a test-driven development (TDD) approach where tests are written before implementation:

### Testing Structure

```
- src/
  - apps/
    - [module]/
      - __tests__/
        - unit/
          - services/
          - controllers/
          - resolvers/
        - integration/
        - e2e/
      - controllers/
      - services/
      - resolvers/
      - etc.
```

### TDD Workflow

1. **Write Test First**: Create test file and define expected behavior
2. **Run and Watch Tests Fail**: Verify that tests fail initially
3. **Implement Feature**: Write minimal code to make tests pass
4. **Refactor**: Clean up code while keeping tests green
5. **Document**: Add Swagger documentation to maintain API docs

### Testing Levels

- **Unit Tests**: Test individual components in isolation
  - Services
  - Controllers
  - Resolvers
  - Utilities
  
- **Integration Tests**: Test interactions between components
  - Service-to-service interactions
  - Database interactions
  
- **E2E Tests**: Test complete user journeys
  - API endpoints
  - GraphQL operations

### Mocking Strategy

- Use mock factories for consistent test setup
- Mock external dependencies:
  - Database interactions
  - External services
  - Message brokers
  - Caches

## Coding Standards

### TypeScript
- Use strict typing
- Avoid `any` type
- Use interfaces for object structures
- Document with JSDoc comments

### File Naming
- Use kebab-case for all filenames
- Follow NestJS naming conventions
- Group files by their type within modules

### Path Aliases
- Use path aliases (`@core/`, `@shared/`, `@infra/`, `@apps/`) for imports

## API Design

### REST Endpoints
- Follow RESTful principles
- Use appropriate HTTP methods
- Implement standard pagination, filtering, and sorting
- API versioning
- Consistent response structures

### GraphQL
- Code-first approach
- Clear type definitions
- Proper error handling
- Support for pagination, filtering, and sorting

## Data Management

### PostgreSQL + MongoDB Pattern
- PostgreSQL as source of truth
- Write operations to PostgreSQL first
- Read operations can use MongoDB for performance
- Data synchronization between databases

### Decimal Handling
- Use `decimal.js` for financial calculations
- Never use floating-point for monetary values

## Caching Strategy

- Redis for all caching needs
- Controller-level caching with interceptors
- Appropriate TTL for cached data
- Cache invalidation strategy
- Hierarchical cache keys

## Error Handling

- NestJS exception filters
- Custom exception classes
- Consistent error responses
- Proper logging

## Asynchronous Processing

- RabbitMQ for message handling
- Clear message contracts
- Dead letter queues
- Background jobs using NestJS scheduling

## Documentation

- Swagger/OpenAPI documentation for all endpoints
- Clear request/response schemas
- Example requests
- Up-to-date documentation

## Deployment

- Docker containerization
- Kubernetes orchestration
- CI/CD pipeline
- Environment-specific configurations
