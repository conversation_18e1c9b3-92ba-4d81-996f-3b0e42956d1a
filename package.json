{"name": "core", "version": "0.0.1", "description": "", "author": "", "private": true, "license": "UNLICENSED", "scripts": {"build": "nest build", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "start": "nest start", "start:dev": "nest start --watch", "start:debug": "nest start --debug --watch", "start:prod": "node dist/main", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "test": "jest", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "jest --config ./test/jest-e2e.config.js", "prepare": "husky install"}, "dependencies": {"@apollo/server": "^4.12.1", "@aws-sdk/client-s3": "^3.815.0", "@aws-sdk/s3-request-presigner": "^3.815.0", "@golevelup/nestjs-rabbitmq": "^5.7.0", "@nestjs/apollo": "^13.1.0", "@nestjs/cache-manager": "^3.0.1", "@nestjs/common": "^11.0.20", "@nestjs/config": "^4.0.2", "@nestjs/core": "^11.0.20", "@nestjs/event-emitter": "^3.0.1", "@nestjs/graphql": "^13.1.0", "@nestjs/jwt": "^11.0.0", "@nestjs/mongoose": "^11.0.3", "@nestjs/passport": "^11.0.5", "@nestjs/platform-express": "^11.0.20", "@nestjs/platform-socket.io": "^11.0.20", "@nestjs/schedule": "^6.0.0", "@nestjs/swagger": "^11.2.0", "@nestjs/typeorm": "^11.0.0", "@nestjs/websockets": "^11.0.20", "@prisma/client": "^6.8.2", "@types/xml2js": "^0.4.14", "amqplib": "^0.10.8", "apollo-server-express": "^3.13.0", "aws-sdk": "^2.1692.0", "bcrypt": "^5.1.1", "cache-manager": "^6.4.3", "cache-manager-ioredis": "^2.1.0", "class-transformer": "^0.5.1", "class-validator": "^0.14.2", "date-fns": "^4.1.0", "decimal.js": "^10.5.0", "exceljs": "^4.4.0", "fast-xml-parser": "^5.2.3", "firebase": "^11.8.1", "firebase-admin": "^13.4.0", "graphql": "^16.11.0", "json2csv": "^6.0.0-alpha.2", "mongoose": "^8.15.0", "multer": "^1.4.5-lts.2", "multer-s3": "^3.0.1", "nestjs-i18n": "^10.5.1", "nodemailer": "^7.0.3", "passport-jwt": "^4.0.1", "pg": "^8.16.0", "redis": "^4.7.0", "reflect-metadata": "^0.2.2", "rxjs": "^7.8.2", "socket.io": "^4.8.1", "swagger-ui-express": "^5.0.1", "typeorm": "^0.3.24", "xlsx": "^0.18.5", "xml2js": "^0.6.2"}, "devDependencies": {"@eslint/eslintrc": "^3.3.1", "@eslint/js": "^9.27.0", "@nestjs/cli": "^11.0.7", "@nestjs/schematics": "^11.0.5", "@nestjs/testing": "^11.0.20", "@swc/cli": "^0.7.7", "@swc/core": "^1.11.29", "@types/express": "^5.0.2", "@types/jest": "^29.5.14", "@types/multer": "^1.4.12", "@types/node": "^22.15.21", "@types/nodemailer": "^6.4.17", "@types/supertest": "^6.0.3", "eslint": "^9.27.0", "eslint-config-prettier": "^10.1.5", "eslint-plugin-prettier": "^5.4.0", "globals": "^16.1.0", "husky": "^9.1.7", "jest": "^29.7.0", "lint-staged": "^16.0.0", "prettier": "^3.5.3", "prisma": "^6.8.2", "source-map-support": "^0.5.21", "supertest": "^7.1.1", "ts-jest": "^29.3.4", "ts-loader": "^9.5.2", "ts-node": "^10.9.2", "tsconfig-paths": "^4.2.0", "typescript": "^5.8.3", "typescript-eslint": "^8.32.1"}}