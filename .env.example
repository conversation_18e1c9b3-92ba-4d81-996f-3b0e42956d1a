# Environment variables declared in this file are automatically made available to Prisma.
# See the documentation for more detail: https://pris.ly/d/prisma-schema#accessing-environment-variables-from-the-schema

# Prisma supports the native connection string format for PostgreSQL, MySQL, SQLite, SQL Server, MongoDB and CockroachDB.
# See the documentation for all the connection string options: https://pris.ly/d/connection-strings

DATABASE_URL="**********************************************************/tailor?schema=public"
MONGODB_URI="*****************************************************************"
CACHE_TTL="300"
REDIS_URL="redis://************:6379"
# REDIS_URL=redis://localhost:6379
# RABBITMQ_URI=amqp://localhost
# RabbitMQ Configuration
RABBITMQ_URL="************************************"
RABBITMQ_QUEUE="tailor_queue"
RABBITMQ_EXCHANGE="tailor_exchange"
RABBITMQ_ROUTING_KEY="tailor_routing_key"
# Set to 'false' to make RabbitMQ optional (app will continue running if connection fails)
RABBITMQ_REQUIRED="false"

JWT_SECRET="your_jwt_secret"

# Firebase Configuration
# FIREBASE_PROJECT_ID=your-firebase-project-id
# FIREBASE_CLIENT_EMAIL=your-service-account-email
# FIREBASE_PRIVATE_KEY="your-private-key"
# Or alternatively use a service account file
# FIREBASE_SERVICE_ACCOUNT_PATH=/path/to/service-account.json

# Object Storage Configuration (AWS S3, Digital Ocean Spaces, etc.)
# STORAGE_PROVIDER=s3 # Options: s3, digital_ocean, minio, firebase, local
# STORAGE_ACCESS_KEY_ID=your-access-key-id
# STORAGE_SECRET_ACCESS_KEY=your-secret-access-key
# STORAGE_REGION=nyc3 # e.g., us-east-1 for AWS, nyc3 for Digital Ocean
# STORAGE_BUCKET=your-bucket-name

# i18n Configuration
I18N_DEFAULT_LANG=en # Options: en, es, ru, mn

# For S3-compatible services like Digital Ocean Spaces
# STORAGE_ENDPOINT=https://nyc3.digitaloceanspaces.com
# STORAGE_FORCE_PATH_STYLE=false # Set to true for MinIO and some other providers