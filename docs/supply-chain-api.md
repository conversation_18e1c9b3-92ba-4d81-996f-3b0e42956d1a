# Supply Chain Management API Documentation

## Overview

The Supply Chain Management system provides comprehensive functionality for managing suppliers, inventory, purchase orders, and analytics. This system integrates seamlessly with the existing TailorLink platform's financial system, user management, and real-time notification features.

## Features

### 🏢 Supplier Management
- **Supplier Registration & Verification**: Complete supplier onboarding with verification workflows
- **Supplier Profiles**: Detailed company information, contacts, and certifications
- **Performance Tracking**: Real-time performance metrics and ratings
- **Catalog Management**: Supplier product catalogs with pricing and availability

### 📦 Inventory Management
- **Multi-Category Inventory**: Hierarchical categorization (Fabrics, Threads, Accessories, Tools, Equipment)
- **Real-Time Stock Tracking**: Current, reserved, and available stock levels
- **Automated Alerts**: Low stock, out of stock, overstock, and expiry warnings
- **Cost Management**: Unit costs, average costs, and inventory valuation
- **Transaction History**: Complete audit trail of all inventory movements

### 🛒 Purchase Order Management
- **Order Lifecycle**: From draft to delivery with status tracking
- **Approval Workflows**: Multi-level approval based on order amounts
- **Supplier Integration**: Direct integration with supplier catalogs
- **Financial Integration**: Seamless integration with wallet and credit systems
- **Delivery Tracking**: Receipt confirmation and quality checks

### 📊 Supply Chain Analytics
- **Performance Dashboards**: Real-time KPIs and metrics
- **Supplier Analytics**: Performance comparisons and ratings
- **Inventory Analytics**: Turnover rates, cost analysis, and optimization
- **Demand Forecasting**: AI-powered demand prediction
- **Cost Analysis**: Detailed cost breakdowns and trends

## API Endpoints

### Supplier Management

#### Create Supplier
```http
POST /supply-chain/suppliers
```

**Request Body:**
```json
{
  "companyName": "Premium Fabrics Ltd.",
  "contactPerson": "John Smith",
  "email": "<EMAIL>",
  "phone": "******-123-4567",
  "address": "123 Industrial Ave, Suite 100",
  "city": "New York",
  "state": "NY",
  "country": "United States",
  "postalCode": "10001",
  "website": "https://www.premiumfabrics.com",
  "taxId": "TAX123456789",
  "businessLicense": "BL987654321",
  "businessType": "MANUFACTURER",
  "specializations": ["COTTON_FABRICS", "SILK_FABRICS", "SYNTHETIC_MATERIALS"],
  "certifications": ["ISO_9001", "OEKO_TEX_100", "GOTS_CERTIFIED"],
  "paymentTerms": "NET_30",
  "creditLimit": "50000.00",
  "currency": "USD"
}
```

**Response:**
```json
{
  "id": "123e4567-e89b-12d3-a456-426614174000",
  "companyName": "Premium Fabrics Ltd.",
  "contactPerson": "John Smith",
  "email": "<EMAIL>",
  "status": "PENDING",
  "verificationStatus": "UNVERIFIED",
  "overallRating": null,
  "totalOrders": 0,
  "totalOrderValue": "0.00",
  "createdAt": "2023-01-15T08:30:00Z",
  "updatedAt": "2023-01-15T08:30:00Z"
}
```

#### Get Suppliers
```http
GET /supply-chain/suppliers?status=ACTIVE&page=1&limit=20
```

**Query Parameters:**
- `status`: Filter by supplier status (PENDING, ACTIVE, INACTIVE, SUSPENDED, TERMINATED)
- `verificationStatus`: Filter by verification status (UNVERIFIED, PENDING, VERIFIED, REJECTED, EXPIRED)
- `businessType`: Filter by business type (MANUFACTURER, DISTRIBUTOR, WHOLESALER, RETAILER)
- `specialization`: Filter by specialization category
- `page`: Page number (default: 1)
- `limit`: Items per page (default: 20)

#### Verify Supplier
```http
POST /supply-chain/suppliers/verify
```

**Request Body:**
```json
{
  "supplierId": "123e4567-e89b-12d3-a456-426614174000",
  "decision": "VERIFIED",
  "notes": "All documents verified successfully"
}
```

### Inventory Management

#### Create Inventory Category
```http
POST /supply-chain/inventory/categories
```

**Request Body:**
```json
{
  "name": "Cotton Fabrics",
  "description": "All types of cotton fabrics and materials",
  "parentId": "123e4567-e89b-12d3-a456-426614174000"
}
```

#### Create Inventory Item
```http
POST /supply-chain/inventory/items
```

**Request Body:**
```json
{
  "itemName": "Premium Cotton Fabric - Navy Blue",
  "itemCode": "PCF-NB-001",
  "description": "100% organic cotton fabric, 200 GSM, navy blue color",
  "categoryId": "123e4567-e89b-12d3-a456-426614174000",
  "supplierId": "123e4567-e89b-12d3-a456-426614174001",
  "itemType": "FABRIC",
  "unit": "METER",
  "specifications": {
    "color": "Navy Blue",
    "material": "100% Cotton",
    "weight": "200 GSM",
    "width": "150 cm"
  },
  "currentStock": 500,
  "minimumStock": 50,
  "maximumStock": 1000,
  "unitCost": "15.50",
  "currency": "USD",
  "location": "Warehouse A, Section 1",
  "binLocation": "A1-B2-C3"
}
```

#### Get Inventory Items
```http
GET /supply-chain/inventory/items?categoryId=123&itemType=FABRIC&lowStock=true
```

**Query Parameters:**
- `categoryId`: Filter by category ID
- `itemType`: Filter by item type (FABRIC, THREAD, ACCESSORY, TOOL, EQUIPMENT, CONSUMABLE, PACKAGING)
- `status`: Filter by item status (ACTIVE, INACTIVE, DISCONTINUED, QUARANTINE, DAMAGED, EXPIRED)
- `lowStock`: Filter items with low stock (boolean)
- `search`: Search in item name, code, or description
- `page`: Page number (default: 1)
- `limit`: Items per page (default: 20)

#### Create Inventory Transaction
```http
POST /supply-chain/inventory/transactions
```

**Request Body:**
```json
{
  "inventoryItemId": "123e4567-e89b-12d3-a456-426614174000",
  "transactionType": "PURCHASE",
  "quantity": 100,
  "unitCost": "15.50",
  "reference": "PO-2023-001",
  "referenceType": "PURCHASE_ORDER",
  "notes": "Received from supplier Premium Fabrics Ltd.",
  "batchNumber": "BATCH-2023-001"
}
```

#### Adjust Inventory
```http
POST /supply-chain/inventory/adjust
```

**Request Body:**
```json
{
  "inventoryItemId": "123e4567-e89b-12d3-a456-426614174000",
  "newQuantity": 450,
  "reason": "Physical count adjustment - found discrepancy",
  "notes": "Conducted physical inventory count on 2023-01-15"
}
```

### Purchase Order Management

#### Create Purchase Order
```http
POST /supply-chain/purchase-orders
```

**Request Body:**
```json
{
  "supplierId": "123e4567-e89b-12d3-a456-426614174000",
  "priority": "NORMAL",
  "orderType": "STANDARD",
  "paymentTerms": "NET_30",
  "paymentMethod": "WALLET",
  "requestedDeliveryDate": "2023-02-15T00:00:00Z",
  "deliveryAddress": "123 Main St, Warehouse A, New York, NY 10001",
  "shippingMethod": "GROUND",
  "notes": "Please ensure all items are quality checked before shipping",
  "items": [
    {
      "inventoryItemId": "123e4567-e89b-12d3-a456-426614174000",
      "itemName": "Premium Cotton Fabric - Navy Blue",
      "itemCode": "PCF-NB-001",
      "description": "100% organic cotton fabric, 200 GSM, navy blue color",
      "quantityOrdered": 100,
      "unitPrice": "15.50",
      "discountPercent": "5.00",
      "expectedDeliveryDate": "2023-02-15T00:00:00Z"
    }
  ]
}
```

#### Get Purchase Orders
```http
GET /supply-chain/purchase-orders?status=PENDING_APPROVAL&page=1&limit=20
```

**Query Parameters:**
- `status`: Filter by order status
- `supplierId`: Filter by supplier ID
- `requestedBy`: Filter by requester user ID
- `priority`: Filter by order priority (LOW, NORMAL, HIGH, URGENT, CRITICAL)
- `page`: Page number (default: 1)
- `limit`: Items per page (default: 20)

#### Approve Purchase Order
```http
POST /supply-chain/purchase-orders/approve
```

**Request Body:**
```json
{
  "purchaseOrderId": "123e4567-e89b-12d3-a456-426614174000",
  "decision": "APPROVED",
  "comments": "Approved within budget limits",
  "conditions": ["Must be delivered by specified date", "Quality inspection required"]
}
```

#### Update Purchase Order Status
```http
PUT /supply-chain/purchase-orders/{id}/status
```

**Request Body:**
```json
{
  "status": "SENT",
  "notes": "Purchase order sent to supplier via email"
}
```

### Supply Chain Analytics

#### Get Supply Chain Dashboard
```http
GET /supply-chain/analytics/dashboard
```

**Response:**
```json
{
  "suppliers": {
    "total": 25,
    "active": 20,
    "verificationPending": 5
  },
  "inventory": {
    "totalItems": 150,
    "lowStockItems": 8,
    "totalValue": "125000.00",
    "turnoverRate": "4.2"
  },
  "purchaseOrders": {
    "total": 75,
    "pending": 12,
    "monthlyValue": "45000.00",
    "averageLeadTime": "7.5"
  },
  "performance": {
    "supplierPerformance": [...],
    "categoryAnalysis": [...],
    "costTrends": [...]
  }
}
```

#### Get Supplier Performance Report
```http
GET /supply-chain/analytics/suppliers/performance?startDate=2023-01-01&endDate=2023-12-31
```

#### Get Inventory Analytics
```http
GET /supply-chain/analytics/inventory/analytics
```

#### Get Demand Forecast
```http
GET /supply-chain/analytics/demand/forecast?inventoryItemId=123&forecastPeriod=MONTHLY&periodsAhead=3
```

#### Get Purchase Order Analytics
```http
GET /supply-chain/analytics/purchase-orders/analytics?startDate=2023-01-01&endDate=2023-12-31
```

## Authentication & Authorization

All endpoints require JWT authentication via the `Authorization: Bearer <token>` header.

### Required Roles

- **Admin**: Full access to all supply chain features
- **Supply Chain Manager**: Full access to supply chain management
- **Inventory Manager**: Inventory and stock management
- **Purchase Manager**: Purchase order management and approval
- **Finance Manager**: Financial aspects and approvals
- **Warehouse Manager**: Receiving and quality checks
- **Tailor**: Read access to inventory for order planning

## Real-Time Features

### WebSocket Notifications

The system sends real-time notifications for:

- **Stock Alerts**: Low stock, out of stock, expiry warnings
- **Purchase Order Updates**: Status changes, approvals, deliveries
- **Supplier Updates**: New registrations, verification status changes
- **System Alerts**: Critical supply chain issues

### Event-Driven Architecture

All major actions publish events to RabbitMQ:

- `supplier_created`, `supplier_updated`, `supplier_verified`
- `inventory_item_created`, `inventory_transaction_created`, `stock_alert_created`
- `purchase_order_created`, `purchase_order_approved`, `purchase_order_status_changed`

## Integration with Existing Systems

### Financial System Integration

- **Wallet Payments**: Purchase orders can be paid using digital wallets
- **Credit Terms**: Suppliers can offer credit terms integrated with the credit system
- **Installment Payments**: Large orders can be paid in installments
- **Financial Reporting**: Supply chain costs integrated into financial reports

### User Management Integration

- **Role-Based Access**: Leverages existing role and permission system
- **Audit Trails**: All actions logged with user information
- **Notifications**: Integrated with existing notification system

### Order System Integration

- **Inventory Allocation**: Orders automatically reserve inventory
- **Material Requirements**: Orders can trigger purchase orders for materials
- **Cost Calculation**: Material costs included in order pricing

## Error Handling

The API uses standard HTTP status codes and returns detailed error messages:

```json
{
  "statusCode": 400,
  "message": "Validation failed",
  "error": "Bad Request",
  "details": [
    {
      "field": "itemCode",
      "message": "Item code already exists"
    }
  ]
}
```

## Rate Limiting

- **Standard endpoints**: 100 requests per minute per user
- **Analytics endpoints**: 20 requests per minute per user
- **Bulk operations**: 10 requests per minute per user

## Data Export

All data can be exported in multiple formats:

- **CSV**: For spreadsheet analysis
- **JSON**: For system integration
- **PDF**: For reporting and documentation

## Security Features

- **Data Encryption**: All sensitive data encrypted at rest and in transit
- **Audit Trails**: Complete audit logs for all operations
- **Access Controls**: Fine-grained permissions and role-based access
- **Data Validation**: Comprehensive input validation and sanitization
