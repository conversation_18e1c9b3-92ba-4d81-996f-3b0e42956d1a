**🏠 ОЁДОЛЧИД-ЗАХИАЛАГЧДЫН ОЛОН НИЙТИЙН ПЛАТФОРМ ТӨСЛИЙН БИЧИГ БАРИМТ, ШААРДЛАГЫН ТОДОРХОЙЛОЛТ (SRS)**

---

## 1. ТӨСЛИЙН ЕРӨНХИЙ ТАНИЛЦУУЛГА

**Нэр:** TailorLink (түр нэр)

**Төрөл:** Хос талт олон нийтийн платформ

**Зорилго:** Оёдолчид болон захиалагчдын харилцааг дэмжих, оёдолчдын ур чадварыг сурталчлах, захиалгыг хялбар болгох, материалын нийлүүлэлтийг төвлөрүүлэх цогц платформ хөгжүүлэх.

---

## 2. ХЭРЭГЛЭГЧИД

* **Захиалагч (Customer)** – хувийн захиалга өгөх, хувцасны хэмжээ оруулах, оёдолч сонгох, төлбөр хийх
* **Оёдолчин (Tailor)** – захиалга хүлээн авах, хэмжээс үзэх, профайл хөгжүүлэх, материал бүртгэх, бүтээл нийтлэх
* **Админ (Admin)** – платформ хянах, хэрэглэгч, бүтээгдэхүүн, сургалт, тайлан хариуцах

---

## 3. СИСТЕМИЙН ХҮРЭЭ

### Үндсэн модулиуд:

* Хэрэглэгчийн бүртгэл, нэвтрэх
* Биеийн хэмжээс бүртгэл
* Захиалга үүсгэх ба удирдах
* Tailor профайл ба бүтээл
* Материалын худалдаа (Supplies marketplace)
* Чат систем
* Төлбөрийн интеграц
* Сургалтын модуль
* Админ самбар (dashboard)

---

## 4. ФУНКЦИОНАЛ ШААРДЛАГА

(Дэлгэрэнгүй тайлбарыг баримтын “📌 ФУНКЦИОНАЛ ШААРДЛАГА” хэсгээс харна уу)

---

## 5. СИСТЕМИЙН ШААРДЛАГА

### 5.1 Нефункциональ шаардлага:

* Хэрэглэхэд хялбар, олон төхөөрөмжид зохицсон UX
* 99.9% uptime бүхий найдвартай ажиллагаа
* Backend latency < 200ms
* Хэрэглэгчийн мэдээлэл GDPR стандарт хангасан байх
* Уян хатан архитектур (Microservice-based)

### 5.2 Гүйцэтгэл:

* Хэрэглэгчийн 10,000+ зэрэгцээ идэвхтэй хэрэглээг дэмжих
* Firebase push notification 10K/мин
* Материалын каталогт 50,000+ бараа хадгалах

### 5.3 Аюулгүй байдал:

* Role-based access control
* Token-based authentication (JWT)
* Secure payment API (QPay, SocialPay)
* File upload хяналт (image, PDF validation)

---

## 6. БОЛОВСРУУЛАЛТЫН ХҮРЭЭ (SCOPE OF DEVELOPMENT)

* **MVP хүрээнд:** хэрэглэгч бүртгэл, захиалга, хэмжээс, профайл, чат, бүтээгдэхүүн худалдан авалт, төлбөр
* **Дараагийн шатанд:** AI хэмжээ тооцоо, видео сургалт, хүргэлтийн интеграц, ухаалаг зөвлөмж

---

## 6A. UI FLOW CHART / SCREEN MAP

### 1. Захиалагчийн апп (Customer)

```
Login / Signup
 └── Home Dashboard
      ├── Profile
      │    └── Edit Info / Measurements
      ├── Browse Tailors
      │    └── Tailor Profile → Reviews / Portfolio / Message
      ├── My Orders
      │    └── Order Detail → Status Tracker
      ├── Create Order
      │    └── Select Garment → Enter Details → Submit
      ├── Products (Marketplace)
      │    └── Product Detail → Add to Cart → Checkout
      └── Training / Learning
           └── Video / Document View
```

### 2. Tailor апп (Tailor)

```
Login / Signup
 └── Tailor Dashboard
      ├── My Profile
      │    └── Certificates / Portfolio Upload
      ├── My Orders
      │    └── View Order → View Measurements → Update Status
      ├── Product Orders (from marketplace)
      ├── Inventory / Supplies
      ├── Messages (Chat with customers)
      └── Training Center
           └── My Courses / Add Training Content
```

### 3. Админ самбар (Admin Panel)

```
Admin Login
 └── Dashboard Overview
      ├── Manage Users (Tailors / Customers)
      ├── Manage Orders
      ├── Approve / Suspend Tailor Accounts
      ├── Upload Products to Marketplace
      ├── Training Management
      └── View Analytics / Reports
```

### 4. Нийтлэг урсгалууд

* Push Notifications (Firebase-based)

* Realtime Chat View per Order

* Checkout Flow with Payment Gateways

* Responsive UX for Web + Mobile

* **MVP хүрээнд:** хэрэглэгч бүртгэл, захиалга, хэмжээс, профайл, чат, бүтээгдэхүүн худалдан авалт, төлбөр

* **Дараагийн шатанд:** AI хэмжээ тооцоо, видео сургалт, хүргэлтийн интеграц, ухаалаг зөвлөмж

---

## 7. ТЕСТ, НӨХЦӨЛ, ХҮЛЭЭН ЗӨВШӨӨРӨХ

| Тестийн төрөл    | Жишээ                                        |
| ---------------- | -------------------------------------------- |
| Unit Test        | Захиалга бүртгэлийн логик, хэмжээс validator |
| Integration Test | Төлбөрийн систем, Firebase push              |
| UI/UX Test       | Responsive view, navigation flow             |
| Load Test        | 10K хэрэглэгч зэрэгцээ нэвтрэх үед           |

---

## 8. ХӨГЖҮҮЛЭЛТИЙН ОРЧИН, ТЕХНИК

* Backend: NestJS + GraphQL
* Frontend: Flutter (Mobile) + Next.js (Web)
* Database: PostgreSQL (write) + MongoDB (read)
* Storage: Firebase / AWS S3
* Auth: Firebase Auth + JWT
* Notification: Firebase Messaging
* Hosting: Docker + Traefik, scalable infra

---

## 9. ӨГӨГДЛИЙН САНГИЙН БҮТЭЦ (DB SCHEMA)

### Users

```
- id (UUID)
- name
- email
- password_hash
- role (CUSTOMER, TAILOR, ADMIN)
- avatar_url
- created_at
- updated_at
```

### TailorProfiles

```
- id
- user_id (FK -> Users)
- bio
- certificates (array or related table)
- portfolio_images (array or related table)
- experience_years
```

### Measurements

```
- id
- user_id (FK -> Users)
- height, weight, chest, waist, hips, shoulder_width
- neck, arm_length, leg_length, thigh, calf, foot_size
- measurement_unit (cm/inch)
- created_at
- updated_at
```

### Orders

```
- id
- customer_id (FK -> Users)
- tailor_id (FK -> Users)
- status (NEW, APPROVED, IN_PROGRESS, COMPLETE, DELIVERED)
- due_date
- notes
- created_at
```

### OrderItems

```
- id
- order_id (FK -> Orders)
- type (e.g., dress, pants)
- fabric
- color
- description
```

### Products (Supplies marketplace)

```
- id
- name
- category
- description
- price
- stock_quantity
- supplier_name
- image_url
- created_at
```

### Carts

```
- id
- user_id (FK -> Users)
- product_id (FK -> Products)
- quantity
```

### Payments

```
- id
- order_id (FK -> Orders)
- amount
- method (QPay, SocialPay, etc)
- status (PENDING, PAID, FAILED)
- paid_at
```

### Messages (Chat)

```
- id
- order_id (FK -> Orders)
- sender_id (FK -> Users)
- message
- created_at
```

### Trainings

```
- id
- title
- instructor_id (FK -> Users)
- content_type (video, pdf)
- url
- price
- access_level (free, premium)
```

### Notifications

```
- id
- user_id (FK -> Users)
- title
- body
- read (boolean)
- created_at
```

* Backend: NestJS + GraphQL
* Frontend: Flutter (Mobile) + Next.js (Web)
* Database: PostgreSQL (write) + MongoDB (read)
* Storage: Firebase / AWS S3
* Auth: Firebase Auth + JWT
* Notification: Firebase Messaging
* Hosting: Docker + Traefik, scalable infra

---

Энэхүү SRS баримт нь төслийн хөгжүүлэлтийг зөв гүйцэтгэх, хэрэглэгчид болон захиалагч талуудад ойлгомжтой хүргэх, хөгжүүлэгчдийн хоорондын зохион байгуулалтыг сайжруулах үндсэн хэрэглэгдэхүүн болно.
