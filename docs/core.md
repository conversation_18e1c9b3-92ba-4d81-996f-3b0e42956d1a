Here is a detailed English version of your ERP project specification, structured clearly with purpose, tech stack, architecture goals, and development requirements:

ERP Base Project Specification (NestJS)
✅ Objective
Build a modern, high-performance, modular ERP backend using NestJS. The system must follow international best practices and coding standards, support enterprise-grade integrations, and provide a fast development experience.

🏗️ Architecture & Stack
Core Technologies
Framework: NestJS (modular, scalable structure)

Primary Database: PostgreSQL (relational, structured data)

Secondary Database: MongoDB (NoSQL, used for fast read operations)

ORM: Prisma (for PostgreSQL and possibly MongoDB)

Cache Layer: Redis (in-memory caching, session storage)

Message Broker: RabbitMQ (microservice communication, task queues)

Realtime: WebSocket (for real-time data updates)

API Support
GraphQL (preferred for flexible data queries)

Optional: REST API layer for public/internal usage

🛠️ Features and Requirements
1. Data Sync Logic
All data writes go to PostgreSQL (source of truth)

A sync service will automatically mirror data into MongoDB

All data reads (especially analytics, dashboards, lists) will be performed from MongoDB for speed and flexibility

2. Data Import/Export
Support importing/exporting in formats:

JSON

XML

XLSX (Excel)

Upload and download functionality should support large files with validation and progress indication.

3. Decimal Handling
Use decimal.js for high-precision decimal operations (especially in financial modules)

4. Caching
Redis for:

Request-level cache (e.g., GraphQL response cache)

Session/token storage

Temporary data (e.g., OTP, 2FA)

5. Messaging
Use RabbitMQ to:

Handle async tasks (e.g., data sync, notifications, background jobs)

Decouple services for scalability

6. Realtime
Implement WebSocket gateways for:

Notification system

Live updates on dashboard

Chat or collaboration features

📦 Modular Structure Example
bash
Copy
Edit
src/
├── apps/
│   ├── user/
│   ├── product/
│   ├── order/
│   └── finance/
├── core/           # Auth, Config, Prisma, Logger
├── shared/         # Common utilities, DTOs, Pipes, Interceptors
├── infra/          # Redis, RabbitMQ, Mongo, Sockets
├── graphql/        # GraphQL schema definitions
└── main.ts
📐 Dev Standards
Follow SOLID, DRY, and Clean Architecture

Use DTOs and class-validator for input validation

Handle errors globally with custom exceptions

All configs should support .env and be injected via @nestjs/config

Enable auto-generated API docs via Swagger or GraphQL playground

Apply E2E and unit testing with Jest

7. API Standardization
All CRUD endpoints that return lists (`getList`) must support the following query parameters:
- `page`: Current page number (pagination)
- `limit`: Items per page (pagination)
- `where`: JSON filtering conditions
- `search`: Text search parameter
- `order`: Sort order specification (field and direction)

✅ Summary Goals
✅ High performance (cached reads, async jobs)

✅ Easy to maintain and scale

✅ Compatible with global ERP features

✅ Rapid development for future modules

✅ Internationalization (i18n ready)

✅ Clean and documented codebase

✅ All service must have swagger