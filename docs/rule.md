# NestJS ERP Project Development Rules & Guidelines

This document outlines the development rules, conventions, and best practices that all team members must follow while working on this NestJS-based ERP system.

## 1. Code Organization

### 1.1 Project Structure
- Top-level directories in `/src`:
  - `apps/`: Domain-specific business modules
  - `core/`: Core functionality (auth, prisma, logger)
  - `graphql/`: GraphQL-specific code and configuration
  - `i18n/`: Internationalization resources
  - `infra/`: Infrastructure services (MongoDB, RabbitMQ, Redis, etc.)
  - `shared/`: Shared utilities, interceptors, pipes, etc.

### 1.2 Module Structure
- Each business domain has its own module in the `src/apps/` directory
- Business modules follow this structure:
  - `controllers/`: REST API controllers
  - `dto/`: Data Transfer Objects
  - `entities/`: Prisma entity extensions
  - `resolvers/`: GraphQL resolvers
  - `services/`: Business logic services
  - `[module-name].module.ts`: Module definition
- Shared functionality should be placed in appropriate directories (`shared/`, `core/`, `infra/`)
- Keep module dependencies clean - higher level modules should not import from lower level modules

### 1.3 File Naming
- Use kebab-case for all filenames (e.g., `user-profile.service.ts`)
- Follow NestJS naming conventions:
  - `*.module.ts` for modules
  - `*.controller.ts` for controllers
  - `*.service.ts` for services
  - `*.dto.ts` for Data Transfer Objects
  - `*.entity.ts` for Prisma entity extensions
  - `*.interface.ts` for interfaces
  - `*.enum.ts` for enumerations
  - `*.repository.ts` for repository patterns

### 1.4 Path Aliases
- Always use path aliases (`@core/`, `@shared/`, `@infra/`, `@apps/`, etc.) instead of relative paths
- Update aliases in both `tsconfig.json` and `jest.config.js` when adding new directories
- Path aliases make imports cleaner and easier to maintain

## 2. Coding Standards

### 2.1 TypeScript
- Use strict typing - avoid `any` type unless absolutely necessary
- Enable strict mode in TypeScript configuration
- Use interfaces for complex object structures
- Document functions and classes with JSDoc comments

### 2.2 Formatting
- Use ESLint and Prettier for code formatting
- 2 spaces for indentation
- Maximum line length of 100 characters
- Semicolons required
- Single quotes for strings

### 2.3 Imports
- Order imports in the following sequence:
  1. External libraries
  2. NestJS framework packages
  3. Project modules (using path aliases)
  4. Relative imports
- Sort imports alphabetically within each group
- Leave a blank line between groups

## 3. API Design

### 3.1 REST Endpoints
- Follow RESTful principles for all API endpoints
- Use appropriate HTTP methods (GET, POST, PUT, DELETE)
- All endpoints returning lists must implement the standard pagination parameters:
  - `page`: Current page number
  - `limit`: Items per page (max 100)
  - `where`: JSON filtering conditions
  - `search`: Text search parameter
  - `order`: Sort order specification (field and direction)
- Use versioning for APIs (e.g., `/v1/users`)
- Return consistent response structures

### 3.2 GraphQL
- Use code-first approach with NestJS GraphQL
- Define clear types for all queries and mutations
- Implement proper error handling
- Support pagination, filtering, and sorting
- Keep resolvers thin, delegate business logic to services

### 3.3 Documentation
- All endpoints must have Swagger/OpenAPI documentation
- Document request/response schemas, query parameters, and possible errors
- Include example requests where helpful

## 4. Data Management

### 4.1 Database
- PostgreSQL is the system of record (source of truth)
- All write operations must go to PostgreSQL first
- Read operations should utilize MongoDB when appropriate for performance
- Follow the data sync pattern to keep MongoDB synchronized with PostgreSQL

### 4.2 Prisma
- Follow Prisma modeling best practices
- Use migrations for all schema changes
- Seed data should be provided for development environments
- Never expose Prisma models directly in responses, always use DTOs

### 4.3 MongoDB
- Use for read-heavy operations and analytics
- Schema should mirror PostgreSQL where appropriate
- Utilize MongoDB's querying capabilities for complex searches and aggregations

### 4.4 Decimal Handling
- Use `decimal.js` for all financial calculations
- Never use floating-point types (number) for monetary values

## 5. Caching Strategy

### 5.1 Redis
- Use Redis for all caching needs
- Cache responses at the controller level using interceptors
- Set appropriate TTL (Time-To-Live) for cached data
- Implement cache invalidation when underlying data changes
- Use cache for session management and rate limiting

### 5.2 Cache Keys
- Use descriptive, hierarchical cache keys
- Include version in cache keys to facilitate cache invalidation during deployments
- Implement cache namespace per module

## 6. Error Handling

### 6.1 Exceptions
- Use NestJS exception filters for global error handling
- Create custom exception classes for domain-specific errors
- Log all exceptions with appropriate severity levels
- Return consistent error responses with:
  - HTTP status code
  - Error code
  - Error message
  - Optional detailed information (in development)

### 6.2 Validation
- Use class-validator for DTO validation
- Implement custom validators for domain-specific validation
- Validate all incoming data at the controller level

## 7. Asynchronous Processing

### 7.1 RabbitMQ
- Use RabbitMQ for all asynchronous message handling
- Define clear message contracts
- Implement proper error handling and dead letter queues
- Use appropriate exchange and queue types based on the use case

### 7.2 Background Jobs
- Implement cron jobs using NestJS scheduling module
- Keep scheduler code separate from business logic
- Log job execution for monitoring

## 8. Testing

### 8.1 Unit Tests
- All services and utilities must have unit tests
- Use Jest for testing
- Mock external dependencies
- Aim for 80% code coverage minimum

### 8.2 Integration Tests
- Test database interactions with an actual test database
- Test API endpoints with supertest
- Implement cleanup between tests

### 8.3 E2E Tests
- Write end-to-end tests for critical user flows
- Use separate test database for E2E tests
- Automate E2E tests in CI/CD pipeline

### 8.4 Test Setup
- Use `src/shared/testing/test.module.ts` for setting up testing modules
- Use `src/shared/testing/test.utils.ts` for test utilities
- Configure Jest for path aliases and test environment

## 9. Security

### 9.1 Authentication & Authorization
- Implement JWT-based authentication
- Use role-based access control
- Validate permissions for all operations
- Store credentials securely

### 9.2 Permission Decorators
- Create and use custom permission decorators for endpoint authorization
- All endpoints must be protected with appropriate permission decorators
- Permission decorators should be chainable and composable
- Implement hierarchical permission structure (e.g., admin > manager > user)
- Use descriptive names for permissions following a consistent naming pattern (e.g., 'resource:action')
- Document all permissions in a central location

### 9.3 Data Protection
- Encrypt sensitive data at rest
- Use HTTPS for all communications
- Implement rate limiting for public endpoints
- Follow OWASP security guidelines

## 10. Internationalization

### 10.1 i18n Structure
- All i18n resources are stored in `src/i18n/locales/`
- Each language has its own directory (e.g., `en`, `es`, `ru`, `mn`)
- Translation files are organized by namespaces (e.g., `common.json`)
- Translations follow the same key structure across all languages

### 10.2 i18n Usage
- Support multiple languages using nestjs-i18n
- All user-facing messages should be internationalized
- Store translations in JSON format
- Support language selection in API requests via:
  - Headers: `x-custom-lang`, `x-lang`, `Accept-Language`
  - Cookies: `lang`, `locale`
  - Query parameters
- Default language is configured via `I18N_DEFAULT_LANG` environment variable

## 11. Logging and Monitoring

### 11.1 Logging
- Use structured logging (JSON format)
- Include appropriate context in all logs (request ID, user ID, etc.)
- Log all significant operations for auditing
- Configure different log levels based on environment

### 11.2 Monitoring
- Implement health check endpoints
- Collect metrics for key performance indicators
- Use appropriate monitoring tools for production environment

## 12. Environment Configuration

### 12.1 Environment Variables
- Use environment variables for all configuration
- Provide `.env.example` with all required variables
- Never commit actual .env files to version control
- Validate environment variables on application startup

### 12.2 Configuration Categories
- Database connections (PostgreSQL, MongoDB)
- Cache settings (Redis)
- Message queue settings (RabbitMQ)
- Authentication settings
- Storage settings (AWS S3, etc.)
- i18n configuration
- API configuration

## 13. Performance

### 13.1 Optimization
- Use proper indexes in databases
- Implement caching for expensive operations
- Optimize database queries
- Use streaming for large data transfers

### 13.2 Monitoring
- Monitor response times
- Implement tracing for complex operations
- Use profiling tools to identify bottlenecks

## 14. Code Review Process

### 14.1 Pull Request Guidelines
- Keep PRs focused and reasonably sized (max 500 lines of code)
- Include tests for all new functionality
- Update documentation when needed
- Provide clear PR descriptions with context
- All PRs must be reviewed by at least one other developer

### 14.2 Review Checklist
- Code meets style guidelines
- All tests pass
- No unnecessary code duplication
- Proper error handling
- No security vulnerabilities
- Performance considerations addressed

## 15. Release Management

### 15.1 Versioning
- Follow Semantic Versioning (MAJOR.MINOR.PATCH)
- Update CHANGELOG.md for all releases
- Tag release commits

### 15.2 Deployment
- Use CI/CD for automated deployments
- Implement blue/green deployment for zero downtime
- Include database migration strategy
- Have rollback plans for all deployments

## 16. Docker and Containerization

### 16.1 Development Environment
- Provide Docker Compose configuration for local development
- Include all dependencies (PostgreSQL, MongoDB, Redis, RabbitMQ) in Docker Compose setup
- Ensure volumes are used to persist data between container restarts
- Configure hot-reloading for development containers
- Document all available Docker Compose services

### 16.2 Production Containers
- Build optimized, minimal images for production
- Use multi-stage builds to reduce final image size
- Never run containers as root
- Implement health checks for all containers
- Follow container security best practices
- Tag images using semantic versioning
- Include necessary metadata using labels

### 16.3 CI/CD Integration
- Build and test Docker images in CI pipeline
- Implement automated security scanning for Docker images
- Push versioned images to container registry
- Use Docker images as the deployment artifact

---

These rules and guidelines aim to ensure a high-quality, maintainable, and performant ERP system. All team members are expected to follow these guidelines and suggest improvements when necessary.