# Enhanced Order Status Tracking Implementation

## Overview
Implement detailed order status tracking to match the business process requirements for tailoring workflow stages.

## Current vs Required Status Flow

### Current Implementation
```typescript
enum OrderStatus {
  NEW = 'NEW',
  APPROVED = 'APPROVED', 
  IN_PROGRESS = 'IN_PROGRESS',
  COMPLETE = 'COMPLETE',
  DELIVERED = 'DELIVERED',
  CANCELLED = 'CANCELLED',
  RETURNED = 'RETURNED',
}
```

### Enhanced Status Flow (Business Process Aligned)
```typescript
enum DetailedOrderStatus {
  // Initial stages
  NEW = 'NEW',
  ORDER_RECEIVED = 'ORDER_RECEIVED',
  
  // Preparation stages
  MEASUREMENTS_CONFIRMED = 'MEASUREMENTS_CONFIRMED',
  MATERIALS_ACQUIRED = 'MATERIALS_ACQUIRED',
  
  // Production stages
  CUTTING_STARTED = 'CUTTING_STARTED',
  STITCHING_IN_PROGRESS = 'STITCHING_IN_PROGRESS',
  FINISHING_TOUCHES = 'FINISHING_TOUCHES',
  
  // Quality and completion
  QUALITY_CHECK = 'QUALITY_CHECK',
  READY_FOR_DELIVERY = 'READY_FOR_DELIVERY',
  
  // Final stages
  DELIVERED = 'DELIVERED',
  COMPLETED = 'COMPLETED',
  
  // Exception cases
  CANCELLED = 'CANCELLED',
  RETURNED = 'RETURNED',
}
```

## Implementation Plan

### 1. Database Schema Updates
```prisma
model Order {
  // ... existing fields
  
  // Enhanced tracking fields
  detailedStatus       DetailedOrderStatus @default(NEW)
  statusHistory        OrderStatusHistory[]
  estimatedCompletion  DateTime?
  actualCompletion     DateTime?
  
  // Stage-specific timestamps
  measurementsConfirmedAt DateTime?
  materialsAcquiredAt     DateTime?
  cuttingStartedAt        DateTime?
  stitchingStartedAt      DateTime?
  finishingStartedAt      DateTime?
  qualityCheckAt          DateTime?
  readyForDeliveryAt      DateTime?
}

model OrderStatusHistory {
  id          String              @id @default(uuid())
  orderId     String
  status      DetailedOrderStatus
  notes       String?
  updatedBy   String              // User ID who made the change
  timestamp   DateTime            @default(now())
  
  order       Order               @relation(fields: [orderId], references: [id])
  updater     User                @relation(fields: [updatedBy], references: [id])
}
```

### 2. Enhanced Order Service Methods
```typescript
class OrderService {
  async updateOrderStatus(
    orderId: string, 
    newStatus: DetailedOrderStatus, 
    notes?: string,
    updatedBy?: string
  ): Promise<OrderDto> {
    // Validate status transition
    await this.validateStatusTransition(orderId, newStatus);
    
    // Update order with timestamp
    const updatedOrder = await this.prisma.order.update({
      where: { id: orderId },
      data: {
        detailedStatus: newStatus,
        ...this.getTimestampField(newStatus),
      },
    });
    
    // Record status history
    await this.recordStatusHistory(orderId, newStatus, notes, updatedBy);
    
    // Send notifications
    await this.notifyStatusChange(updatedOrder, newStatus);
    
    return this.mapToOrderDto(updatedOrder);
  }
  
  private getTimestampField(status: DetailedOrderStatus): Record<string, Date> {
    const now = new Date();
    switch (status) {
      case DetailedOrderStatus.MEASUREMENTS_CONFIRMED:
        return { measurementsConfirmedAt: now };
      case DetailedOrderStatus.MATERIALS_ACQUIRED:
        return { materialsAcquiredAt: now };
      case DetailedOrderStatus.CUTTING_STARTED:
        return { cuttingStartedAt: now };
      case DetailedOrderStatus.STITCHING_IN_PROGRESS:
        return { stitchingStartedAt: now };
      case DetailedOrderStatus.FINISHING_TOUCHES:
        return { finishingStartedAt: now };
      case DetailedOrderStatus.QUALITY_CHECK:
        return { qualityCheckAt: now };
      case DetailedOrderStatus.READY_FOR_DELIVERY:
        return { readyForDeliveryAt: now };
      default:
        return {};
    }
  }
}
```

### 3. Status Transition Validation
```typescript
class StatusTransitionValidator {
  private static readonly VALID_TRANSITIONS: Record<DetailedOrderStatus, DetailedOrderStatus[]> = {
    [DetailedOrderStatus.NEW]: [
      DetailedOrderStatus.ORDER_RECEIVED,
      DetailedOrderStatus.CANCELLED
    ],
    [DetailedOrderStatus.ORDER_RECEIVED]: [
      DetailedOrderStatus.MEASUREMENTS_CONFIRMED,
      DetailedOrderStatus.CANCELLED
    ],
    [DetailedOrderStatus.MEASUREMENTS_CONFIRMED]: [
      DetailedOrderStatus.MATERIALS_ACQUIRED,
      DetailedOrderStatus.CANCELLED
    ],
    // ... define all valid transitions
  };
  
  static isValidTransition(from: DetailedOrderStatus, to: DetailedOrderStatus): boolean {
    return this.VALID_TRANSITIONS[from]?.includes(to) ?? false;
  }
}
```

### 4. Enhanced Notification System
```typescript
class OrderNotificationService {
  async notifyStatusChange(order: Order, newStatus: DetailedOrderStatus): Promise<void> {
    const message = this.getStatusMessage(newStatus);
    
    // Notify customer
    await this.websocketService.sendToUser(order.customerId, 'order_status_update', {
      orderId: order.id,
      status: newStatus,
      message,
      estimatedCompletion: order.estimatedCompletion,
    });
    
    // Notify tailor if applicable
    if (order.tailorId) {
      await this.websocketService.sendToUser(order.tailorId, 'order_status_update', {
        orderId: order.id,
        status: newStatus,
        message: `Order ${order.id} status updated to ${newStatus}`,
      });
    }
    
    // Create database notification
    await this.createNotification(order, newStatus, message);
  }
  
  private getStatusMessage(status: DetailedOrderStatus): string {
    const messages = {
      [DetailedOrderStatus.ORDER_RECEIVED]: 'Your order has been received and is being reviewed.',
      [DetailedOrderStatus.MEASUREMENTS_CONFIRMED]: 'Your measurements have been confirmed.',
      [DetailedOrderStatus.MATERIALS_ACQUIRED]: 'Materials for your order have been acquired.',
      [DetailedOrderStatus.CUTTING_STARTED]: 'Cutting process has started for your garment.',
      [DetailedOrderStatus.STITCHING_IN_PROGRESS]: 'Your garment is being stitched.',
      [DetailedOrderStatus.FINISHING_TOUCHES]: 'Final touches are being added to your garment.',
      [DetailedOrderStatus.QUALITY_CHECK]: 'Your garment is undergoing quality inspection.',
      [DetailedOrderStatus.READY_FOR_DELIVERY]: 'Your order is ready for pickup/delivery!',
      [DetailedOrderStatus.DELIVERED]: 'Your order has been delivered.',
      [DetailedOrderStatus.COMPLETED]: 'Your order is complete. Thank you!',
    };
    
    return messages[status] || 'Order status updated.';
  }
}
```

### 5. API Enhancements
```typescript
// New endpoints for detailed tracking
@Patch(':id/status')
async updateDetailedStatus(
  @Param('id') id: string,
  @Body() updateStatusDto: UpdateOrderStatusDto,
  @CurrentUser() user: User,
): Promise<OrderDto> {
  return this.orderService.updateOrderStatus(
    id, 
    updateStatusDto.status, 
    updateStatusDto.notes,
    user.id
  );
}

@Get(':id/status-history')
async getStatusHistory(@Param('id') id: string): Promise<OrderStatusHistory[]> {
  return this.orderService.getStatusHistory(id);
}

@Get(':id/timeline')
async getOrderTimeline(@Param('id') id: string): Promise<OrderTimeline> {
  return this.orderService.getOrderTimeline(id);
}
```

### 6. Frontend Integration Points
- Real-time status updates via WebSocket
- Progress bar visualization
- Estimated completion time display
- Status history timeline
- Tailor status update interface
