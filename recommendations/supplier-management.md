# Supplier Management Implementation Plan

## Overview
Implement a comprehensive supplier management system to support the tailor purchase portal described in the business process documentation.

## Required Components

### 1. Supplier Module Structure
```
src/apps/supplier/
├── controllers/
│   ├── supplier.controller.ts
│   └── purchase-order.controller.ts
├── dto/
│   ├── create-supplier.dto.ts
│   ├── create-purchase-order.dto.ts
│   └── supplier-catalog.dto.ts
├── entities/
│   ├── supplier.entity.ts
│   └── purchase-order.entity.ts
├── services/
│   ├── supplier.service.ts
│   └── purchase-order.service.ts
└── supplier.module.ts
```

### 2. Database Schema Extensions
```prisma
model Supplier {
  id              String   @id @default(uuid())
  name            String
  contactEmail    String
  contactPhone    String?
  address         String?
  rating          Decimal? @db.Decimal(3,2)
  isActive        Boolean  @default(true)
  specializations String[] // fabric types, materials
  
  products        Product[]
  purchaseOrders  PurchaseOrder[]
  reviews         SupplierReview[]
  
  createdAt       DateTime @default(now())
  updatedAt       DateTime @updatedAt
}

model PurchaseOrder {
  id              String              @id @default(uuid())
  orderNumber     String              @unique
  supplierId      String
  tailorId        String
  status          PurchaseOrderStatus @default(PENDING)
  subtotal        String              // Decimal as string
  tax             String              // Decimal as string
  shipping        String              // Decimal as string
  total           String              // Decimal as string
  
  supplier        Supplier            @relation(fields: [supplierId], references: [id])
  tailor          User                @relation(fields: [tailorId], references: [id])
  items           PurchaseOrderItem[]
  
  orderDate       DateTime            @default(now())
  expectedDate    DateTime?
  deliveredDate   DateTime?
  createdAt       DateTime            @default(now())
  updatedAt       DateTime            @updatedAt
}

enum PurchaseOrderStatus {
  PENDING
  CONFIRMED
  SHIPPED
  DELIVERED
  CANCELLED
}
```

### 3. Key Features to Implement

#### A. Supplier Catalog Management
- Material browsing with filters (type, color, price)
- Real-time inventory tracking
- Sample request functionality
- Bulk pricing tiers

#### B. Purchase Order Processing
- Order creation and confirmation
- Shipping tracking integration
- Delivery notifications
- Partial delivery handling

#### C. Supplier Relationship Management
- Rating and review system
- Performance analytics
- Preferred supplier lists
- Communication tools

### 4. Integration Points

#### A. With Existing Order System
```typescript
// Extend order service to track material usage
class OrderService {
  async trackMaterialUsage(orderId: string, materials: MaterialUsage[]): Promise<void>
  async generateMaterialRequirements(orderId: string): Promise<MaterialRequirement[]>
}
```

#### B. With Inventory Management
```typescript
// New inventory service for material tracking
class InventoryService {
  async updateMaterialStock(materialId: string, quantity: number): Promise<void>
  async checkLowStockAlerts(tailorId: string): Promise<LowStockAlert[]>
  async forecastMaterialNeeds(tailorId: string): Promise<MaterialForecast[]>
}
```

### 5. Implementation Priority
1. **Phase 1**: Basic supplier and purchase order management
2. **Phase 2**: Catalog browsing and ordering
3. **Phase 3**: Advanced analytics and forecasting
4. **Phase 4**: Integration with external supplier APIs

### 6. API Endpoints to Add
```
POST   /suppliers                    # Create supplier
GET    /suppliers                    # List suppliers
GET    /suppliers/:id                # Get supplier details
PUT    /suppliers/:id                # Update supplier
DELETE /suppliers/:id                # Delete supplier

POST   /purchase-orders              # Create purchase order
GET    /purchase-orders              # List purchase orders
GET    /purchase-orders/:id          # Get purchase order
PUT    /purchase-orders/:id/status   # Update order status

GET    /suppliers/:id/catalog        # Browse supplier catalog
POST   /suppliers/:id/samples        # Request samples
GET    /materials/search             # Search materials across suppliers
```
