// Test environment setup
process.env.NODE_ENV = 'test';
process.env.DATABASE_URL = 'postgresql://test:test@localhost:5432/test_db';
process.env.MONGODB_URI = 'mongodb://localhost:27017/test_db';
process.env.REDIS_HOST = 'localhost';
process.env.REDIS_PORT = '6379';
process.env.RABBITMQ_URL = 'amqp://localhost:5672';
process.env.RABBITMQ_EXCHANGE = 'test_exchange';
process.env.JWT_SECRET = 'test_secret';
process.env.JWT_EXPIRATION = '1d';
process.env.I18N_DEFAULT_LANG = 'en';

// Mock console methods to reduce test output noise
const originalConsoleLog = console.log;
const originalConsoleError = console.error;
const originalConsoleWarn = console.warn;

// Only show errors during tests
console.log = jest.fn();
console.warn = jest.fn();
console.error = originalConsoleError; // Keep error logging

// Store original console methods in global scope
global.__originalConsole = {
  log: originalConsoleLog,
  error: originalConsoleError,
  warn: originalConsoleWarn
};

// No need for afterAll here - we'll restore in a separate teardown file
