module.exports = {
  moduleFileExtensions: ['js', 'json', 'ts'],
  rootDir: '.',
  testEnvironment: 'node',
  testRegex: '.e2e-spec.ts$',
  transform: {
    '^.+\\.(t|j)s$': 'ts-jest',
  },
  // Set up module name mapper to support path aliases
  moduleNameMapper: {
    '^@/(.*)$': '<rootDir>/../src/$1',
    '^@core/(.*)$': '<rootDir>/../src/core/$1',
    '^@shared/(.*)$': '<rootDir>/../src/shared/$1',
    '^@infra/(.*)$': '<rootDir>/../src/infra/$1',
    '^@graphql/(.*)$': '<rootDir>/../src/graphql/$1',
    '^@apps/(.*)$': '<rootDir>/../src/apps/$1',
    '^@i18n/(.*)$': '<rootDir>/../src/i18n/$1',
  },
  // Mock environment variables
  setupFiles: ['<rootDir>/setup.js'],
};
