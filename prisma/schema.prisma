// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

// Looking for ways to speed up your queries, or scale easily with your serverless or edge functions?
// Try Prisma Accelerate: https://pris.ly/cli/accelerate-init

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

// User Module Models
model User {
  id            String   @id @default(uuid())
  email         String   @unique
  password      String
  firstName     String
  lastName      String
  roles         String[] // CUSTOMER, TAILOR, ADMIN
  permissions   String[]
  phone         String?
  avatar        String?
  bio           String?
  emailVerified Boolean  @default(false)
  createdAt     DateTime @default(now())
  updatedAt     DateTime @updatedAt
  lastLoginAt   DateTime?
  deviceToken String?

  userPreferences   UserPreference[]
  orders            Order[]
  receivedOrders    Order[]          @relation("TailorOrders")
  transactions      Transaction[]
  invoices          Invoice[]
  measurements      Measurement[]
  tailorProfile     TailorProfile?
  messages          Message[]
  sentMessages      Message[]        @relation("MessageSender")
  trainings         Training[]
  trainingsEnrolled TrainingEnrollment[]
  products          Product[]        @relation("ProductSupplier")
  carts             Cart[]
  portfolio         PortfolioItem[]
  notifications     Notification[]
  verificationTokens VerificationToken[] @relation("UserVerificationTokens")
}

model UserPreference {
  id        String   @id @default(uuid())
  userId    String
  theme     String?
  language  String?
  timezone  String?
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)
}

// Product Module Models
model Product {
  id              String   @id @default(uuid())
  name            String
  description     String
  sku             String   @unique
  price           String // Stored as string to preserve decimal precision
  discountedPrice String?  // Stored as string to preserve decimal precision
  quantity        Int
  category        String?
  tags            String[]
  images          String[]
  isActive        Boolean  @default(true)
  supplierId      String?  // For marketplace supplies
  supplierName    String?  // For marketplace supplies
  isMaterial      Boolean  @default(false) // For tailor supplies/materials
  createdAt       DateTime @default(now())
  updatedAt       DateTime @updatedAt

  orderItems OrderItem[]
  supplier   User?     @relation("ProductSupplier", fields: [supplierId], references: [id])
  carts      Cart[]
}

model ProductCategory {
  id          String   @id @default(uuid())
  name        String   @unique
  description String?
  parentId    String?
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  parent   ProductCategory?  @relation("CategoryToCategory", fields: [parentId], references: [id])
  children ProductCategory[] @relation("CategoryToCategory")
}

// Order Module Models
enum OrderStatus {
  NEW
  APPROVED
  IN_PROGRESS
  COMPLETE
  DELIVERED
  CANCELLED
  RETURNED
}

enum PaymentStatus {
  PENDING
  PAID
  FAILED
  REFUNDED
}

model Order {
  id              String        @id @default(uuid())
  customerId      String
  tailorId        String?       // For tailor orders
  customerName    String?
  customerEmail   String?
  status          OrderStatus   @default(NEW)
  paymentStatus   PaymentStatus @default(PENDING)
  paymentMethod   String?
  subtotal        String // Stored as string to preserve decimal precision
  tax             String // Stored as string to preserve decimal precision
  shipping        String // Stored as string to preserve decimal precision
  discount        String? // Stored as string to preserve decimal precision
  total           String // Stored as string to preserve decimal precision
  notes           String?
  shippingAddress String?
  billingAddress  String?
  trackingNumber  String?
  dueDate         DateTime?     // For tailor orders
  shippedAt       DateTime?
  deliveredAt     DateTime?
  createdAt       DateTime      @default(now())
  updatedAt       DateTime      @updatedAt
  isCustomGarment Boolean       @default(false) // For tailor custom orders

  customer     User          @relation(fields: [customerId], references: [id])
  tailor       User?         @relation("TailorOrders", fields: [tailorId], references: [id])
  items        OrderItem[]
  transactions Transaction[]
  invoices     Invoice[]
  messages     Message[]
}

model OrderItem {
  id          String @id @default(uuid())
  orderId     String
  productId   String
  productName String
  unitPrice   String // Stored as string to preserve decimal precision
  quantity    Int
  totalPrice  String // Stored as string to preserve decimal precision
  discount    String? // Stored as string to preserve decimal precision
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  order   Order   @relation(fields: [orderId], references: [id], onDelete: Cascade)
  product Product @relation(fields: [productId], references: [id])
}

// Finance Module Models
enum TransactionType {
  SALE
  PURCHASE
  REFUND
  EXPENSE
  PAYROLL
  TAX
  OTHER
}

enum TransactionStatus {
  PENDING
  COMPLETED
  FAILED
  VOIDED
}

model Transaction {
  id              String            @id @default(uuid())
  type            TransactionType
  status          TransactionStatus @default(PENDING)
  amount          String // Stored as string to preserve decimal precision
  currency        String            @default("USD")
  description     String
  orderId         String?
  customerId      String?
  invoiceId       String?
  paymentMethod   String?
  referenceNumber String?
  notes           String?
  transactionDate DateTime          @default(now())
  createdAt       DateTime          @default(now())
  updatedAt       DateTime          @updatedAt

  order    Order?  @relation(fields: [orderId], references: [id])
  customer User?   @relation(fields: [customerId], references: [id])
  invoice  Invoice? @relation(fields: [invoiceId], references: [id])
}

model Invoice {
  id            String    @id @default(uuid())
  customerId    String?
  invoiceNumber String    @unique
  subtotal      String // Stored as string to preserve decimal precision
  tax           String // Stored as string to preserve decimal precision
  discount      String? // Stored as string to preserve decimal precision
  total         String // Stored as string to preserve decimal precision
  status        String    @default("PENDING")
  orderId       String?
  issueDate     DateTime
  dueDate       DateTime
  paidDate      DateTime?
  createdAt     DateTime  @default(now())
  updatedAt     DateTime  @updatedAt

  customer     User?         @relation(fields: [customerId], references: [id])
  order        Order?        @relation(fields: [orderId], references: [id])
  transactions Transaction[]
}

// TailorLink Specific Models
model TailorProfile {
  id              String   @id @default(uuid())
  userId          String   @unique
  bio             String?
  certificates    String[] // Array of certificate URLs
  experienceYears Int?
  specialties     String[] // Array of specialties
  rating          Float?   @default(0)
  reviewCount     Int      @default(0)
  isVerified      Boolean  @default(false)
  createdAt       DateTime @default(now())
  updatedAt       DateTime @updatedAt

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)
}

model PortfolioItem {
  id          String   @id @default(uuid())
  userId      String
  title       String
  description String?
  imageUrl    String
  category    String?
  tags        String[]
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)
}

model Measurement {
  id             String   @id @default(uuid())
  userId         String
  height         Float?
  weight         Float?
  chest          Float?
  waist          Float?
  hips           Float?
  shoulderWidth  Float?
  neck           Float?
  armLength      Float?
  legLength      Float?
  thigh          Float?
  calf           Float?
  footSize       Float?
  measurementUnit String   @default("cm") // cm or inch
  notes          String?
  createdAt      DateTime @default(now())
  updatedAt      DateTime @updatedAt

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)
}

model Message {
  id        String   @id @default(uuid())
  orderId   String?
  senderId  String
  receiverId String
  content   String
  isRead    Boolean  @default(false)
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  order    Order? @relation(fields: [orderId], references: [id])
  sender   User   @relation("MessageSender", fields: [senderId], references: [id])
  receiver User   @relation(fields: [receiverId], references: [id])
}

model Training {
  id           String   @id @default(uuid())
  title        String
  description  String
  instructorId String
  contentType  String // video, pdf, etc.
  contentUrl   String
  price        String? // Stored as string to preserve decimal precision
  accessLevel  String   @default("free") // free, premium
  duration     Int? // in minutes
  createdAt    DateTime @default(now())
  updatedAt    DateTime @updatedAt

  instructor User                 @relation(fields: [instructorId], references: [id])
  enrollments TrainingEnrollment[]
}

model TrainingEnrollment {
  id         String   @id @default(uuid())
  userId     String
  trainingId String
  enrolledAt DateTime @default(now())
  completedAt DateTime?
  progress   Int      @default(0) // percentage of completion

  user     User     @relation(fields: [userId], references: [id])
  training Training @relation(fields: [trainingId], references: [id])

  @@unique([userId, trainingId])
}

model Cart {
  id        String   @id @default(uuid())
  userId    String
  productId String
  quantity  Int
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  user    User    @relation(fields: [userId], references: [id])
  product Product @relation(fields: [productId], references: [id])

  @@unique([userId, productId])
}

model Notification {
  id        String   @id @default(uuid())
  userId    String
  title     String
  body      String
  isRead    Boolean  @default(false)
  type      String?  // order_update, message, etc.
  data      Json?    // Additional data related to the notification
  createdAt DateTime @default(now())

  user User @relation(fields: [userId], references: [id])
}

// Auth Module Models
model VerificationToken {
  id        String   @id @default(uuid())
  userId    String
  token     String   @unique
  type      String   // email_verification, password_reset, etc.
  expiresAt DateTime
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  user User @relation("UserVerificationTokens", fields: [userId], references: [id], onDelete: Cascade)
}
