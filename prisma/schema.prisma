// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

// Looking for ways to speed up your queries, or scale easily with your serverless or edge functions?
// Try Prisma Accelerate: https://pris.ly/cli/accelerate-init

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

// User Module Models
model User {
  id            String   @id @default(uuid())
  email         String   @unique
  password      String
  firstName     String
  lastName      String
  roles         String[] // CUSTOMER, TAILOR, ADMIN
  permissions   String[]
  phone         String?
  avatar        String?
  bio           String?
  emailVerified Boolean  @default(false)
  createdAt     DateTime @default(now())
  updatedAt     DateTime @updatedAt
  lastLoginAt   DateTime?
  deviceToken String?

  userPreferences   UserPreference[]
  orders            Order[]
  receivedOrders    Order[]          @relation("TailorOrders")
  transactions      Transaction[]
  invoices          Invoice[]
  measurements      Measurement[]
  tailorProfile     TailorProfile?
  messages          Message[]
  sentMessages      Message[]        @relation("MessageSender")
  trainings         Training[]
  trainingsEnrolled TrainingEnrollment[]
  products          Product[]        @relation("ProductSupplier")
  carts             Cart[]
  portfolio         PortfolioItem[]
  notifications     Notification[]
  verificationTokens VerificationToken[] @relation("UserVerificationTokens")
  customerEscrows   EscrowTransaction[] @relation("CustomerEscrows")
  tailorEscrows     EscrowTransaction[] @relation("TailorEscrows")
  escrowEvents      EscrowEvent[]
  orderStatusUpdates OrderStatusHistory[] @relation("OrderStatusUpdates")

  // Financial system relations
  wallet            Wallet?
  creditProfile     CreditProfile?
  loanApplications  LoanApplication[]
  loans             Loan[]
  installmentPlans  InstallmentPlan[]
  paymentReminders  PaymentReminder[]

  // Supply chain relations
  supplierRatings   SupplierRating[]
  purchaseOrders    PurchaseOrder[]
  purchaseOrderApprovals PurchaseOrderApproval[]
  approvedPurchaseOrders PurchaseOrder[] @relation("PurchaseOrderApprovals")
  receivedDeliveries PurchaseOrderDelivery[] @relation("DeliveryReceiver")
  qualityChecks     PurchaseOrderDelivery[] @relation("QualityChecker")
}

model UserPreference {
  id        String   @id @default(uuid())
  userId    String
  theme     String?
  language  String?
  timezone  String?
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)
}

// Product Module Models
model Product {
  id              String   @id @default(uuid())
  name            String
  description     String
  sku             String   @unique
  price           String // Stored as string to preserve decimal precision
  discountedPrice String?  // Stored as string to preserve decimal precision
  quantity        Int
  category        String?
  tags            String[]
  images          String[]
  isActive        Boolean  @default(true)
  supplierId      String?  // For marketplace supplies
  supplierName    String?  // For marketplace supplies
  isMaterial      Boolean  @default(false) // For tailor supplies/materials
  createdAt       DateTime @default(now())
  updatedAt       DateTime @updatedAt

  orderItems OrderItem[]
  supplier   User?     @relation("ProductSupplier", fields: [supplierId], references: [id])
  carts      Cart[]
}

model ProductCategory {
  id          String   @id @default(uuid())
  name        String   @unique
  description String?
  parentId    String?
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  parent   ProductCategory?  @relation("CategoryToCategory", fields: [parentId], references: [id])
  children ProductCategory[] @relation("CategoryToCategory")
}

// Order Module Models
enum OrderStatus {
  NEW
  APPROVED
  IN_PROGRESS
  COMPLETE
  DELIVERED
  CANCELLED
  RETURNED
}

enum DetailedOrderStatus {
  // Initial stages
  NEW
  ORDER_RECEIVED

  // Preparation stages
  MEASUREMENTS_CONFIRMED
  MATERIALS_ACQUIRED

  // Production stages
  CUTTING_STARTED
  STITCHING_IN_PROGRESS
  FINISHING_TOUCHES

  // Quality and completion
  QUALITY_CHECK
  READY_FOR_DELIVERY

  // Final stages
  DELIVERED
  COMPLETED

  // Exception cases
  CANCELLED
  RETURNED
}

enum PaymentStatus {
  PENDING
  PAID
  FAILED
  REFUNDED
}

model Order {
  id              String        @id @default(uuid())
  customerId      String
  tailorId        String?       // For tailor orders
  customerName    String?
  customerEmail   String?
  status          OrderStatus   @default(NEW)
  detailedStatus  DetailedOrderStatus @default(NEW)
  paymentStatus   PaymentStatus @default(PENDING)
  paymentMethod   String?
  subtotal        String // Stored as string to preserve decimal precision
  tax             String // Stored as string to preserve decimal precision
  shipping        String // Stored as string to preserve decimal precision
  discount        String? // Stored as string to preserve decimal precision
  total           String // Stored as string to preserve decimal precision
  notes           String?
  shippingAddress String?
  billingAddress  String?
  trackingNumber  String?
  dueDate         DateTime?     // For tailor orders

  // Enhanced tracking timestamps
  estimatedCompletion     DateTime?
  actualCompletion        DateTime?
  measurementsConfirmedAt DateTime?
  materialsAcquiredAt     DateTime?
  cuttingStartedAt        DateTime?
  stitchingStartedAt      DateTime?
  finishingStartedAt      DateTime?
  qualityCheckAt          DateTime?
  readyForDeliveryAt      DateTime?

  shippedAt       DateTime?
  deliveredAt     DateTime?
  createdAt       DateTime      @default(now())
  updatedAt       DateTime      @updatedAt
  isCustomGarment Boolean       @default(false) // For tailor custom orders

  customer     User          @relation(fields: [customerId], references: [id])
  tailor       User?         @relation("TailorOrders", fields: [tailorId], references: [id])
  items        OrderItem[]
  transactions Transaction[]
  invoices     Invoice[]
  messages     Message[]
  escrowTransaction EscrowTransaction?
  statusHistory OrderStatusHistory[]
  installmentPlan InstallmentPlan?
}

model OrderItem {
  id          String @id @default(uuid())
  orderId     String
  productId   String
  productName String
  unitPrice   String // Stored as string to preserve decimal precision
  quantity    Int
  totalPrice  String // Stored as string to preserve decimal precision
  discount    String? // Stored as string to preserve decimal precision
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  order   Order   @relation(fields: [orderId], references: [id], onDelete: Cascade)
  product Product @relation(fields: [productId], references: [id])
}

model OrderStatusHistory {
  id          String              @id @default(uuid())
  orderId     String
  status      DetailedOrderStatus
  notes       String?
  updatedBy   String              // User ID who made the change
  timestamp   DateTime            @default(now())

  order       Order               @relation(fields: [orderId], references: [id])
  updater     User                @relation("OrderStatusUpdates", fields: [updatedBy], references: [id])

  @@map("order_status_history")
}

// Finance Module Models
enum TransactionType {
  SALE
  PURCHASE
  REFUND
  EXPENSE
  PAYROLL
  TAX
  OTHER
}

enum TransactionStatus {
  PENDING
  COMPLETED
  FAILED
  VOIDED
}

model Transaction {
  id              String            @id @default(uuid())
  type            TransactionType
  status          TransactionStatus @default(PENDING)
  amount          String // Stored as string to preserve decimal precision
  currency        String            @default("USD")
  description     String
  orderId         String?
  customerId      String?
  invoiceId       String?
  paymentMethod   String?
  referenceNumber String?
  notes           String?
  transactionDate DateTime          @default(now())
  createdAt       DateTime          @default(now())
  updatedAt       DateTime          @updatedAt

  order    Order?  @relation(fields: [orderId], references: [id])
  customer User?   @relation(fields: [customerId], references: [id])
  invoice  Invoice? @relation(fields: [invoiceId], references: [id])
}

model Invoice {
  id            String    @id @default(uuid())
  customerId    String?
  invoiceNumber String    @unique
  subtotal      String // Stored as string to preserve decimal precision
  tax           String // Stored as string to preserve decimal precision
  discount      String? // Stored as string to preserve decimal precision
  total         String // Stored as string to preserve decimal precision
  status        String    @default("PENDING")
  orderId       String?
  issueDate     DateTime
  dueDate       DateTime
  paidDate      DateTime?
  createdAt     DateTime  @default(now())
  updatedAt     DateTime  @updatedAt

  customer     User?         @relation(fields: [customerId], references: [id])
  order        Order?        @relation(fields: [orderId], references: [id])
  transactions Transaction[]
}

model EscrowTransaction {
  id                String              @id @default(uuid())
  orderId           String              @unique
  customerId        String
  tailorId          String?
  amount            String              // Total amount held in escrow
  platformFee       String              // Platform fee amount
  tailorAmount      String              // Amount to be released to tailor
  status            EscrowStatus        @default(HELD)
  holdReason        String?
  releaseConditions String[]            // Conditions that must be met for release

  // Payment details
  paymentMethod     String
  paymentReference  String?
  gatewayTransactionId String?

  // Timestamps
  heldAt            DateTime            @default(now())
  releasedAt        DateTime?
  refundedAt        DateTime?
  expiresAt         DateTime?           // Auto-release date if no action taken

  // Relations
  order             Order               @relation(fields: [orderId], references: [id])
  customer          User                @relation("CustomerEscrows", fields: [customerId], references: [id])
  tailor            User?               @relation("TailorEscrows", fields: [tailorId], references: [id])
  escrowEvents      EscrowEvent[]

  createdAt         DateTime            @default(now())
  updatedAt         DateTime            @updatedAt

  @@map("escrow_transactions")
}

model EscrowEvent {
  id                String              @id @default(uuid())
  escrowTransactionId String
  eventType         EscrowEventType
  description       String
  amount            String?             // Amount involved in this event
  triggeredBy       String?             // User ID who triggered the event
  metadata          Json?               // Additional event data
  timestamp         DateTime            @default(now())

  // Relations
  escrowTransaction EscrowTransaction   @relation(fields: [escrowTransactionId], references: [id])
  triggeredByUser   User?               @relation(fields: [triggeredBy], references: [id])

  @@map("escrow_events")
}

// Digital Wallet System Models
model Wallet {
  id              String              @id @default(uuid())
  userId          String              @unique
  balance         String              @default("0.00") // Primary currency balance
  currency        String              @default("USD")
  status          WalletStatus        @default(ACTIVE)

  // Security and limits
  dailyLimit      String?             // Daily transaction limit
  monthlyLimit    String?             // Monthly transaction limit
  isVerified      Boolean             @default(false)

  // Multi-currency balances (JSON object)
  currencyBalances Json?              // {"USD": "100.00", "EUR": "85.50"}

  // Metadata
  lastActivity    DateTime?
  createdAt       DateTime            @default(now())
  updatedAt       DateTime            @updatedAt

  // Relations
  user            User                @relation(fields: [userId], references: [id])
  transactions    WalletTransaction[]
  sentTransfers   WalletTransfer[]    @relation("SenderWallet")
  receivedTransfers WalletTransfer[]  @relation("ReceiverWallet")

  @@map("wallets")
}

model WalletTransaction {
  id              String                    @id @default(uuid())
  walletId        String
  type            WalletTransactionType
  status          WalletTransactionStatus   @default(PENDING)
  amount          String                    // Transaction amount
  currency        String                    @default("USD")

  // Balance tracking
  balanceBefore   String                    // Balance before transaction
  balanceAfter    String                    // Balance after transaction

  // Transaction details
  description     String
  reference       String?                   // External reference (order ID, transfer ID, etc.)
  metadata        Json?                     // Additional transaction data

  // Fee information
  feeAmount       String?                   // Fee charged for transaction
  feeDescription  String?                   // Fee description

  // External integration
  gatewayTransactionId String?              // Payment gateway transaction ID
  gatewayResponse Json?                     // Gateway response data

  // Timestamps
  processedAt     DateTime?
  createdAt       DateTime                  @default(now())
  updatedAt       DateTime                  @updatedAt

  // Relations
  wallet          Wallet                    @relation(fields: [walletId], references: [id])

  @@map("wallet_transactions")
}

model WalletTransfer {
  id              String                    @id @default(uuid())
  senderWalletId  String
  receiverWalletId String
  amount          String
  currency        String                    @default("USD")

  // Transfer details
  description     String?
  reference       String?                   // Order ID or other reference
  status          WalletTransactionStatus   @default(PENDING)

  // Fee information
  transferFee     String?                   // Fee for the transfer
  feePayerId      String?                   // Who pays the fee (sender/receiver)

  // Exchange rate (for multi-currency transfers)
  exchangeRate    String?                   // Exchange rate used
  convertedAmount String?                   // Amount in receiver's currency

  // Timestamps
  initiatedAt     DateTime                  @default(now())
  completedAt     DateTime?
  createdAt       DateTime                  @default(now())
  updatedAt       DateTime                  @updatedAt

  // Relations
  senderWallet    Wallet                    @relation("SenderWallet", fields: [senderWalletId], references: [id])
  receiverWallet  Wallet                    @relation("ReceiverWallet", fields: [receiverWalletId], references: [id])

  @@map("wallet_transfers")
}

model ExchangeRate {
  id              String                    @id @default(uuid())
  fromCurrency    String
  toCurrency      String
  rate            String                    // Exchange rate
  source          String                    // Rate source (API provider)

  // Timestamps
  effectiveAt     DateTime                  @default(now())
  createdAt       DateTime                  @default(now())

  @@unique([fromCurrency, toCurrency, effectiveAt])
  @@map("exchange_rates")
}

// Loan/Credit System Models
model CreditProfile {
  id              String                    @id @default(uuid())
  userId          String                    @unique

  // Credit scoring
  creditScore     Int?                      // Numerical credit score
  creditGrade     CreditScoreGrade?         // Grade based on score
  lastUpdated     DateTime?                 // Last credit score update

  // Credit limits
  totalCreditLimit String                   @default("0.00")
  availableCredit String                    @default("0.00")
  usedCredit      String                    @default("0.00")

  // Risk assessment
  riskLevel       String?                   // LOW, MEDIUM, HIGH
  riskFactors     String[]                  // Array of risk factors

  // Income verification
  monthlyIncome   String?
  incomeVerified  Boolean                   @default(false)
  employmentStatus String?                  // EMPLOYED, SELF_EMPLOYED, UNEMPLOYED

  // Credit history
  paymentHistory  Json?                     // Payment history data
  defaultHistory  Json?                     // Default/late payment history

  // Timestamps
  createdAt       DateTime                  @default(now())
  updatedAt       DateTime                  @updatedAt

  // Relations
  user            User                      @relation(fields: [userId], references: [id])
  loanApplications LoanApplication[]
  loans           Loan[]

  @@map("credit_profiles")
}

model LoanApplication {
  id              String                    @id @default(uuid())
  userId          String
  creditProfileId String

  // Loan details
  loanType        LoanType
  requestedAmount String
  currency        String                    @default("USD")
  purpose         String                    // Loan purpose description
  termMonths      Int                       // Loan term in months

  // Application status
  status          LoanStatus                @default(DRAFT)
  submittedAt     DateTime?
  reviewedAt      DateTime?
  reviewedBy      String?                   // Admin user ID

  // Decision details
  approvedAmount  String?                   // Approved amount (may differ from requested)
  interestRate    String?                   // Annual interest rate
  rejectionReason String?                   // Reason for rejection

  // Supporting documents
  documents       Json?                     // Array of document URLs

  // Risk assessment
  riskAssessment  Json?                     // Risk assessment data

  // Timestamps
  createdAt       DateTime                  @default(now())
  updatedAt       DateTime                  @updatedAt

  // Relations
  user            User                      @relation(fields: [userId], references: [id])
  creditProfile   CreditProfile             @relation(fields: [creditProfileId], references: [id])
  loan            Loan?                     // Created loan if approved

  @@map("loan_applications")
}

model Loan {
  id              String                    @id @default(uuid())
  userId          String
  creditProfileId String
  applicationId   String?                   @unique

  // Loan terms
  loanType        LoanType
  principalAmount String                    // Original loan amount
  currency        String                    @default("USD")
  interestRate    String                    // Annual interest rate
  termMonths      Int                       // Loan term in months

  // Current status
  status          LoanStatus                @default(ACTIVE)
  currentBalance  String                    // Outstanding balance
  totalPaid       String                    @default("0.00")

  // Payment schedule
  monthlyPayment  String                    // Monthly payment amount
  nextPaymentDate DateTime?                 // Next payment due date
  lastPaymentDate DateTime?                 // Last payment received date

  // Fees and penalties
  lateFeeRate     String                    @default("0.05") // 5% late fee
  totalLateFees   String                    @default("0.00")
  totalInterestPaid String                  @default("0.00")

  // Loan lifecycle dates
  disbursedAt     DateTime?                 // When loan was disbursed
  maturityDate    DateTime?                 // Loan maturity date
  closedAt        DateTime?                 // When loan was closed

  // Default tracking
  daysOverdue     Int                       @default(0)
  defaultedAt     DateTime?

  // Timestamps
  createdAt       DateTime                  @default(now())
  updatedAt       DateTime                  @updatedAt

  // Relations
  user            User                      @relation(fields: [userId], references: [id])
  creditProfile   CreditProfile             @relation(fields: [creditProfileId], references: [id])
  application     LoanApplication?          @relation(fields: [applicationId], references: [id])
  payments        LoanPayment[]

  @@map("loans")
}

model LoanPayment {
  id              String                    @id @default(uuid())
  loanId          String

  // Payment details
  amount          String                    // Payment amount
  currency        String                    @default("USD")
  principalAmount String                    // Principal portion
  interestAmount  String                    // Interest portion
  lateFeeAmount   String                    @default("0.00") // Late fee portion

  // Payment tracking
  dueDate         DateTime                  // Original due date
  paidDate        DateTime?                 // Actual payment date
  status          String                    @default("PENDING") // PENDING, PAID, FAILED, OVERDUE

  // Payment method
  paymentMethod   String?                   // WALLET, BANK_TRANSFER, etc.
  transactionId   String?                   // Reference to wallet transaction

  // Balance tracking
  balanceBefore   String                    // Loan balance before payment
  balanceAfter    String                    // Loan balance after payment

  // Timestamps
  createdAt       DateTime                  @default(now())
  updatedAt       DateTime                  @updatedAt

  // Relations
  loan            Loan                      @relation(fields: [loanId], references: [id])

  @@map("loan_payments")
}

// Installment/Divided Payment System Models
model InstallmentPlan {
  id              String                    @id @default(uuid())
  orderId         String                    @unique
  userId          String                    // Customer who created the plan

  // Plan details
  totalAmount     String                    // Total order amount
  currency        String                    @default("USD")
  numberOfInstallments Int                  // Number of installments (2, 3, 6, 12)
  installmentAmount String                  // Amount per installment

  // Plan configuration
  status          InstallmentPlanStatus     @default(ACTIVE)
  interestRate    String                    @default("0.00") // Annual interest rate
  processingFee   String                    @default("0.00") // One-time processing fee

  // Payment schedule
  firstPaymentDate DateTime                 // First installment due date
  paymentFrequency String                   @default("MONTHLY") // WEEKLY, MONTHLY

  // Discount/penalty settings
  earlyPaymentDiscount String               @default("0.00") // Discount for early payment
  lateFeeRate     String                    @default("0.05") // Late fee percentage
  gracePeriodDays Int                       @default(3)      // Grace period before late fee

  // Plan lifecycle
  createdAt       DateTime                  @default(now())
  activatedAt     DateTime?                 // When plan was activated
  completedAt     DateTime?                 // When all installments paid
  cancelledAt     DateTime?                 // When plan was cancelled

  // Totals tracking
  totalPaid       String                    @default("0.00")
  totalLateFees   String                    @default("0.00")
  totalInterest   String                    @default("0.00")

  // Relations
  order           Order                     @relation(fields: [orderId], references: [id])
  user            User                      @relation(fields: [userId], references: [id])
  installments    Installment[]

  @@map("installment_plans")
}

model Installment {
  id              String                    @id @default(uuid())
  planId          String
  installmentNumber Int                     // 1, 2, 3, etc.

  // Payment details
  amount          String                    // Installment amount
  currency        String                    @default("USD")
  principalAmount String                    // Principal portion
  interestAmount  String                    @default("0.00") // Interest portion
  lateFeeAmount   String                    @default("0.00") // Late fee amount

  // Payment schedule
  dueDate         DateTime                  // When payment is due
  paidDate        DateTime?                 // When payment was made
  status          InstallmentStatus         @default(PENDING)

  // Payment tracking
  paymentMethod   String?                   // WALLET, ESCROW, BANK_TRANSFER
  transactionId   String?                   // Reference to payment transaction
  escrowReleaseId String?                   // Reference to escrow release

  // Reminder tracking
  remindersSent   Int                       @default(0)
  lastReminderAt  DateTime?

  // Timestamps
  createdAt       DateTime                  @default(now())
  updatedAt       DateTime                  @updatedAt

  // Relations
  plan            InstallmentPlan           @relation(fields: [planId], references: [id])

  @@map("installments")
}

model PaymentReminder {
  id              String                    @id @default(uuid())
  userId          String
  installmentId   String?                   // For installment reminders
  loanPaymentId   String?                   // For loan payment reminders

  // Reminder details
  type            String                    // INSTALLMENT, LOAN_PAYMENT, WALLET_LOW_BALANCE
  title           String                    // Reminder title
  message         String                    // Reminder message
  amount          String?                   // Amount due
  dueDate         DateTime?                 // Payment due date

  // Reminder status
  status          String                    @default("PENDING") // PENDING, SENT, ACKNOWLEDGED
  sentAt          DateTime?
  acknowledgedAt  DateTime?

  // Delivery channels
  emailSent       Boolean                   @default(false)
  smsSent         Boolean                   @default(false)
  pushSent        Boolean                   @default(false)
  inAppSent       Boolean                   @default(false)

  // Timestamps
  createdAt       DateTime                  @default(now())
  updatedAt       DateTime                  @updatedAt

  // Relations
  user            User                      @relation(fields: [userId], references: [id])

  @@map("payment_reminders")
}

enum EscrowStatus {
  HELD              // Payment is held in escrow
  RELEASED          // Payment released to tailor
  REFUNDED          // Payment refunded to customer
  DISPUTED          // Payment is under dispute
  EXPIRED           // Escrow expired, needs manual review
  PARTIALLY_RELEASED // Partial payment released
}

enum EscrowEventType {
  PAYMENT_HELD      // Initial payment held
  RELEASE_REQUESTED // Release requested by system/user
  RELEASED          // Payment released to tailor
  REFUND_REQUESTED  // Refund requested
  REFUNDED          // Payment refunded to customer
  DISPUTE_RAISED    // Dispute raised
  DISPUTE_RESOLVED  // Dispute resolved
  EXPIRED           // Escrow expired
  MANUAL_REVIEW     // Requires manual review
}

// Digital Wallet System Enums
enum WalletStatus {
  ACTIVE
  SUSPENDED
  FROZEN
  CLOSED
}

enum WalletTransactionType {
  DEPOSIT           // Money added to wallet
  WITHDRAWAL        // Money withdrawn from wallet
  TRANSFER_OUT      // Transfer to another wallet
  TRANSFER_IN       // Received from another wallet
  PAYMENT           // Payment for order/service
  REFUND            // Refund received
  FEE               // Platform fee deduction
  INTEREST          // Interest earned/charged
  LOAN_DISBURSEMENT // Loan amount credited
  LOAN_REPAYMENT    // Loan repayment deducted
  INSTALLMENT       // Installment payment
  PENALTY           // Late payment penalty
  BONUS             // Promotional bonus
  CASHBACK          // Cashback reward
}

enum WalletTransactionStatus {
  PENDING
  COMPLETED
  FAILED
  CANCELLED
  REVERSED
}

// Loan/Credit System Enums
enum LoanStatus {
  DRAFT             // Application being prepared
  SUBMITTED         // Application submitted
  UNDER_REVIEW      // Being reviewed
  APPROVED          // Loan approved
  REJECTED          // Loan rejected
  ACTIVE            // Loan active/disbursed
  COMPLETED         // Loan fully repaid
  DEFAULTED         // Loan in default
  WRITTEN_OFF       // Bad debt written off
}

enum LoanType {
  PERSONAL          // Personal loan
  BUSINESS          // Business loan for tailors
  ORDER_FINANCING   // Financing for specific orders
  EQUIPMENT         // Equipment financing
  WORKING_CAPITAL   // Working capital loan
}

enum CreditScoreGrade {
  EXCELLENT         // 750+
  GOOD              // 650-749
  FAIR              // 550-649
  POOR              // 350-549
  NO_CREDIT         // No credit history
}

// Installment System Enums
enum InstallmentPlanStatus {
  ACTIVE
  COMPLETED
  DEFAULTED
  CANCELLED
}

enum InstallmentStatus {
  PENDING
  PAID
  OVERDUE
  FAILED
  WAIVED
}

// TailorLink Specific Models
model TailorProfile {
  id              String   @id @default(uuid())
  userId          String   @unique
  bio             String?
  certificates    String[] // Array of certificate URLs
  experienceYears Int?
  specialties     String[] // Array of specialties
  rating          Float?   @default(0)
  reviewCount     Int      @default(0)
  isVerified      Boolean  @default(false)
  createdAt       DateTime @default(now())
  updatedAt       DateTime @updatedAt

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)
}

model PortfolioItem {
  id          String   @id @default(uuid())
  userId      String
  title       String
  description String?
  imageUrl    String
  category    String?
  tags        String[]
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)
}

model Measurement {
  id             String   @id @default(uuid())
  userId         String
  height         Float?
  weight         Float?
  chest          Float?
  waist          Float?
  hips           Float?
  shoulderWidth  Float?
  neck           Float?
  armLength      Float?
  legLength      Float?
  thigh          Float?
  calf           Float?
  footSize       Float?
  measurementUnit String   @default("cm") // cm or inch
  notes          String?
  createdAt      DateTime @default(now())
  updatedAt      DateTime @updatedAt

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)
}

model Message {
  id        String   @id @default(uuid())
  orderId   String?
  senderId  String
  receiverId String
  content   String
  isRead    Boolean  @default(false)
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  order    Order? @relation(fields: [orderId], references: [id])
  sender   User   @relation("MessageSender", fields: [senderId], references: [id])
  receiver User   @relation(fields: [receiverId], references: [id])
}

model Training {
  id           String   @id @default(uuid())
  title        String
  description  String
  instructorId String
  contentType  String // video, pdf, etc.
  contentUrl   String
  price        String? // Stored as string to preserve decimal precision
  accessLevel  String   @default("free") // free, premium
  duration     Int? // in minutes
  createdAt    DateTime @default(now())
  updatedAt    DateTime @updatedAt

  instructor User                 @relation(fields: [instructorId], references: [id])
  enrollments TrainingEnrollment[]
}

model TrainingEnrollment {
  id         String   @id @default(uuid())
  userId     String
  trainingId String
  enrolledAt DateTime @default(now())
  completedAt DateTime?
  progress   Int      @default(0) // percentage of completion

  user     User     @relation(fields: [userId], references: [id])
  training Training @relation(fields: [trainingId], references: [id])

  @@unique([userId, trainingId])
}

model Cart {
  id        String   @id @default(uuid())
  userId    String
  productId String
  quantity  Int
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  user    User    @relation(fields: [userId], references: [id])
  product Product @relation(fields: [productId], references: [id])

  @@unique([userId, productId])
}

model Notification {
  id        String   @id @default(uuid())
  userId    String
  title     String
  body      String
  isRead    Boolean  @default(false)
  type      String?  // order_update, message, etc.
  data      Json?    // Additional data related to the notification
  createdAt DateTime @default(now())

  user User @relation(fields: [userId], references: [id])
}

// Auth Module Models
model VerificationToken {
  id        String   @id @default(uuid())
  userId    String
  token     String   @unique
  type      String   // email_verification, password_reset, etc.
  expiresAt DateTime
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  user User @relation("UserVerificationTokens", fields: [userId], references: [id], onDelete: Cascade)
}

// ================================
// SUPPLY CHAIN MANAGEMENT MODELS
// ================================

// Supplier Management Models
model Supplier {
  id                    String   @id @default(uuid())
  companyName           String
  contactPerson         String
  email                 String   @unique
  phone                 String?
  address               String?
  city                  String?
  state                 String?
  country               String?
  postalCode            String?
  website               String?
  taxId                 String?
  businessLicense       String?

  // Business Information
  businessType          String   // MANUFACTURER, DISTRIBUTOR, WHOLESALER, RETAILER
  specializations       String[] // Array of specialization categories
  certifications        String[] // Quality certifications, compliance certificates
  paymentTerms          String?  // NET_30, NET_60, COD, etc.
  creditLimit           String?  @db.Text // Stored as string for decimal precision
  currency              String   @default("USD")

  // Status and Verification
  status                SupplierStatus @default(PENDING)
  verificationStatus    VerificationStatus @default(UNVERIFIED)
  verifiedAt            DateTime?
  verifiedBy            String?

  // Performance Metrics
  overallRating         String?  @db.Text // Stored as string for decimal precision
  totalOrders           Int      @default(0)
  totalOrderValue       String   @default("0.00") @db.Text
  onTimeDeliveryRate    String?  @db.Text // Percentage as string
  qualityRating         String?  @db.Text

  // Relationships
  contacts              SupplierContact[]
  catalogs              SupplierCatalog[]
  ratings               SupplierRating[]
  performanceMetrics    SupplierPerformanceMetric[]
  purchaseOrders        PurchaseOrder[]
  inventoryItems        InventoryItem[]
  supplyChainMetrics    SupplyChainMetric[]

  // Audit fields
  createdAt             DateTime @default(now())
  updatedAt             DateTime @updatedAt
  createdBy             String?
  updatedBy             String?

  @@map("suppliers")
}

model SupplierContact {
  id          String   @id @default(uuid())
  supplierId  String
  name        String
  title       String?
  email       String
  phone       String?
  department  String?  // SALES, SUPPORT, FINANCE, LOGISTICS
  isPrimary   Boolean  @default(false)
  isActive    Boolean  @default(true)

  // Relationships
  supplier    Supplier @relation(fields: [supplierId], references: [id], onDelete: Cascade)

  // Audit fields
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  @@map("supplier_contacts")
}

model SupplierCatalog {
  id                String   @id @default(uuid())
  supplierId        String
  itemName          String
  itemCode          String?
  description       String?
  category          String
  subcategory       String?

  // Pricing and Availability
  unitPrice         String   @db.Text // Stored as string for decimal precision
  currency          String   @default("USD")
  minimumOrderQty   Int      @default(1)
  availableQuantity Int?
  leadTimeDays      Int?

  // Product Details
  specifications    Json?    // Flexible product specifications
  images            String[] // Array of image URLs
  documents         String[] // Array of document URLs (specs, certificates)

  // Status
  isActive          Boolean  @default(true)
  isDiscontinued    Boolean  @default(false)

  // Relationships
  supplier          Supplier @relation(fields: [supplierId], references: [id], onDelete: Cascade)
  purchaseOrderItems PurchaseOrderItem[]
  inventoryItems    InventoryItem[]

  // Audit fields
  createdAt         DateTime @default(now())
  updatedAt         DateTime @updatedAt

  @@unique([supplierId, itemCode])
  @@map("supplier_catalogs")
}

model SupplierRating {
  id          String   @id @default(uuid())
  supplierId  String
  ratedBy     String   // User ID who provided the rating
  orderId     String?  // Purchase order ID if rating is order-specific

  // Rating Categories
  overallRating     String @db.Text // 1.00 to 5.00 as string
  qualityRating     String @db.Text
  deliveryRating    String @db.Text
  serviceRating     String @db.Text
  valueRating       String @db.Text

  // Review Details
  title       String?
  comment     String?
  pros        String[]
  cons        String[]

  // Status
  isVerified  Boolean  @default(false)
  isPublic    Boolean  @default(true)

  // Relationships
  supplier    Supplier @relation(fields: [supplierId], references: [id], onDelete: Cascade)
  rater       User     @relation(fields: [ratedBy], references: [id])

  // Audit fields
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  @@map("supplier_ratings")
}

model SupplierPerformanceMetric {
  id                    String   @id @default(uuid())
  supplierId            String
  metricPeriod          String   // MONTHLY, QUARTERLY, YEARLY
  periodStart           DateTime
  periodEnd             DateTime

  // Performance Metrics
  totalOrders           Int      @default(0)
  totalOrderValue       String   @default("0.00") @db.Text
  onTimeDeliveries      Int      @default(0)
  lateDeliveries        Int      @default(0)
  cancelledOrders       Int      @default(0)
  qualityIssues         Int      @default(0)

  // Calculated Metrics
  onTimeDeliveryRate    String?  @db.Text
  averageLeadTime       String?  @db.Text
  defectRate            String?  @db.Text
  fillRate              String?  @db.Text

  // Relationships
  supplier              Supplier @relation(fields: [supplierId], references: [id], onDelete: Cascade)

  // Audit fields
  createdAt             DateTime @default(now())
  updatedAt             DateTime @updatedAt

  @@unique([supplierId, metricPeriod, periodStart])
  @@map("supplier_performance_metrics")
}

// Inventory Management Models
model InventoryCategory {
  id          String   @id @default(uuid())
  name        String   @unique
  description String?
  parentId    String?
  isActive    Boolean  @default(true)

  // Category hierarchy
  parent      InventoryCategory?  @relation("CategoryHierarchy", fields: [parentId], references: [id])
  children    InventoryCategory[] @relation("CategoryHierarchy")

  // Relationships
  inventoryItems InventoryItem[]
  supplyChainMetrics SupplyChainMetric[]

  // Audit fields
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  @@map("inventory_categories")
}

model InventoryItem {
  id                String   @id @default(uuid())
  itemName          String
  itemCode          String   @unique
  description       String?
  categoryId        String
  supplierId        String?
  supplierCatalogId String?

  // Item Details
  itemType          InventoryItemType // FABRIC, THREAD, ACCESSORY, TOOL, EQUIPMENT
  unit              String            // METER, YARD, PIECE, KG, etc.
  specifications    Json?             // Color, material, size, etc.

  // Stock Information
  currentStock      Int      @default(0)
  reservedStock     Int      @default(0) // Stock allocated to orders
  availableStock    Int      @default(0) // Current - Reserved
  minimumStock      Int      @default(0) // Reorder point
  maximumStock      Int?     // Maximum stock level

  // Pricing Information
  unitCost          String   @db.Text // Cost per unit
  averageCost       String   @db.Text // Weighted average cost
  lastPurchasePrice String?  @db.Text // Last purchase price
  currency          String   @default("USD")

  // Location and Storage
  location          String?  // Warehouse location
  binLocation       String?  // Specific bin/shelf location
  storageConditions String?  // Special storage requirements

  // Status and Tracking
  status            InventoryStatus @default(ACTIVE)
  isPerishable      Boolean  @default(false)
  expiryDate        DateTime?
  batchNumber       String?
  serialNumber      String?

  // Relationships
  category          InventoryCategory @relation(fields: [categoryId], references: [id])
  supplier          Supplier?         @relation(fields: [supplierId], references: [id])
  supplierCatalog   SupplierCatalog?  @relation(fields: [supplierCatalogId], references: [id])
  transactions      InventoryTransaction[]
  stockAlerts       StockAlert[]
  valuations        InventoryValuation[]
  purchaseOrderItems PurchaseOrderItem[]
  demandForecasts   DemandForecast[]
  supplyChainMetrics SupplyChainMetric[]

  // Audit fields
  createdAt         DateTime @default(now())
  updatedAt         DateTime @updatedAt
  createdBy         String?
  updatedBy         String?

  @@map("inventory_items")
}

model InventoryTransaction {
  id              String   @id @default(uuid())
  inventoryItemId String
  transactionType InventoryTransactionType
  quantity        Int      // Positive for IN, negative for OUT
  unitCost        String?  @db.Text // Cost per unit for this transaction
  totalCost       String?  @db.Text // Total cost for this transaction

  // Transaction Details
  reference       String?  // PO number, order number, etc.
  referenceType   String?  // PURCHASE_ORDER, SALE_ORDER, ADJUSTMENT, etc.
  notes           String?
  batchNumber     String?
  expiryDate      DateTime?

  // Stock Levels After Transaction
  stockBefore     Int      // Stock level before transaction
  stockAfter      Int      // Stock level after transaction

  // Relationships
  inventoryItem   InventoryItem @relation(fields: [inventoryItemId], references: [id])

  // Audit fields
  transactionDate DateTime @default(now())
  createdAt       DateTime @default(now())
  createdBy       String?

  @@map("inventory_transactions")
}

model StockAlert {
  id              String   @id @default(uuid())
  inventoryItemId String
  alertType       StockAlertType
  threshold       Int      // Stock level that triggered alert
  currentStock    Int      // Current stock when alert was created
  message         String

  // Alert Status
  status          AlertStatus @default(ACTIVE)
  acknowledgedBy  String?     // User who acknowledged the alert
  acknowledgedAt  DateTime?
  resolvedAt      DateTime?

  // Relationships
  inventoryItem   InventoryItem @relation(fields: [inventoryItemId], references: [id])

  // Audit fields
  createdAt       DateTime @default(now())
  updatedAt       DateTime @updatedAt

  @@map("stock_alerts")
}

model InventoryValuation {
  id              String   @id @default(uuid())
  inventoryItemId String
  valuationDate   DateTime
  quantity        Int      // Quantity at valuation date
  unitCost        String   @db.Text // Unit cost at valuation date
  totalValue      String   @db.Text // Total value (quantity * unit cost)
  valuationMethod String   // FIFO, LIFO, WEIGHTED_AVERAGE

  // Relationships
  inventoryItem   InventoryItem @relation(fields: [inventoryItemId], references: [id])

  // Audit fields
  createdAt       DateTime @default(now())
  createdBy       String?

  @@map("inventory_valuations")
}

// Purchase Order Management Models
model PurchaseOrder {
  id              String   @id @default(uuid())
  orderNumber     String   @unique
  supplierId      String
  requestedBy     String   // User ID who created the order
  approvedBy      String?  // User ID who approved the order

  // Order Details
  status          PurchaseOrderStatus @default(DRAFT)
  priority        OrderPriority @default(NORMAL)
  orderType       String   @default("STANDARD") // STANDARD, URGENT, BLANKET

  // Financial Information
  subtotal        String   @db.Text // Subtotal amount
  taxAmount       String   @default("0.00") @db.Text
  shippingCost    String   @default("0.00") @db.Text
  discountAmount  String   @default("0.00") @db.Text
  totalAmount     String   @db.Text // Total order amount
  currency        String   @default("USD")

  // Payment Terms
  paymentTerms    String?  // NET_30, NET_60, COD, etc.
  paymentMethod   String?  // WALLET, BANK_TRANSFER, CREDIT, etc.
  paymentStatus   PaymentStatus @default(PENDING)

  // Delivery Information
  requestedDeliveryDate DateTime?
  expectedDeliveryDate  DateTime?
  actualDeliveryDate    DateTime?
  deliveryAddress       String?
  shippingMethod        String?
  trackingNumber        String?

  // Order Lifecycle
  orderDate       DateTime @default(now())
  submittedAt     DateTime?
  approvedAt      DateTime?
  sentToSupplier  DateTime?
  acknowledgedAt  DateTime? // When supplier acknowledged the order

  // Notes and Instructions
  notes           String?
  internalNotes   String?
  specialInstructions String?

  // Relationships
  supplier        Supplier @relation(fields: [supplierId], references: [id])
  requester       User     @relation(fields: [requestedBy], references: [id])
  approver        User?    @relation("PurchaseOrderApprovals", fields: [approvedBy], references: [id])
  items           PurchaseOrderItem[]
  approvals       PurchaseOrderApproval[]
  deliveries      PurchaseOrderDelivery[]

  // Audit fields
  createdAt       DateTime @default(now())
  updatedAt       DateTime @updatedAt

  @@map("purchase_orders")
}

model PurchaseOrderItem {
  id                String   @id @default(uuid())
  purchaseOrderId   String
  inventoryItemId   String?
  supplierCatalogId String?

  // Item Details
  itemName          String
  itemCode          String?
  description       String?
  specifications    Json?

  // Quantity and Pricing
  quantityOrdered   Int
  quantityReceived  Int      @default(0)
  quantityRejected  Int      @default(0)
  unitPrice         String   @db.Text
  totalPrice        String   @db.Text
  discountPercent   String   @default("0.00") @db.Text
  discountAmount    String   @default("0.00") @db.Text

  // Status
  status            PurchaseOrderItemStatus @default(PENDING)

  // Delivery Information
  expectedDeliveryDate DateTime?
  actualDeliveryDate   DateTime?

  // Quality Information
  qualityStatus     String?  // PASSED, FAILED, PENDING_INSPECTION
  qualityNotes      String?

  // Relationships
  purchaseOrder     PurchaseOrder    @relation(fields: [purchaseOrderId], references: [id], onDelete: Cascade)
  inventoryItem     InventoryItem?   @relation(fields: [inventoryItemId], references: [id])
  supplierCatalog   SupplierCatalog? @relation(fields: [supplierCatalogId], references: [id])

  // Audit fields
  createdAt         DateTime @default(now())
  updatedAt         DateTime @updatedAt

  @@map("purchase_order_items")
}

model PurchaseOrderApproval {
  id                String   @id @default(uuid())
  purchaseOrderId   String
  approverId        String
  approvalLevel     Int      // 1, 2, 3 for multi-level approval

  // Approval Details
  status            ApprovalStatus @default(PENDING)
  decision          String?  // APPROVED, REJECTED, NEEDS_REVISION
  comments          String?
  conditions        String[] // Any conditions for approval

  // Financial Limits
  approvalLimit     String?  @db.Text // Maximum amount this approver can approve

  // Timestamps
  requestedAt       DateTime @default(now())
  respondedAt       DateTime?

  // Relationships
  purchaseOrder     PurchaseOrder @relation(fields: [purchaseOrderId], references: [id], onDelete: Cascade)
  approver          User          @relation(fields: [approverId], references: [id])

  @@unique([purchaseOrderId, approverId, approvalLevel])
  @@map("purchase_order_approvals")
}

model PurchaseOrderDelivery {
  id                String   @id @default(uuid())
  purchaseOrderId   String
  deliveryNumber    String   @unique

  // Delivery Details
  deliveryDate      DateTime
  deliveryMethod    String?  // PICKUP, DELIVERY, COURIER
  trackingNumber    String?
  receivedBy        String?  // User ID who received the delivery

  // Delivery Status
  status            DeliveryStatus @default(PENDING)
  isPartialDelivery Boolean  @default(false)

  // Quality Check
  qualityCheckStatus String? // PASSED, FAILED, PENDING
  qualityCheckBy     String? // User ID who performed quality check
  qualityCheckDate   DateTime?
  qualityNotes       String?

  // Documentation
  deliveryReceipt    String? // URL to delivery receipt
  packingList        String? // URL to packing list
  qualityReport      String? // URL to quality report

  // Notes
  deliveryNotes      String?
  issues             String[] // Any issues with the delivery

  // Relationships
  purchaseOrder      PurchaseOrder @relation(fields: [purchaseOrderId], references: [id], onDelete: Cascade)
  receiver           User?         @relation("DeliveryReceiver", fields: [receivedBy], references: [id])
  qualityChecker     User?         @relation("QualityChecker", fields: [qualityCheckBy], references: [id])

  // Audit fields
  createdAt          DateTime @default(now())
  updatedAt          DateTime @updatedAt

  @@map("purchase_order_deliveries")
}

// Supply Chain Analytics Models
model DemandForecast {
  id              String   @id @default(uuid())
  inventoryItemId String
  forecastPeriod  String   // WEEKLY, MONTHLY, QUARTERLY
  periodStart     DateTime
  periodEnd       DateTime

  // Forecast Data
  predictedDemand Int      // Predicted quantity needed
  confidence      String   @db.Text // Confidence level (0-100%)

  // Historical Data Used
  historicalPeriods Int    // Number of periods used for forecast
  seasonalFactor    String? @db.Text // Seasonal adjustment factor
  trendFactor       String? @db.Text // Trend adjustment factor

  // Actual vs Forecast (filled after period ends)
  actualDemand    Int?     // Actual demand during period
  accuracy        String?  @db.Text // Forecast accuracy percentage

  // Forecast Method
  forecastMethod  String   // MOVING_AVERAGE, EXPONENTIAL_SMOOTHING, LINEAR_REGRESSION
  parameters      Json?    // Method-specific parameters

  // Relationships
  inventoryItem   InventoryItem @relation(fields: [inventoryItemId], references: [id])

  // Audit fields
  createdAt       DateTime @default(now())
  updatedAt       DateTime @updatedAt

  @@unique([inventoryItemId, forecastPeriod, periodStart])
  @@map("demand_forecasts")
}

model SupplyChainMetric {
  id              String   @id @default(uuid())
  metricType      String   // INVENTORY_TURNOVER, LEAD_TIME, FILL_RATE, etc.
  metricPeriod    String   // DAILY, WEEKLY, MONTHLY, QUARTERLY, YEARLY
  periodStart     DateTime
  periodEnd       DateTime

  // Metric Values
  metricValue     String   @db.Text // Primary metric value
  targetValue     String?  @db.Text // Target/benchmark value
  previousValue   String?  @db.Text // Previous period value

  // Dimensional Breakdown
  supplierId      String?  // If metric is supplier-specific
  categoryId      String?  // If metric is category-specific
  itemId          String?  // If metric is item-specific

  // Additional Data
  metadata        Json?    // Additional metric data
  notes           String?

  // Relationships
  supplier        Supplier?         @relation(fields: [supplierId], references: [id])
  category        InventoryCategory? @relation(fields: [categoryId], references: [id])
  item            InventoryItem?    @relation(fields: [itemId], references: [id])

  // Audit fields
  createdAt       DateTime @default(now())
  calculatedBy    String?  // User or system that calculated the metric

  @@unique([metricType, metricPeriod, periodStart, supplierId, categoryId, itemId])
  @@map("supply_chain_metrics")
}

// Supply Chain Management Enums
enum SupplierStatus {
  PENDING       // Awaiting approval
  ACTIVE        // Active supplier
  INACTIVE      // Temporarily inactive
  SUSPENDED     // Suspended due to issues
  TERMINATED    // Permanently terminated
}

enum VerificationStatus {
  UNVERIFIED    // Not yet verified
  PENDING       // Verification in progress
  VERIFIED      // Successfully verified
  REJECTED      // Verification failed
  EXPIRED       // Verification expired
}

enum InventoryItemType {
  FABRIC        // Fabrics and textiles
  THREAD        // Threads and yarns
  ACCESSORY     // Buttons, zippers, etc.
  TOOL          // Sewing tools and equipment
  EQUIPMENT     // Machinery and large equipment
  CONSUMABLE    // Consumable supplies
  PACKAGING     // Packaging materials
}

enum InventoryStatus {
  ACTIVE        // Available for use
  INACTIVE      // Not available for use
  DISCONTINUED  // No longer stocked
  QUARANTINE    // Under quality review
  DAMAGED       // Damaged stock
  EXPIRED       // Past expiry date
}

enum InventoryTransactionType {
  PURCHASE      // Stock received from supplier
  SALE          // Stock used for order
  ADJUSTMENT    // Manual stock adjustment
  TRANSFER      // Transfer between locations
  RETURN        // Return to supplier
  WASTE         // Stock written off as waste
  THEFT         // Stock lost to theft
  DAMAGE        // Stock damaged
}

enum StockAlertType {
  LOW_STOCK     // Stock below minimum level
  OUT_OF_STOCK  // Stock is zero
  OVERSTOCK     // Stock above maximum level
  EXPIRY_WARNING // Stock approaching expiry
  REORDER_POINT // Reached reorder point
}

enum AlertStatus {
  ACTIVE        // Alert is active
  ACKNOWLEDGED  // Alert has been seen
  RESOLVED      // Issue has been resolved
  DISMISSED     // Alert was dismissed
}

enum PurchaseOrderStatus {
  DRAFT         // Being prepared
  PENDING_APPROVAL // Awaiting approval
  APPROVED      // Approved and ready to send
  SENT          // Sent to supplier
  ACKNOWLEDGED  // Acknowledged by supplier
  IN_PROGRESS   // Being fulfilled by supplier
  PARTIALLY_RECEIVED // Some items received
  COMPLETED     // All items received
  CANCELLED     // Order cancelled
  REJECTED      // Rejected by supplier or approver
}

enum OrderPriority {
  LOW           // Low priority
  NORMAL        // Normal priority
  HIGH          // High priority
  URGENT        // Urgent priority
  CRITICAL      // Critical priority
}

enum PurchaseOrderItemStatus {
  PENDING       // Awaiting fulfillment
  CONFIRMED     // Confirmed by supplier
  IN_PRODUCTION // Being produced
  SHIPPED       // Shipped by supplier
  RECEIVED      // Received and accepted
  REJECTED      // Received but rejected
  CANCELLED     // Item cancelled
}

enum ApprovalStatus {
  PENDING       // Awaiting approval
  APPROVED      // Approved
  REJECTED      // Rejected
  NEEDS_REVISION // Needs changes
  EXPIRED       // Approval request expired
}

enum DeliveryStatus {
  PENDING       // Awaiting delivery
  IN_TRANSIT    // In transit
  DELIVERED     // Delivered
  PARTIALLY_DELIVERED // Partially delivered
  FAILED        // Delivery failed
  RETURNED      // Returned to sender
}
