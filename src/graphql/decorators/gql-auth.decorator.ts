import { createParamDecorator, ExecutionContext } from '@nestjs/common';
import { GqlExecutionContext } from '@nestjs/graphql';

export const CurrentUser = createParamDecorator(
  (data: unknown, context: ExecutionContext) => {
    const ctx = GqlExecutionContext.create(context);
    return ctx.getContext().req.user;
  },
);

export const GqlRoles = (...roles: string[]) => {
  return createParamDecorator((data: unknown, context: ExecutionContext) => {
    const ctx = GqlExecutionContext.create(context);
    const user = ctx.getContext().req.user;

    // Check if user has required roles
    if (user && user.roles) {
      const hasRole = roles.some(role => user.roles.includes(role));
      if (!hasRole) {
        throw new Error('Insufficient permissions');
      }
    } else {
      throw new Error('Unauthorized access');
    }

    return user;
  });
};
