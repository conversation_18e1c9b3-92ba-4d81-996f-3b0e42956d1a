import { CustomScalar, Scalar } from '@nestjs/graphql';
import { Kind, ValueNode } from 'graphql';

@Scalar('JSON', () => Object)
export class JsonScalar implements CustomScalar<string, any> {
  description = 'JSON custom scalar type';

  parseValue(value: string): any {
    return typeof value === 'string' ? JSON.parse(value) : value;
  }

  serialize(value: any): string {
    return JSON.stringify(value);
  }

  parseLiteral(ast: ValueNode): any {
    switch (ast.kind) {
      case Kind.STRING:
        return JSON.parse(ast.value);
      case Kind.OBJECT:
        throw new Error('Objects are not allowed as input to JSON scalar');
      default:
        return null;
    }
  }
}
