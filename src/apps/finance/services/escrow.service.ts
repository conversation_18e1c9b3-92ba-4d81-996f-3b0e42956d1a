import { Injectable, NotFoundException, BadRequestException, Logger } from '@nestjs/common';
import { PrismaService } from '@core/prisma/prisma.service';
import { MongoDbService } from '@infra/mongodb/mongodb.service';
import { RabbitMQService } from '@infra/rabbitmq/rabbitmq.service';
import { WebsocketService } from '@infra/websocket/websocket.service';
import { DecimalService } from '@shared/decimal/decimal.service';
import { EscrowStatus, EscrowEventType } from '@prisma/client';
import { CreateEscrowDto } from '../dto/create-escrow.dto';
import { EscrowDto } from '../dto/escrow.dto';
import Decimal from 'decimal.js';

@Injectable()
export class EscrowService {
  private readonly logger = new Logger(EscrowService.name);

  constructor(
    private readonly prisma: PrismaService,
    private readonly mongoDbService: MongoDbService,
    private readonly rabbitMQService: RabbitMQService,
    private readonly websocketService: WebsocketService,
    private readonly decimalService: DecimalService,
  ) {}

  async createEscrow(createEscrowDto: CreateEscrowDto): Promise<EscrowDto> {
    try {
      // Validate order exists and is not already in escrow
      const order = await this.prisma.order.findUnique({
        where: { id: createEscrowDto.orderId },
        include: { escrowTransaction: true },
      });

      if (!order) {
        throw new NotFoundException(`Order with ID ${createEscrowDto.orderId} not found`);
      }

      if (order.escrowTransaction) {
        throw new BadRequestException(`Order ${createEscrowDto.orderId} already has an escrow transaction`);
      }

      // Calculate platform fee and tailor amount
      const amount = this.decimalService.create(createEscrowDto.amount);
      const platformFeeRate = this.decimalService.create(createEscrowDto.platformFeeRate || '0.05'); // Default 5%
      const platformFee = amount.mul(platformFeeRate);
      const tailorAmount = amount.sub(platformFee);

      // Calculate expiry date (default 30 days from now)
      const expiresAt = new Date();
      expiresAt.setDate(expiresAt.getDate() + (createEscrowDto.expiryDays || 30));

      // Create escrow transaction
      const escrowTransaction = await this.prisma.escrowTransaction.create({
        data: {
          orderId: createEscrowDto.orderId,
          customerId: createEscrowDto.customerId,
          tailorId: order.tailorId,
          amount: amount.toString(),
          platformFee: platformFee.toString(),
          tailorAmount: tailorAmount.toString(),
          status: EscrowStatus.HELD,
          holdReason: createEscrowDto.holdReason || 'Payment held pending order completion',
          releaseConditions: createEscrowDto.releaseConditions || ['ORDER_COMPLETED', 'CUSTOMER_CONFIRMATION'],
          paymentMethod: createEscrowDto.paymentMethod,
          paymentReference: createEscrowDto.paymentReference,
          gatewayTransactionId: createEscrowDto.gatewayTransactionId,
          expiresAt,
        },
      });

      // Create initial escrow event
      await this.createEscrowEvent(
        escrowTransaction.id,
        EscrowEventType.PAYMENT_HELD,
        `Payment of ${amount.toString()} ${createEscrowDto.currency || 'USD'} held in escrow`,
        amount.toString(),
        createEscrowDto.customerId,
        {
          paymentMethod: createEscrowDto.paymentMethod,
          platformFee: platformFee.toString(),
          tailorAmount: tailorAmount.toString(),
        }
      );

      // Sync to MongoDB
      await this.syncEscrowToMongo(escrowTransaction);

      // Notify stakeholders
      await this.notifyEscrowCreated(escrowTransaction);

      return this.mapToEscrowDto(escrowTransaction);
    } catch (error) {
      this.logger.error(`Failed to create escrow: ${error.message}`, error.stack);
      if (error instanceof NotFoundException || error instanceof BadRequestException) {
        throw error;
      }
      throw new BadRequestException(`Failed to create escrow: ${error.message}`);
    }
  }

  async releaseEscrow(escrowId: string, triggeredBy?: string, notes?: string): Promise<EscrowDto> {
    try {
      const escrow = await this.prisma.escrowTransaction.findUnique({
        where: { id: escrowId },
        include: { order: true, customer: true, tailor: true },
      });

      if (!escrow) {
        throw new NotFoundException(`Escrow transaction with ID ${escrowId} not found`);
      }

      if (escrow.status !== EscrowStatus.HELD) {
        throw new BadRequestException(`Cannot release escrow with status: ${escrow.status}`);
      }

      // Validate release conditions are met
      await this.validateReleaseConditions(escrow);

      // Update escrow status
      const updatedEscrow = await this.prisma.escrowTransaction.update({
        where: { id: escrowId },
        data: {
          status: EscrowStatus.RELEASED,
          releasedAt: new Date(),
        },
        include: { order: true, customer: true, tailor: true },
      });

      // Create release event
      await this.createEscrowEvent(
        escrowId,
        EscrowEventType.RELEASED,
        notes || `Payment released to tailor. Amount: ${escrow.tailorAmount}`,
        escrow.tailorAmount,
        triggeredBy,
        { releaseReason: notes }
      );

      // Process actual payment to tailor (integrate with payment gateway)
      await this.processPaymentToTailor(updatedEscrow);

      // Sync to MongoDB
      await this.syncEscrowToMongo(updatedEscrow);

      // Notify stakeholders
      await this.notifyEscrowReleased(updatedEscrow);

      return this.mapToEscrowDto(updatedEscrow);
    } catch (error) {
      this.logger.error(`Failed to release escrow: ${error.message}`, error.stack);
      if (error instanceof NotFoundException || error instanceof BadRequestException) {
        throw error;
      }
      throw new BadRequestException(`Failed to release escrow: ${error.message}`);
    }
  }

  async refundEscrow(escrowId: string, reason: string, triggeredBy?: string): Promise<EscrowDto> {
    try {
      const escrow = await this.prisma.escrowTransaction.findUnique({
        where: { id: escrowId },
        include: { order: true, customer: true, tailor: true },
      });

      if (!escrow) {
        throw new NotFoundException(`Escrow transaction with ID ${escrowId} not found`);
      }

      if (escrow.status !== EscrowStatus.HELD && escrow.status !== EscrowStatus.DISPUTED) {
        throw new BadRequestException(`Cannot refund escrow with status: ${escrow.status}`);
      }

      // Update escrow status
      const updatedEscrow = await this.prisma.escrowTransaction.update({
        where: { id: escrowId },
        data: {
          status: EscrowStatus.REFUNDED,
          refundedAt: new Date(),
        },
        include: { order: true, customer: true, tailor: true },
      });

      // Create refund event
      await this.createEscrowEvent(
        escrowId,
        EscrowEventType.REFUNDED,
        `Payment refunded to customer. Reason: ${reason}`,
        escrow.amount,
        triggeredBy,
        { refundReason: reason }
      );

      // Process actual refund to customer (integrate with payment gateway)
      await this.processRefundToCustomer(updatedEscrow, reason);

      // Sync to MongoDB
      await this.syncEscrowToMongo(updatedEscrow);

      // Notify stakeholders
      await this.notifyEscrowRefunded(updatedEscrow, reason);

      return this.mapToEscrowDto(updatedEscrow);
    } catch (error) {
      this.logger.error(`Failed to refund escrow: ${error.message}`, error.stack);
      if (error instanceof NotFoundException || error instanceof BadRequestException) {
        throw error;
      }
      throw new BadRequestException(`Failed to refund escrow: ${error.message}`);
    }
  }

  async findEscrowByOrderId(orderId: string): Promise<EscrowDto | null> {
    const escrow = await this.mongoDbService.findOne('escrowTransactions', { orderId });
    return escrow ? this.mapToEscrowDto(escrow) : null;
  }

  async findAllEscrows(status?: EscrowStatus): Promise<EscrowDto[]> {
    const filter = status ? { status } : {};
    const escrows = await this.mongoDbService.find('escrowTransactions', filter);
    return escrows.map(escrow => this.mapToEscrowDto(escrow));
  }

  async checkExpiredEscrows(): Promise<void> {
    const expiredEscrows = await this.prisma.escrowTransaction.findMany({
      where: {
        status: EscrowStatus.HELD,
        expiresAt: {
          lt: new Date(),
        },
      },
    });

    for (const escrow of expiredEscrows) {
      await this.handleExpiredEscrow(escrow.id);
    }
  }

  private async validateReleaseConditions(escrow: any): Promise<void> {
    const conditions = escrow.releaseConditions || [];
    
    for (const condition of conditions) {
      switch (condition) {
        case 'ORDER_COMPLETED':
          if (escrow.order.status !== 'DELIVERED' && escrow.order.status !== 'COMPLETED') {
            throw new BadRequestException('Order must be completed before releasing escrow');
          }
          break;
        case 'CUSTOMER_CONFIRMATION':
          // Check if customer has confirmed receipt (this would be implemented based on your confirmation system)
          break;
        default:
          this.logger.warn(`Unknown release condition: ${condition}`);
      }
    }
  }

  private async createEscrowEvent(
    escrowTransactionId: string,
    eventType: EscrowEventType,
    description: string,
    amount?: string,
    triggeredBy?: string,
    metadata?: any
  ): Promise<void> {
    await this.prisma.escrowEvent.create({
      data: {
        escrowTransactionId,
        eventType,
        description,
        amount,
        triggeredBy,
        metadata,
      },
    });
  }

  private async processPaymentToTailor(escrow: any): Promise<void> {
    // Integrate with payment gateway to transfer funds to tailor
    // This is a placeholder for actual payment processing
    this.logger.log(`Processing payment of ${escrow.tailorAmount} to tailor ${escrow.tailorId}`);
    
    // Create a transaction record for the payment
    await this.prisma.transaction.create({
      data: {
        type: 'SALE',
        status: 'COMPLETED',
        amount: escrow.tailorAmount,
        currency: 'USD',
        description: `Escrow release payment for order ${escrow.orderId}`,
        orderId: escrow.orderId,
        customerId: escrow.customerId,
        paymentMethod: escrow.paymentMethod,
        referenceNumber: `ESCROW_RELEASE_${escrow.id}`,
      },
    });
  }

  private async processRefundToCustomer(escrow: any, reason: string): Promise<void> {
    // Integrate with payment gateway to refund customer
    // This is a placeholder for actual refund processing
    this.logger.log(`Processing refund of ${escrow.amount} to customer ${escrow.customerId}`);
    
    // Create a transaction record for the refund
    await this.prisma.transaction.create({
      data: {
        type: 'REFUND',
        status: 'COMPLETED',
        amount: escrow.amount,
        currency: 'USD',
        description: `Escrow refund for order ${escrow.orderId}. Reason: ${reason}`,
        orderId: escrow.orderId,
        customerId: escrow.customerId,
        paymentMethod: escrow.paymentMethod,
        referenceNumber: `ESCROW_REFUND_${escrow.id}`,
      },
    });
  }

  private async handleExpiredEscrow(escrowId: string): Promise<void> {
    await this.prisma.escrowTransaction.update({
      where: { id: escrowId },
      data: { status: EscrowStatus.EXPIRED },
    });

    await this.createEscrowEvent(
      escrowId,
      EscrowEventType.EXPIRED,
      'Escrow transaction expired and requires manual review',
      undefined,
      undefined,
      { autoExpired: true }
    );
  }

  private async syncEscrowToMongo(escrow: any): Promise<void> {
    await this.mongoDbService.syncDocument('escrowTransactions', escrow);
  }

  private async notifyEscrowCreated(escrow: any): Promise<void> {
    await this.rabbitMQService.publish({
      type: 'escrow_created',
      data: escrow,
    });

    // Notify customer
    this.websocketService.sendToUser(escrow.customerId, 'escrow_created', {
      message: 'Your payment has been securely held in escrow',
      escrowId: escrow.id,
      amount: escrow.amount,
    });

    // Notify tailor if applicable
    if (escrow.tailorId) {
      this.websocketService.sendToUser(escrow.tailorId, 'escrow_created', {
        message: 'Payment for your order has been secured in escrow',
        escrowId: escrow.id,
        amount: escrow.tailorAmount,
      });
    }
  }

  private async notifyEscrowReleased(escrow: any): Promise<void> {
    await this.rabbitMQService.publish({
      type: 'escrow_released',
      data: escrow,
    });

    // Notify tailor
    if (escrow.tailorId) {
      this.websocketService.sendToUser(escrow.tailorId, 'escrow_released', {
        message: 'Payment has been released to your account',
        escrowId: escrow.id,
        amount: escrow.tailorAmount,
      });
    }

    // Notify customer
    this.websocketService.sendToUser(escrow.customerId, 'escrow_released', {
      message: 'Payment has been released to the tailor',
      escrowId: escrow.id,
    });
  }

  private async notifyEscrowRefunded(escrow: any, reason: string): Promise<void> {
    await this.rabbitMQService.publish({
      type: 'escrow_refunded',
      data: { ...escrow, refundReason: reason },
    });

    // Notify customer
    this.websocketService.sendToUser(escrow.customerId, 'escrow_refunded', {
      message: 'Your payment has been refunded',
      escrowId: escrow.id,
      amount: escrow.amount,
      reason,
    });

    // Notify tailor if applicable
    if (escrow.tailorId) {
      this.websocketService.sendToUser(escrow.tailorId, 'escrow_refunded', {
        message: 'Order payment has been refunded to customer',
        escrowId: escrow.id,
        reason,
      });
    }
  }

  private mapToEscrowDto(escrow: any): EscrowDto {
    return {
      id: escrow.id,
      orderId: escrow.orderId,
      customerId: escrow.customerId,
      tailorId: escrow.tailorId,
      amount: escrow.amount,
      platformFee: escrow.platformFee,
      tailorAmount: escrow.tailorAmount,
      status: escrow.status,
      holdReason: escrow.holdReason,
      releaseConditions: escrow.releaseConditions,
      paymentMethod: escrow.paymentMethod,
      paymentReference: escrow.paymentReference,
      gatewayTransactionId: escrow.gatewayTransactionId,
      heldAt: escrow.heldAt,
      releasedAt: escrow.releasedAt,
      refundedAt: escrow.refundedAt,
      expiresAt: escrow.expiresAt,
      createdAt: escrow.createdAt,
      updatedAt: escrow.updatedAt,
    };
  }
}
