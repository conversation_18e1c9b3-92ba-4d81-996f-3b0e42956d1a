import { Injectable, Logger } from '@nestjs/common';
import { PrismaService } from '@core/prisma/prisma.service';
import { DecimalService } from '@shared/decimal/decimal.service';
import { WalletTransactionStatus, LoanStatus, InstallmentPlanStatus, InstallmentStatus } from '@prisma/client';

@Injectable()
export class FinancialReportsService {
  private readonly logger = new Logger(FinancialReportsService.name);

  constructor(
    private readonly prisma: PrismaService,
    private readonly decimalService: DecimalService,
  ) {}

  async getFinancialDashboard(period: string): Promise<any> {
    try {
      const periodDate = this.getPeriodDate(period);
      
      const [
        totalRevenue,
        totalTransactions,
        walletStats,
        loanStats,
        installmentStats,
        revenueByMonth,
        transactionsByType,
        riskDistribution
      ] = await Promise.all([
        this.calculateTotalRevenue(periodDate),
        this.getTotalTransactions(periodDate),
        this.getWalletStats(),
        this.getLoanStats(),
        this.getInstallmentStats(),
        this.getRevenueByMonth(periodDate),
        this.getTransactionsByType(periodDate),
        this.getRiskDistribution()
      ]);

      return {
        totalRevenue,
        totalTransactions,
        activeWallets: walletStats.activeWallets,
        activeLoans: loanStats.activeLoans,
        activeInstallmentPlans: installmentStats.activePlans,
        totalWalletBalance: walletStats.totalBalance,
        totalLoanBalance: loanStats.totalBalance,
        totalInstallmentBalance: installmentStats.totalOutstanding,
        monthlyGrowth: await this.calculateMonthlyGrowth(),
        defaultRate: loanStats.defaultRate,
        collectionEfficiency: installmentStats.collectionEfficiency,
        revenueByMonth,
        transactionsByType,
        riskDistribution,
        lastUpdated: new Date(),
      };
    } catch (error) {
      this.logger.error(`Failed to generate financial dashboard: ${error.message}`, error.stack);
      throw error;
    }
  }

  async getTransactionReport(filters: any): Promise<any> {
    try {
      const where: any = {
        status: WalletTransactionStatus.COMPLETED,
      };

      if (filters.startDate) where.createdAt = { ...where.createdAt, gte: filters.startDate };
      if (filters.endDate) where.createdAt = { ...where.createdAt, lte: filters.endDate };
      if (filters.type) where.type = filters.type;
      if (filters.userId) where.wallet = { userId: filters.userId };

      const [transactions, totalCount, summary] = await Promise.all([
        this.prisma.walletTransaction.findMany({
          where,
          include: { wallet: { include: { user: true } } },
          orderBy: { createdAt: 'desc' },
          skip: (filters.page - 1) * filters.limit,
          take: filters.limit,
        }),
        this.prisma.walletTransaction.count({ where }),
        this.getTransactionSummary(where)
      ]);

      return {
        transactions: transactions.map(tx => ({
          id: tx.id,
          type: tx.type,
          amount: tx.amount,
          currency: tx.currency,
          description: tx.description,
          status: tx.status,
          createdAt: tx.createdAt,
          user: {
            id: tx.wallet.user.id,
            name: `${tx.wallet.user.firstName} ${tx.wallet.user.lastName}`,
            email: tx.wallet.user.email,
          },
        })),
        summary,
        pagination: {
          page: filters.page,
          limit: filters.limit,
          total: totalCount,
          totalPages: Math.ceil(totalCount / filters.limit),
        },
      };
    } catch (error) {
      this.logger.error(`Failed to generate transaction report: ${error.message}`, error.stack);
      throw error;
    }
  }

  async getCreditRiskReport(): Promise<any> {
    try {
      const [
        creditProfiles,
        loanPerformance,
        riskTrends
      ] = await Promise.all([
        this.getCreditProfileStats(),
        this.getLoanPerformanceStats(),
        this.getRiskTrends()
      ]);

      return {
        overview: {
          totalCreditProfiles: creditProfiles.total,
          totalCreditLimit: creditProfiles.totalLimit,
          totalUsedCredit: creditProfiles.totalUsed,
          utilizationRate: creditProfiles.utilizationRate,
          averageCreditScore: creditProfiles.averageScore,
          defaultRate: loanPerformance.defaultRate,
        },
        riskDistribution: creditProfiles.riskDistribution,
        creditGradeDistribution: creditProfiles.gradeDistribution,
        loanPerformance,
        riskTrends,
        lastUpdated: new Date(),
      };
    } catch (error) {
      this.logger.error(`Failed to generate credit risk report: ${error.message}`, error.stack);
      throw error;
    }
  }

  async getInstallmentPerformanceReport(): Promise<any> {
    try {
      const [
        planStats,
        paymentPerformance,
        performanceTrends
      ] = await Promise.all([
        this.getInstallmentPlanStats(),
        this.getPaymentPerformanceStats(),
        this.getInstallmentPerformanceTrends()
      ]);

      return {
        overview: planStats,
        paymentPerformance,
        planDistribution: await this.getPlanDistribution(),
        performanceTrends,
        lastUpdated: new Date(),
      };
    } catch (error) {
      this.logger.error(`Failed to generate installment performance report: ${error.message}`, error.stack);
      throw error;
    }
  }

  async getUserFinancialStatement(userId: string, options: any): Promise<any> {
    try {
      const [
        user,
        wallet,
        creditProfile,
        loans,
        installmentPlans,
        transactions
      ] = await Promise.all([
        this.prisma.user.findUnique({ where: { id: userId } }),
        this.prisma.wallet.findUnique({ where: { userId } }),
        this.prisma.creditProfile.findUnique({ where: { userId } }),
        this.prisma.loan.findMany({ where: { userId }, include: { payments: true } }),
        this.prisma.installmentPlan.findMany({ 
          where: { userId }, 
          include: { installments: true } 
        }),
        this.getUserTransactions(userId, options)
      ]);

      const summary = this.calculateUserFinancialSummary({
        wallet,
        creditProfile,
        loans,
        installmentPlans
      });

      return {
        user: {
          id: user?.id,
          name: `${user?.firstName} ${user?.lastName}`,
          email: user?.email,
        },
        wallet,
        creditProfile,
        loans,
        installmentPlans,
        transactions,
        summary,
        generatedAt: new Date(),
      };
    } catch (error) {
      this.logger.error(`Failed to generate user financial statement: ${error.message}`, error.stack);
      throw error;
    }
  }

  async getRevenueAnalysis(period: string): Promise<any> {
    try {
      const periodDate = this.getPeriodDate(period);
      
      const [
        totalRevenue,
        revenueBySource,
        monthlyTrends,
        projections,
        growthMetrics
      ] = await Promise.all([
        this.calculateTotalRevenue(periodDate),
        this.getRevenueBySource(periodDate),
        this.getMonthlyRevenueTrends(periodDate),
        this.getRevenueProjections(),
        this.getGrowthMetrics(periodDate)
      ]);

      return {
        totalRevenue,
        revenueBySource,
        monthlyTrends,
        projections,
        growthMetrics,
        lastUpdated: new Date(),
      };
    } catch (error) {
      this.logger.error(`Failed to generate revenue analysis: ${error.message}`, error.stack);
      throw error;
    }
  }

  // Private helper methods
  private getPeriodDate(period: string): Date {
    const now = new Date();
    switch (period) {
      case '7d':
        return new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
      case '30d':
        return new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
      case '90d':
        return new Date(now.getTime() - 90 * 24 * 60 * 60 * 1000);
      case '1y':
        return new Date(now.getTime() - 365 * 24 * 60 * 60 * 1000);
      default:
        return new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
    }
  }

  private async calculateTotalRevenue(since: Date): Promise<string> {
    // Calculate revenue from various sources
    const [platformFees, interestIncome, transactionFees, lateFees] = await Promise.all([
      this.calculatePlatformFees(since),
      this.calculateInterestIncome(since),
      this.calculateTransactionFees(since),
      this.calculateLateFees(since)
    ]);

    return this.decimalService.create(platformFees)
      .add(this.decimalService.create(interestIncome))
      .add(this.decimalService.create(transactionFees))
      .add(this.decimalService.create(lateFees))
      .toString();
  }

  private async calculatePlatformFees(since: Date): Promise<string> {
    // This would calculate platform fees from escrow transactions
    const escrowTransactions = await this.prisma.escrowTransaction.findMany({
      where: {
        createdAt: { gte: since },
        status: 'RELEASED',
      },
    });

    return escrowTransactions.reduce((total, tx) => 
      total.add(this.decimalService.create(tx.platformFee || '0')), 
      this.decimalService.create('0')
    ).toString();
  }

  private async calculateInterestIncome(since: Date): Promise<string> {
    // Calculate interest income from loans
    const loanPayments = await this.prisma.loanPayment.findMany({
      where: {
        createdAt: { gte: since },
        status: 'PAID',
      },
    });

    return loanPayments.reduce((total, payment) => 
      total.add(this.decimalService.create(payment.interestAmount)), 
      this.decimalService.create('0')
    ).toString();
  }

  private async calculateTransactionFees(since: Date): Promise<string> {
    // Calculate transaction fees from wallet transactions
    const transactions = await this.prisma.walletTransaction.findMany({
      where: {
        createdAt: { gte: since },
        status: WalletTransactionStatus.COMPLETED,
        feeAmount: { not: null },
      },
    });

    return transactions.reduce((total, tx) => 
      total.add(this.decimalService.create(tx.feeAmount || '0')), 
      this.decimalService.create('0')
    ).toString();
  }

  private async calculateLateFees(since: Date): Promise<string> {
    // Calculate late fees from installments
    const installments = await this.prisma.installment.findMany({
      where: {
        updatedAt: { gte: since },
        status: InstallmentStatus.PAID,
        lateFeeAmount: { not: '0.00' },
      },
    });

    return installments.reduce((total, installment) => 
      total.add(this.decimalService.create(installment.lateFeeAmount)), 
      this.decimalService.create('0')
    ).toString();
  }

  private async getTotalTransactions(since: Date): Promise<number> {
    return this.prisma.walletTransaction.count({
      where: {
        createdAt: { gte: since },
        status: WalletTransactionStatus.COMPLETED,
      },
    });
  }

  private async getWalletStats(): Promise<any> {
    const [activeWallets, activeWalletBalances] = await Promise.all([
      this.prisma.wallet.count({ where: { status: 'ACTIVE' } }),
      this.prisma.wallet.findMany({
        where: { status: 'ACTIVE' },
        select: { balance: true }
      })
    ]);

    const totalBalance = activeWalletBalances.reduce((sum, wallet) =>
      sum.add(this.decimalService.create(wallet.balance)),
      this.decimalService.create('0')
    ).toString();

    return {
      activeWallets,
      totalBalance,
    };
  }

  private async getLoanStats(): Promise<any> {
    const [activeLoans, activeLoanBalances, defaultedLoans] = await Promise.all([
      this.prisma.loan.count({ where: { status: LoanStatus.ACTIVE } }),
      this.prisma.loan.findMany({
        where: { status: LoanStatus.ACTIVE },
        select: { currentBalance: true }
      }),
      this.prisma.loan.count({ where: { status: LoanStatus.DEFAULTED } })
    ]);

    const totalBalance = activeLoanBalances.reduce((sum, loan) =>
      sum.add(this.decimalService.create(loan.currentBalance)),
      this.decimalService.create('0')
    ).toString();

    const totalLoans = await this.prisma.loan.count();
    const defaultRate = totalLoans > 0 ? (defaultedLoans / totalLoans) * 100 : 0;

    return {
      activeLoans,
      totalBalance,
      defaultRate: Number(defaultRate.toFixed(2)),
    };
  }

  private async getInstallmentStats(): Promise<any> {
    const [activePlans, pendingInstallments, completedPlans] = await Promise.all([
      this.prisma.installmentPlan.count({ where: { status: InstallmentPlanStatus.ACTIVE } }),
      this.prisma.installment.findMany({
        where: { status: InstallmentStatus.PENDING },
        select: { amount: true }
      }),
      this.prisma.installmentPlan.count({ where: { status: InstallmentPlanStatus.COMPLETED } })
    ]);

    const totalOutstanding = pendingInstallments.reduce((sum, installment) =>
      sum.add(this.decimalService.create(installment.amount)),
      this.decimalService.create('0')
    ).toString();

    const totalPlans = await this.prisma.installmentPlan.count();
    const collectionEfficiency = totalPlans > 0 ? (completedPlans / totalPlans) * 100 : 0;

    return {
      activePlans,
      totalOutstanding,
      collectionEfficiency: Number(collectionEfficiency.toFixed(2)),
    };
  }

  private async calculateMonthlyGrowth(): Promise<number> {
    // Calculate month-over-month growth
    const now = new Date();
    const lastMonth = new Date(now.getFullYear(), now.getMonth() - 1, 1);
    const twoMonthsAgo = new Date(now.getFullYear(), now.getMonth() - 2, 1);

    const [currentMonthRevenue, lastMonthRevenue] = await Promise.all([
      this.calculateTotalRevenue(lastMonth),
      this.calculateTotalRevenue(twoMonthsAgo)
    ]);

    const current = this.decimalService.create(currentMonthRevenue);
    const previous = this.decimalService.create(lastMonthRevenue);

    if (previous.eq(0)) return 0;

    return current.sub(previous).div(previous).mul(100).toNumber();
  }

  private async getRevenueByMonth(since: Date): Promise<any[]> {
    // This would generate monthly revenue data
    // Implementation would depend on specific requirements
    return [];
  }

  private async getTransactionsByType(since: Date): Promise<any> {
    const transactions = await this.prisma.walletTransaction.groupBy({
      by: ['type'],
      where: {
        createdAt: { gte: since },
        status: WalletTransactionStatus.COMPLETED,
      },
      _count: { type: true },
    });

    // Get volume for each type separately
    const result: any = {};
    for (const tx of transactions) {
      const volumeData = await this.prisma.walletTransaction.findMany({
        where: {
          type: tx.type,
          createdAt: { gte: since },
          status: WalletTransactionStatus.COMPLETED,
        },
        select: { amount: true },
      });

      const volume = volumeData.reduce((sum, t) =>
        sum.add(this.decimalService.create(t.amount)),
        this.decimalService.create('0')
      ).toString();

      result[tx.type] = {
        count: tx._count.type,
        volume,
      };
    }

    return result;
  }

  private async getRiskDistribution(): Promise<any> {
    const riskDistribution = await this.prisma.creditProfile.groupBy({
      by: ['riskLevel'],
      _count: { riskLevel: true },
    });

    const result: any = {};
    riskDistribution.forEach(risk => {
      if (risk.riskLevel) {
        result[risk.riskLevel] = risk._count.riskLevel;
      }
    });

    return result;
  }

  private async getTransactionSummary(where: any): Promise<any> {
    const [allTransactions, typeDistribution, statusDistribution] = await Promise.all([
      this.prisma.walletTransaction.findMany({
        where,
        select: { id: true, amount: true },
      }),
      this.prisma.walletTransaction.groupBy({
        by: ['type'],
        where,
        _count: { type: true },
      }),
      this.prisma.walletTransaction.groupBy({
        by: ['status'],
        where,
        _count: { status: true },
      })
    ]);

    const totalTransactions = allTransactions.length;
    const totalVolume = allTransactions.reduce((sum, tx) =>
      sum.add(this.decimalService.create(tx.amount)),
      this.decimalService.create('0')
    ).toString();

    const averageTransactionSize = totalTransactions > 0
      ? this.decimalService.create(totalVolume).div(totalTransactions).toString()
      : '0.00';

    const transactionsByType: any = {};
    typeDistribution.forEach(type => {
      transactionsByType[type.type] = type._count.type;
    });

    const transactionsByStatus: any = {};
    statusDistribution.forEach(status => {
      transactionsByStatus[status.status] = status._count.status;
    });

    return {
      totalTransactions,
      totalVolume,
      averageTransactionSize,
      transactionsByType,
      transactionsByStatus,
    };
  }

  private async getCreditProfileStats(): Promise<any> {
    // Implementation for credit profile statistics
    return {
      total: 0,
      totalLimit: '0.00',
      totalUsed: '0.00',
      utilizationRate: 0,
      averageScore: 0,
      riskDistribution: {},
      gradeDistribution: {},
    };
  }

  private async getLoanPerformanceStats(): Promise<any> {
    // Implementation for loan performance statistics
    return {
      defaultRate: 0,
    };
  }

  private async getRiskTrends(): Promise<any[]> {
    // Implementation for risk trends
    return [];
  }

  private async getInstallmentPlanStats(): Promise<any> {
    // Implementation for installment plan statistics
    return {};
  }

  private async getPaymentPerformanceStats(): Promise<any> {
    // Implementation for payment performance statistics
    return {};
  }

  private async getInstallmentPerformanceTrends(): Promise<any[]> {
    // Implementation for installment performance trends
    return [];
  }

  private async getPlanDistribution(): Promise<any> {
    // Implementation for plan distribution
    return {};
  }

  private async getUserTransactions(userId: string, options: any): Promise<any[]> {
    // Implementation for user transactions
    return [];
  }

  private calculateUserFinancialSummary(data: any): any {
    // Implementation for user financial summary calculation
    return {};
  }

  private async getRevenueBySource(since: Date): Promise<any> {
    // Implementation for revenue by source
    return {};
  }

  private async getMonthlyRevenueTrends(since: Date): Promise<any[]> {
    // Implementation for monthly revenue trends
    return [];
  }

  private async getRevenueProjections(): Promise<any> {
    // Implementation for revenue projections
    return {};
  }

  private async getGrowthMetrics(since: Date): Promise<any> {
    // Implementation for growth metrics
    return {};
  }
}
