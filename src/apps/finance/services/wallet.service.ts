import { Injectable, NotFoundException, BadRequestException, Logger } from '@nestjs/common';
import { PrismaService } from '@core/prisma/prisma.service';
import { MongoDbService } from '@infra/mongodb/mongodb.service';
import { RabbitMQService } from '@infra/rabbitmq/rabbitmq.service';
import { WebsocketService } from '@infra/websocket/websocket.service';
import { DecimalService } from '@shared/decimal/decimal.service';
import { WalletStatus, WalletTransactionType, WalletTransactionStatus } from '@prisma/client';
import { CreateWalletDto, FundWalletDto, TransferFundsDto, WithdrawFundsDto } from '../dto/wallet.dto';
import { WalletDto, WalletTransactionDto, WalletTransferDto } from '../dto/wallet-response.dto';
import Decimal from 'decimal.js';

@Injectable()
export class WalletService {
  private readonly logger = new Logger(WalletService.name);

  constructor(
    private readonly prisma: PrismaService,
    private readonly mongoDbService: MongoDbService,
    private readonly rabbitMQService: RabbitMQService,
    private readonly websocketService: WebsocketService,
    private readonly decimalService: DecimalService,
  ) {}

  async createWallet(createWalletDto: CreateWalletDto): Promise<WalletDto> {
    try {
      // Check if user already has a wallet
      const existingWallet = await this.prisma.wallet.findUnique({
        where: { userId: createWalletDto.userId },
      });

      if (existingWallet) {
        throw new BadRequestException('User already has a wallet');
      }

      // Create wallet with initial balance
      const wallet = await this.prisma.wallet.create({
        data: {
          userId: createWalletDto.userId,
          balance: createWalletDto.initialBalance || '0.00',
          currency: createWalletDto.currency || 'USD',
          dailyLimit: createWalletDto.dailyLimit,
          monthlyLimit: createWalletDto.monthlyLimit,
          currencyBalances: createWalletDto.initialBalance 
            ? { [createWalletDto.currency || 'USD']: createWalletDto.initialBalance }
            : {},
        },
        include: { user: true },
      });

      // Create initial transaction if there's an initial balance
      if (createWalletDto.initialBalance && this.decimalService.create(createWalletDto.initialBalance).gt(0)) {
        await this.createTransaction(
          wallet.id,
          WalletTransactionType.DEPOSIT,
          createWalletDto.initialBalance,
          'Initial wallet funding',
          '0.00',
          createWalletDto.initialBalance,
          { source: 'INITIAL_FUNDING' }
        );
      }

      // Sync to MongoDB
      await this.mongoDbService.syncDocument('wallets', wallet);

      // Notify user
      await this.notifyWalletCreated(wallet);

      return this.mapToWalletDto(wallet);
    } catch (error) {
      this.logger.error(`Failed to create wallet: ${error.message}`, error.stack);
      throw error;
    }
  }

  async getWallet(userId: string): Promise<WalletDto> {
    const wallet = await this.prisma.wallet.findUnique({
      where: { userId },
      include: { user: true },
    });

    if (!wallet) {
      throw new NotFoundException(`Wallet not found for user ${userId}`);
    }

    return this.mapToWalletDto(wallet);
  }

  async fundWallet(fundWalletDto: FundWalletDto): Promise<WalletTransactionDto> {
    try {
      const wallet = await this.prisma.wallet.findUnique({
        where: { userId: fundWalletDto.userId },
      });

      if (!wallet) {
        throw new NotFoundException(`Wallet not found for user ${fundWalletDto.userId}`);
      }

      if (wallet.status !== WalletStatus.ACTIVE) {
        throw new BadRequestException(`Wallet is ${wallet.status} and cannot receive funds`);
      }

      // Validate daily/monthly limits
      await this.validateTransactionLimits(wallet.id, fundWalletDto.amount, 'DEPOSIT');

      // Calculate new balance
      const currentBalance = this.decimalService.create(wallet.balance);
      const fundAmount = this.decimalService.create(fundWalletDto.amount);
      const newBalance = currentBalance.add(fundAmount);

      // Process payment through gateway (placeholder)
      const gatewayResponse = await this.processPaymentGateway(fundWalletDto);

      // Update wallet balance
      await this.prisma.wallet.update({
        where: { id: wallet.id },
        data: {
          balance: newBalance.toString(),
          lastActivity: new Date(),
          currencyBalances: this.updateCurrencyBalance(
            wallet.currencyBalances as any,
            fundWalletDto.currency || 'USD',
            fundAmount.toString(),
            'ADD'
          ),
        },
      });

      // Create transaction record
      const transaction = await this.createTransaction(
        wallet.id,
        WalletTransactionType.DEPOSIT,
        fundWalletDto.amount,
        fundWalletDto.description || 'Wallet funding',
        wallet.balance,
        newBalance.toString(),
        {
          paymentMethod: fundWalletDto.paymentMethod,
          gatewayTransactionId: gatewayResponse.transactionId,
          source: fundWalletDto.source,
        }
      );

      // Sync to MongoDB
      await this.mongoDbService.syncDocument('walletTransactions', transaction);

      // Notify user
      await this.notifyWalletFunded(wallet.userId, transaction);

      return this.mapToTransactionDto(transaction);
    } catch (error) {
      this.logger.error(`Failed to fund wallet: ${error.message}`, error.stack);
      throw error;
    }
  }

  async withdrawFunds(withdrawFundsDto: WithdrawFundsDto): Promise<WalletTransactionDto> {
    try {
      const wallet = await this.prisma.wallet.findUnique({
        where: { userId: withdrawFundsDto.userId },
      });

      if (!wallet) {
        throw new NotFoundException(`Wallet not found for user ${withdrawFundsDto.userId}`);
      }

      if (wallet.status !== WalletStatus.ACTIVE) {
        throw new BadRequestException(`Wallet is ${wallet.status} and cannot process withdrawals`);
      }

      // Check sufficient balance
      const currentBalance = this.decimalService.create(wallet.balance);
      const withdrawAmount = this.decimalService.create(withdrawFundsDto.amount);

      if (currentBalance.lt(withdrawAmount)) {
        throw new BadRequestException('Insufficient wallet balance');
      }

      // Validate daily/monthly limits
      await this.validateTransactionLimits(wallet.id, withdrawFundsDto.amount, 'WITHDRAWAL');

      // Calculate new balance
      const newBalance = currentBalance.sub(withdrawAmount);

      // Process withdrawal through gateway (placeholder)
      const gatewayResponse = await this.processWithdrawalGateway(withdrawFundsDto);

      // Update wallet balance
      await this.prisma.wallet.update({
        where: { id: wallet.id },
        data: {
          balance: newBalance.toString(),
          lastActivity: new Date(),
          currencyBalances: this.updateCurrencyBalance(
            wallet.currencyBalances as any,
            withdrawFundsDto.currency || 'USD',
            withdrawAmount.toString(),
            'SUBTRACT'
          ),
        },
      });

      // Create transaction record
      const transaction = await this.createTransaction(
        wallet.id,
        WalletTransactionType.WITHDRAWAL,
        withdrawFundsDto.amount,
        withdrawFundsDto.description || 'Wallet withdrawal',
        wallet.balance,
        newBalance.toString(),
        {
          withdrawalMethod: withdrawFundsDto.withdrawalMethod,
          gatewayTransactionId: gatewayResponse.transactionId,
          destination: withdrawFundsDto.destination,
        }
      );

      // Sync to MongoDB
      await this.mongoDbService.syncDocument('walletTransactions', transaction);

      // Notify user
      await this.notifyWalletWithdrawal(wallet.userId, transaction);

      return this.mapToTransactionDto(transaction);
    } catch (error) {
      this.logger.error(`Failed to withdraw funds: ${error.message}`, error.stack);
      throw error;
    }
  }

  async transferFunds(transferFundsDto: TransferFundsDto): Promise<WalletTransferDto> {
    try {
      // Get sender and receiver wallets
      const [senderWallet, receiverWallet] = await Promise.all([
        this.prisma.wallet.findUnique({
          where: { userId: transferFundsDto.senderUserId },
          include: { user: true }
        }),
        this.prisma.wallet.findUnique({
          where: { userId: transferFundsDto.receiverUserId },
          include: { user: true }
        }),
      ]);

      if (!senderWallet) {
        throw new NotFoundException(`Sender wallet not found`);
      }

      if (!receiverWallet) {
        throw new NotFoundException(`Receiver wallet not found`);
      }

      if (senderWallet.status !== WalletStatus.ACTIVE || receiverWallet.status !== WalletStatus.ACTIVE) {
        throw new BadRequestException('Both wallets must be active for transfers');
      }

      // Check sufficient balance
      const senderBalance = this.decimalService.create(senderWallet.balance);
      const transferAmount = this.decimalService.create(transferFundsDto.amount);
      const transferFee = this.decimalService.create(transferFundsDto.transferFee || '0.00');
      const totalDeduction = transferAmount.add(transferFee);

      if (senderBalance.lt(totalDeduction)) {
        throw new BadRequestException('Insufficient balance for transfer including fees');
      }

      // Validate transfer limits
      await this.validateTransactionLimits(senderWallet.id, transferFundsDto.amount, 'TRANSFER');

      // Calculate exchange rate if different currencies
      let exchangeRate = '1.00';
      let convertedAmount = transferFundsDto.amount;
      
      if (senderWallet.currency !== receiverWallet.currency) {
        const rate = await this.getExchangeRate(senderWallet.currency, receiverWallet.currency);
        exchangeRate = rate.toString();
        convertedAmount = transferAmount.mul(rate).toString();
      }

      // Create transfer record
      const transfer = await this.prisma.walletTransfer.create({
        data: {
          senderWalletId: senderWallet.id,
          receiverWalletId: receiverWallet.id,
          amount: transferFundsDto.amount,
          currency: transferFundsDto.currency || 'USD',
          description: transferFundsDto.description,
          reference: transferFundsDto.reference,
          transferFee: transferFee.toString(),
          feePayerId: transferFundsDto.feePayerId || transferFundsDto.senderUserId,
          exchangeRate,
          convertedAmount,
          status: WalletTransactionStatus.COMPLETED,
          completedAt: new Date(),
        },
        include: {
          senderWallet: { include: { user: true } },
          receiverWallet: { include: { user: true } },
        },
      });

      // Update sender wallet
      const newSenderBalance = senderBalance.sub(totalDeduction);
      await this.prisma.wallet.update({
        where: { id: senderWallet.id },
        data: {
          balance: newSenderBalance.toString(),
          lastActivity: new Date(),
        },
      });

      // Update receiver wallet
      const receiverBalance = this.decimalService.create(receiverWallet.balance);
      const newReceiverBalance = receiverBalance.add(this.decimalService.create(convertedAmount));
      await this.prisma.wallet.update({
        where: { id: receiverWallet.id },
        data: {
          balance: newReceiverBalance.toString(),
          lastActivity: new Date(),
        },
      });

      // Create transaction records for both wallets
      await Promise.all([
        this.createTransaction(
          senderWallet.id,
          WalletTransactionType.TRANSFER_OUT,
          transferFundsDto.amount,
          `Transfer to ${receiverWallet.user?.firstName || 'user'}`,
          senderWallet.balance,
          newSenderBalance.toString(),
          { transferId: transfer.id, receiverUserId: transferFundsDto.receiverUserId }
        ),
        this.createTransaction(
          receiverWallet.id,
          WalletTransactionType.TRANSFER_IN,
          convertedAmount,
          `Transfer from ${senderWallet.user?.firstName || 'user'}`,
          receiverWallet.balance,
          newReceiverBalance.toString(),
          { transferId: transfer.id, senderUserId: transferFundsDto.senderUserId }
        ),
      ]);

      // Sync to MongoDB
      await this.mongoDbService.syncDocument('walletTransfers', transfer);

      // Notify both users
      await this.notifyWalletTransfer(transfer);

      return this.mapToTransferDto(transfer);
    } catch (error) {
      this.logger.error(`Failed to transfer funds: ${error.message}`, error.stack);
      throw error;
    }
  }

  async getTransactionHistory(userId: string, limit = 50, offset = 0): Promise<WalletTransactionDto[]> {
    const wallet = await this.prisma.wallet.findUnique({
      where: { userId },
    });

    if (!wallet) {
      throw new NotFoundException(`Wallet not found for user ${userId}`);
    }

    const transactions = await this.prisma.walletTransaction.findMany({
      where: { walletId: wallet.id },
      orderBy: { createdAt: 'desc' },
      take: limit,
      skip: offset,
      include: { wallet: { include: { user: true } } },
    });

    return transactions.map(transaction => this.mapToTransactionDto(transaction));
  }

  // Private helper methods
  private async createTransaction(
    walletId: string,
    type: WalletTransactionType,
    amount: string,
    description: string,
    balanceBefore: string,
    balanceAfter: string,
    metadata?: any
  ) {
    return this.prisma.walletTransaction.create({
      data: {
        walletId,
        type,
        amount,
        description,
        balanceBefore,
        balanceAfter,
        status: WalletTransactionStatus.COMPLETED,
        processedAt: new Date(),
        metadata,
      },
      include: { wallet: { include: { user: true } } },
    });
  }

  private async validateTransactionLimits(walletId: string, amount: string, type: string): Promise<void> {
    const wallet = await this.prisma.wallet.findUnique({ where: { id: walletId } });
    if (!wallet) return;

    const transactionAmount = this.decimalService.create(amount);

    // Check daily limit
    if (wallet.dailyLimit) {
      const dailyLimit = this.decimalService.create(wallet.dailyLimit);
      const today = new Date();
      today.setHours(0, 0, 0, 0);

      const dailyTransactions = await this.prisma.walletTransaction.findMany({
        where: {
          walletId,
          createdAt: { gte: today },
          type: { in: ['DEPOSIT', 'WITHDRAWAL', 'TRANSFER_OUT'] },
          status: 'COMPLETED',
        },
      });

      const dailyTotal = dailyTransactions.reduce((sum, tx) =>
        sum.add(this.decimalService.create(tx.amount)), this.decimalService.create('0')
      );

      if (dailyTotal.add(transactionAmount).gt(dailyLimit)) {
        throw new BadRequestException('Daily transaction limit exceeded');
      }
    }

    // Check monthly limit
    if (wallet.monthlyLimit) {
      const monthlyLimit = this.decimalService.create(wallet.monthlyLimit);
      const monthStart = new Date();
      monthStart.setDate(1);
      monthStart.setHours(0, 0, 0, 0);

      const monthlyTransactions = await this.prisma.walletTransaction.findMany({
        where: {
          walletId,
          createdAt: { gte: monthStart },
          type: { in: ['DEPOSIT', 'WITHDRAWAL', 'TRANSFER_OUT'] },
          status: 'COMPLETED',
        },
      });

      const monthlyTotal = monthlyTransactions.reduce((sum, tx) =>
        sum.add(this.decimalService.create(tx.amount)), this.decimalService.create('0')
      );

      if (monthlyTotal.add(transactionAmount).gt(monthlyLimit)) {
        throw new BadRequestException('Monthly transaction limit exceeded');
      }
    }
  }

  private async processPaymentGateway(fundWalletDto: FundWalletDto): Promise<any> {
    // Placeholder for payment gateway integration
    return {
      transactionId: `gw_${Date.now()}`,
      status: 'SUCCESS',
    };
  }

  private async processWithdrawalGateway(withdrawFundsDto: WithdrawFundsDto): Promise<any> {
    // Placeholder for withdrawal gateway integration
    return {
      transactionId: `wd_${Date.now()}`,
      status: 'SUCCESS',
    };
  }

  private async getExchangeRate(fromCurrency: string, toCurrency: string): Promise<Decimal> {
    // Get latest exchange rate from database or external API
    const rate = await this.prisma.exchangeRate.findFirst({
      where: { fromCurrency, toCurrency },
      orderBy: { effectiveAt: 'desc' },
    });

    return rate ? this.decimalService.create(rate.rate) : this.decimalService.create('1.00');
  }

  private updateCurrencyBalance(currencyBalances: any, currency: string, amount: string, operation: 'ADD' | 'SUBTRACT'): any {
    const balances = currencyBalances || {};
    const currentBalance = this.decimalService.create(balances[currency] || '0.00');
    const changeAmount = this.decimalService.create(amount);
    
    const newBalance = operation === 'ADD' 
      ? currentBalance.add(changeAmount)
      : currentBalance.sub(changeAmount);
    
    return {
      ...balances,
      [currency]: newBalance.toString(),
    };
  }

  private async notifyWalletCreated(wallet: any): Promise<void> {
    await this.websocketService.sendToUser(wallet.userId, 'wallet_created', {
      message: 'Your digital wallet has been created successfully',
      walletId: wallet.id,
    });

    await this.rabbitMQService.publish({
      type: 'wallet_created',
      data: wallet,
    });
  }

  private async notifyWalletFunded(userId: string, transaction: any): Promise<void> {
    await this.websocketService.sendToUser(userId, 'wallet_funded', {
      message: `Your wallet has been funded with ${transaction.amount}`,
      transactionId: transaction.id,
      amount: transaction.amount,
      newBalance: transaction.balanceAfter,
    });
  }

  private async notifyWalletWithdrawal(userId: string, transaction: any): Promise<void> {
    await this.websocketService.sendToUser(userId, 'wallet_withdrawal', {
      message: `Withdrawal of ${transaction.amount} processed successfully`,
      transactionId: transaction.id,
      amount: transaction.amount,
      newBalance: transaction.balanceAfter,
    });
  }

  private async notifyWalletTransfer(transfer: any): Promise<void> {
    // Notify sender
    await this.websocketService.sendToUser(transfer.senderWallet.userId, 'wallet_transfer_sent', {
      message: `Transfer of ${transfer.amount} sent successfully`,
      transferId: transfer.id,
      amount: transfer.amount,
      recipient: transfer.receiverWallet.user?.firstName,
    });

    // Notify receiver
    await this.websocketService.sendToUser(transfer.receiverWallet.userId, 'wallet_transfer_received', {
      message: `You received ${transfer.convertedAmount} from ${transfer.senderWallet.user?.firstName}`,
      transferId: transfer.id,
      amount: transfer.convertedAmount,
      sender: transfer.senderWallet.user?.firstName,
    });
  }

  private mapToWalletDto(wallet: any): WalletDto {
    return {
      id: wallet.id,
      userId: wallet.userId,
      balance: wallet.balance,
      currency: wallet.currency,
      status: wallet.status,
      dailyLimit: wallet.dailyLimit,
      monthlyLimit: wallet.monthlyLimit,
      isVerified: wallet.isVerified,
      currencyBalances: wallet.currencyBalances,
      lastActivity: wallet.lastActivity,
      createdAt: wallet.createdAt,
      updatedAt: wallet.updatedAt,
    };
  }

  private mapToTransactionDto(transaction: any): WalletTransactionDto {
    return {
      id: transaction.id,
      walletId: transaction.walletId,
      type: transaction.type,
      status: transaction.status,
      amount: transaction.amount,
      currency: transaction.currency,
      balanceBefore: transaction.balanceBefore,
      balanceAfter: transaction.balanceAfter,
      description: transaction.description,
      reference: transaction.reference,
      metadata: transaction.metadata,
      feeAmount: transaction.feeAmount,
      feeDescription: transaction.feeDescription,
      gatewayTransactionId: transaction.gatewayTransactionId,
      processedAt: transaction.processedAt,
      createdAt: transaction.createdAt,
      updatedAt: transaction.updatedAt,
    };
  }

  async updateWalletStatus(userId: string, status: WalletStatus, reason?: string): Promise<WalletDto> {
    try {
      const wallet = await this.prisma.wallet.findUnique({
        where: { userId },
        include: { user: true },
      });

      if (!wallet) {
        throw new NotFoundException(`Wallet not found for user ${userId}`);
      }

      const updatedWallet = await this.prisma.wallet.update({
        where: { userId },
        data: {
          status,
          lastActivity: new Date(),
        },
        include: { user: true },
      });

      // Log status change
      await this.prisma.walletTransaction.create({
        data: {
          walletId: wallet.id,
          type: WalletTransactionType.FEE,
          amount: '0.00',
          description: `Wallet status changed to ${status}${reason ? `: ${reason}` : ''}`,
          balanceBefore: wallet.balance,
          balanceAfter: wallet.balance,
          status: WalletTransactionStatus.COMPLETED,
          processedAt: new Date(),
          metadata: { statusChange: true, oldStatus: wallet.status, newStatus: status, reason },
        },
      });

      // Sync to MongoDB
      await this.mongoDbService.syncDocument('wallets', updatedWallet);

      // Notify user
      await this.notifyWalletStatusChanged(updatedWallet, status, reason);

      return this.mapToWalletDto(updatedWallet);
    } catch (error) {
      this.logger.error(`Failed to update wallet status: ${error.message}`, error.stack);
      throw error;
    }
  }

  async updateWalletLimits(userId: string, dailyLimit?: string, monthlyLimit?: string): Promise<WalletDto> {
    try {
      const wallet = await this.prisma.wallet.findUnique({
        where: { userId },
        include: { user: true },
      });

      if (!wallet) {
        throw new NotFoundException(`Wallet not found for user ${userId}`);
      }

      const updateData: any = { lastActivity: new Date() };
      if (dailyLimit !== undefined) updateData.dailyLimit = dailyLimit;
      if (monthlyLimit !== undefined) updateData.monthlyLimit = monthlyLimit;

      const updatedWallet = await this.prisma.wallet.update({
        where: { userId },
        data: updateData,
        include: { user: true },
      });

      // Log limit change
      await this.prisma.walletTransaction.create({
        data: {
          walletId: wallet.id,
          type: WalletTransactionType.FEE,
          amount: '0.00',
          description: `Wallet limits updated - Daily: ${dailyLimit || 'unchanged'}, Monthly: ${monthlyLimit || 'unchanged'}`,
          balanceBefore: wallet.balance,
          balanceAfter: wallet.balance,
          status: WalletTransactionStatus.COMPLETED,
          processedAt: new Date(),
          metadata: {
            limitChange: true,
            oldDailyLimit: wallet.dailyLimit,
            newDailyLimit: dailyLimit,
            oldMonthlyLimit: wallet.monthlyLimit,
            newMonthlyLimit: monthlyLimit
          },
        },
      });

      // Sync to MongoDB
      await this.mongoDbService.syncDocument('wallets', updatedWallet);

      // Notify user
      await this.notifyWalletLimitsChanged(updatedWallet, dailyLimit, monthlyLimit);

      return this.mapToWalletDto(updatedWallet);
    } catch (error) {
      this.logger.error(`Failed to update wallet limits: ${error.message}`, error.stack);
      throw error;
    }
  }

  async getWalletSummary(): Promise<any> {
    try {
      const [
        totalWallets,
        activeWallets,
        allWallets,
        dailyTransactionsResult,
        dailyTransactions,
        currencyBreakdownResult
      ] = await Promise.all([
        this.prisma.wallet.count(),
        this.prisma.wallet.count({ where: { status: WalletStatus.ACTIVE } }),
        this.prisma.wallet.count(),
        this.prisma.walletTransaction.count({
          where: {
            createdAt: { gte: new Date(new Date().setHours(0, 0, 0, 0)) },
            status: WalletTransactionStatus.COMPLETED
          }
        }),
        this.prisma.walletTransaction.findMany({
          where: {
            createdAt: { gte: new Date(new Date().setHours(0, 0, 0, 0)) },
            status: WalletTransactionStatus.COMPLETED,
            type: { in: [WalletTransactionType.DEPOSIT, WalletTransactionType.WITHDRAWAL, WalletTransactionType.TRANSFER_OUT] }
          },
          select: { amount: true }
        }),
        this.prisma.wallet.findMany({
          where: { status: WalletStatus.ACTIVE },
          select: { balance: true, currency: true, currencyBalances: true }
        })
      ]);

      // Calculate currency breakdown
      const currencyBreakdown: any = {};
      currencyBreakdownResult.forEach(wallet => {
        if (wallet.currencyBalances && typeof wallet.currencyBalances === 'object') {
          Object.entries(wallet.currencyBalances as any).forEach(([currency, balance]) => {
            if (!currencyBreakdown[currency]) currencyBreakdown[currency] = '0.00';
            currencyBreakdown[currency] = this.decimalService.create(currencyBreakdown[currency])
              .add(this.decimalService.create(balance as string)).toString();
          });
        } else {
          const currency = wallet.currency || 'USD';
          if (!currencyBreakdown[currency]) currencyBreakdown[currency] = '0.00';
          currencyBreakdown[currency] = this.decimalService.create(currencyBreakdown[currency])
            .add(this.decimalService.create(wallet.balance)).toString();
        }
      });

      // Calculate total value from all active wallets
      const totalValue = currencyBreakdownResult.reduce((sum, wallet) => {
        return sum.add(this.decimalService.create(wallet.balance));
      }, this.decimalService.create('0')).toString();

      // Calculate daily volume
      const dailyVolume = dailyTransactions.reduce((sum, tx) => {
        return sum.add(this.decimalService.create(tx.amount));
      }, this.decimalService.create('0')).toString();

      const averageBalance = activeWallets > 0
        ? this.decimalService.create(totalValue).div(activeWallets).toString()
        : '0.00';

      return {
        totalWallets,
        activeWallets,
        suspendedWallets: await this.prisma.wallet.count({ where: { status: WalletStatus.SUSPENDED } }),
        frozenWallets: await this.prisma.wallet.count({ where: { status: WalletStatus.FROZEN } }),
        totalValue,
        dailyTransactions: dailyTransactionsResult,
        dailyVolume,
        averageBalance,
        currencyBreakdown,
        lastUpdated: new Date(),
      };
    } catch (error) {
      this.logger.error(`Failed to get wallet summary: ${error.message}`, error.stack);
      throw error;
    }
  }

  private async notifyWalletStatusChanged(wallet: any, status: WalletStatus, reason?: string): Promise<void> {
    const message = `Your wallet status has been changed to ${status}${reason ? `: ${reason}` : ''}`;

    await this.websocketService.sendToUser(wallet.userId, 'wallet_status_changed', {
      message,
      walletId: wallet.id,
      newStatus: status,
      reason,
    });

    await this.rabbitMQService.publish({
      type: 'wallet_status_changed',
      data: { wallet, newStatus: status, reason },
    });
  }

  private async notifyWalletLimitsChanged(wallet: any, dailyLimit?: string, monthlyLimit?: string): Promise<void> {
    const message = `Your wallet transaction limits have been updated`;

    await this.websocketService.sendToUser(wallet.userId, 'wallet_limits_changed', {
      message,
      walletId: wallet.id,
      dailyLimit,
      monthlyLimit,
    });
  }

  private mapToTransferDto(transfer: any): WalletTransferDto {
    return {
      id: transfer.id,
      senderWalletId: transfer.senderWalletId,
      receiverWalletId: transfer.receiverWalletId,
      amount: transfer.amount,
      currency: transfer.currency,
      description: transfer.description,
      reference: transfer.reference,
      status: transfer.status,
      transferFee: transfer.transferFee,
      feePayerId: transfer.feePayerId,
      exchangeRate: transfer.exchangeRate,
      convertedAmount: transfer.convertedAmount,
      initiatedAt: transfer.initiatedAt,
      completedAt: transfer.completedAt,
      createdAt: transfer.createdAt,
      updatedAt: transfer.updatedAt,
    };
  }
}
