import { Injectable, NotFoundException, BadRequestException, Logger } from '@nestjs/common';
import { PrismaService } from '@core/prisma/prisma.service';
import { MongoDbService } from '@infra/mongodb/mongodb.service';
import { RabbitMQService } from '@infra/rabbitmq/rabbitmq.service';
import { WebsocketService } from '@infra/websocket/websocket.service';
import { DecimalService } from '@shared/decimal/decimal.service';
import { LoanStatus, LoanType, CreditScoreGrade } from '@prisma/client';
import { CreateCreditProfileDto, CreateLoanApplicationDto, ProcessLoanApplicationDto } from '../dto/credit.dto';
import { CreditProfileDto, LoanApplicationDto, LoanDto, LoanPaymentDto } from '../dto/credit-response.dto';
import Decimal from 'decimal.js';

@Injectable()
export class CreditService {
  private readonly logger = new Logger(CreditService.name);

  constructor(
    private readonly prisma: PrismaService,
    private readonly mongoDbService: MongoDbService,
    private readonly rabbitMQService: RabbitMQService,
    private readonly websocketService: WebsocketService,
    private readonly decimalService: DecimalService,
  ) {}

  async createCreditProfile(createCreditProfileDto: CreateCreditProfileDto): Promise<CreditProfileDto> {
    try {
      // Check if user already has a credit profile
      const existingProfile = await this.prisma.creditProfile.findUnique({
        where: { userId: createCreditProfileDto.userId },
      });

      if (existingProfile) {
        throw new BadRequestException('User already has a credit profile');
      }

      // Calculate initial credit score based on provided data
      const creditScore = await this.calculateCreditScore(createCreditProfileDto);
      const creditGrade = this.getCreditGrade(creditScore);

      // Determine initial credit limit based on score and income
      const creditLimit = await this.calculateCreditLimit(creditScore, createCreditProfileDto.monthlyIncome);

      const creditProfile = await this.prisma.creditProfile.create({
        data: {
          userId: createCreditProfileDto.userId,
          creditScore,
          creditGrade,
          totalCreditLimit: creditLimit.toString(),
          availableCredit: creditLimit.toString(),
          monthlyIncome: createCreditProfileDto.monthlyIncome,
          incomeVerified: createCreditProfileDto.incomeVerified || false,
          employmentStatus: createCreditProfileDto.employmentStatus,
          riskLevel: this.calculateRiskLevel(creditScore),
          riskFactors: this.identifyRiskFactors(createCreditProfileDto),
          lastUpdated: new Date(),
        },
        include: { user: true },
      });

      // Sync to MongoDB
      await this.mongoDbService.syncDocument('creditProfiles', creditProfile);

      // Notify user
      await this.notifyCreditProfileCreated(creditProfile);

      return this.mapToCreditProfileDto(creditProfile);
    } catch (error) {
      this.logger.error(`Failed to create credit profile: ${error.message}`, error.stack);
      throw error;
    }
  }

  async getCreditProfile(userId: string): Promise<CreditProfileDto> {
    const creditProfile = await this.prisma.creditProfile.findUnique({
      where: { userId },
      include: { user: true, loanApplications: true, loans: true },
    });

    if (!creditProfile) {
      throw new NotFoundException(`Credit profile not found for user ${userId}`);
    }

    return this.mapToCreditProfileDto(creditProfile);
  }

  async createLoanApplication(createLoanApplicationDto: CreateLoanApplicationDto): Promise<LoanApplicationDto> {
    try {
      // Get user's credit profile
      const creditProfile = await this.prisma.creditProfile.findUnique({
        where: { userId: createLoanApplicationDto.userId },
      });

      if (!creditProfile) {
        throw new BadRequestException('User must have a credit profile to apply for loans');
      }

      // Check if user has available credit
      const requestedAmount = this.decimalService.create(createLoanApplicationDto.requestedAmount);
      const availableCredit = this.decimalService.create(creditProfile.availableCredit);

      if (requestedAmount.gt(availableCredit)) {
        throw new BadRequestException('Requested amount exceeds available credit limit');
      }

      // Check for existing pending applications
      const pendingApplications = await this.prisma.loanApplication.count({
        where: {
          userId: createLoanApplicationDto.userId,
          status: { in: [LoanStatus.SUBMITTED, LoanStatus.UNDER_REVIEW] },
        },
      });

      if (pendingApplications > 0) {
        throw new BadRequestException('User has pending loan applications');
      }

      // Create loan application
      const loanApplication = await this.prisma.loanApplication.create({
        data: {
          userId: createLoanApplicationDto.userId,
          creditProfileId: creditProfile.id,
          loanType: createLoanApplicationDto.loanType,
          requestedAmount: createLoanApplicationDto.requestedAmount,
          currency: createLoanApplicationDto.currency || 'USD',
          purpose: createLoanApplicationDto.purpose,
          termMonths: createLoanApplicationDto.termMonths,
          status: LoanStatus.DRAFT,
          documents: createLoanApplicationDto.documents,
        },
        include: { user: true, creditProfile: true },
      });

      // Sync to MongoDB
      await this.mongoDbService.syncDocument('loanApplications', loanApplication);

      return this.mapToLoanApplicationDto(loanApplication);
    } catch (error) {
      this.logger.error(`Failed to create loan application: ${error.message}`, error.stack);
      throw error;
    }
  }

  async submitLoanApplication(applicationId: string): Promise<LoanApplicationDto> {
    try {
      const application = await this.prisma.loanApplication.findUnique({
        where: { id: applicationId },
        include: { user: true, creditProfile: true },
      });

      if (!application) {
        throw new NotFoundException(`Loan application with ID ${applicationId} not found`);
      }

      if (application.status !== LoanStatus.DRAFT) {
        throw new BadRequestException(`Cannot submit application with status: ${application.status}`);
      }

      // Perform risk assessment
      const riskAssessment = await this.performRiskAssessment(application);

      // Update application status
      const updatedApplication = await this.prisma.loanApplication.update({
        where: { id: applicationId },
        data: {
          status: LoanStatus.SUBMITTED,
          submittedAt: new Date(),
          riskAssessment,
        },
        include: { user: true, creditProfile: true },
      });

      // Auto-approve low-risk applications
      if (riskAssessment.autoApprove) {
        return this.processLoanApplication({
          applicationId,
          decision: 'APPROVED',
          approvedAmount: application.requestedAmount,
          interestRate: riskAssessment.recommendedRate,
          reviewedBy: 'SYSTEM',
          notes: 'Auto-approved based on risk assessment',
        });
      }

      // Sync to MongoDB
      await this.mongoDbService.syncDocument('loanApplications', updatedApplication);

      // Notify user and admins
      await this.notifyLoanApplicationSubmitted(updatedApplication);

      return this.mapToLoanApplicationDto(updatedApplication);
    } catch (error) {
      this.logger.error(`Failed to submit loan application: ${error.message}`, error.stack);
      throw error;
    }
  }

  async processLoanApplication(processLoanApplicationDto: ProcessLoanApplicationDto): Promise<LoanApplicationDto> {
    try {
      const application = await this.prisma.loanApplication.findUnique({
        where: { id: processLoanApplicationDto.applicationId },
        include: { user: true, creditProfile: true },
      });

      if (!application) {
        throw new NotFoundException(`Loan application not found`);
      }

      if (application.status !== LoanStatus.SUBMITTED && application.status !== LoanStatus.UNDER_REVIEW) {
        throw new BadRequestException(`Cannot process application with status: ${application.status}`);
      }

      const decision = processLoanApplicationDto.decision;
      const newStatus = decision === 'APPROVED' ? LoanStatus.APPROVED : LoanStatus.REJECTED;

      // Update application
      const updatedApplication = await this.prisma.loanApplication.update({
        where: { id: processLoanApplicationDto.applicationId },
        data: {
          status: newStatus,
          reviewedAt: new Date(),
          reviewedBy: processLoanApplicationDto.reviewedBy,
          approvedAmount: processLoanApplicationDto.approvedAmount,
          interestRate: processLoanApplicationDto.interestRate,
          rejectionReason: processLoanApplicationDto.rejectionReason,
        },
        include: { user: true, creditProfile: true },
      });

      // If approved, create loan
      if (decision === 'APPROVED') {
        await this.createLoanFromApplication(updatedApplication, processLoanApplicationDto);
      }

      // Sync to MongoDB
      await this.mongoDbService.syncDocument('loanApplications', updatedApplication);

      // Notify user
      await this.notifyLoanApplicationProcessed(updatedApplication, decision);

      return this.mapToLoanApplicationDto(updatedApplication);
    } catch (error) {
      this.logger.error(`Failed to process loan application: ${error.message}`, error.stack);
      throw error;
    }
  }

  async getLoanApplications(userId?: string, status?: LoanStatus): Promise<LoanApplicationDto[]> {
    const where: any = {};
    if (userId) where.userId = userId;
    if (status) where.status = status;

    const applications = await this.prisma.loanApplication.findMany({
      where,
      include: { user: true, creditProfile: true },
      orderBy: { createdAt: 'desc' },
    });

    return applications.map(app => this.mapToLoanApplicationDto(app));
  }

  async getUserLoans(userId: string): Promise<LoanDto[]> {
    const loans = await this.prisma.loan.findMany({
      where: { userId },
      include: { user: true, creditProfile: true, payments: true },
      orderBy: { createdAt: 'desc' },
    });

    return loans.map(loan => this.mapToLoanDto(loan));
  }

  async processLoanPayment(loanId: string, amount: string, paymentMethod: string): Promise<LoanPaymentDto> {
    try {
      const loan = await this.prisma.loan.findUnique({
        where: { id: loanId },
        include: { user: true, creditProfile: true },
      });

      if (!loan) {
        throw new NotFoundException(`Loan with ID ${loanId} not found`);
      }

      if (loan.status !== LoanStatus.ACTIVE) {
        throw new BadRequestException(`Cannot process payment for loan with status: ${loan.status}`);
      }

      const paymentAmount = this.decimalService.create(amount);
      const currentBalance = this.decimalService.create(loan.currentBalance);
      const monthlyPayment = this.decimalService.create(loan.monthlyPayment);

      // Calculate payment allocation (principal vs interest)
      const { principalAmount, interestAmount, lateFeeAmount } = this.calculatePaymentAllocation(
        loan,
        paymentAmount
      );

      // Create payment record
      const payment = await this.prisma.loanPayment.create({
        data: {
          loanId,
          amount: amount,
          principalAmount: principalAmount.toString(),
          interestAmount: interestAmount.toString(),
          lateFeeAmount: lateFeeAmount.toString(),
          dueDate: loan.nextPaymentDate || new Date(),
          paidDate: new Date(),
          status: 'PAID',
          paymentMethod,
          balanceBefore: loan.currentBalance,
          balanceAfter: currentBalance.sub(principalAmount).toString(),
        },
      });

      // Update loan
      const newBalance = currentBalance.sub(principalAmount);
      const totalPaid = this.decimalService.create(loan.totalPaid).add(paymentAmount);
      
      await this.prisma.loan.update({
        where: { id: loanId },
        data: {
          currentBalance: newBalance.toString(),
          totalPaid: totalPaid.toString(),
          lastPaymentDate: new Date(),
          nextPaymentDate: this.calculateNextPaymentDate(loan.nextPaymentDate),
          status: newBalance.lte(0) ? LoanStatus.COMPLETED : LoanStatus.ACTIVE,
          closedAt: newBalance.lte(0) ? new Date() : null,
        },
      });

      // Update credit profile available credit
      await this.updateAvailableCredit(loan.creditProfileId, principalAmount.toString());

      // Sync to MongoDB
      await this.mongoDbService.syncDocument('loanPayments', payment);

      // Notify user
      await this.notifyLoanPaymentProcessed(loan.userId, payment);

      return this.mapToLoanPaymentDto(payment);
    } catch (error) {
      this.logger.error(`Failed to process loan payment: ${error.message}`, error.stack);
      throw error;
    }
  }

  // Private helper methods
  private async calculateCreditScore(profileData: CreateCreditProfileDto): Promise<number> {
    // Simplified credit scoring algorithm
    let score = 600; // Base score

    // Income factor
    if (profileData.monthlyIncome) {
      const income = this.decimalService.create(profileData.monthlyIncome);
      if (income.gte(5000)) score += 100;
      else if (income.gte(3000)) score += 50;
      else if (income.gte(1000)) score += 25;
    }

    // Employment status
    if (profileData.employmentStatus === 'EMPLOYED') score += 50;
    else if (profileData.employmentStatus === 'SELF_EMPLOYED') score += 25;

    // Income verification
    if (profileData.incomeVerified) score += 25;

    // Cap the score
    return Math.min(850, Math.max(300, score));
  }

  private getCreditGrade(score: number): CreditScoreGrade {
    if (score >= 750) return CreditScoreGrade.EXCELLENT;
    if (score >= 650) return CreditScoreGrade.GOOD;
    if (score >= 550) return CreditScoreGrade.FAIR;
    if (score >= 350) return CreditScoreGrade.POOR;
    return CreditScoreGrade.NO_CREDIT;
  }

  private async calculateCreditLimit(creditScore: number, monthlyIncome?: string): Promise<Decimal> {
    let baseLimit = this.decimalService.create('1000'); // Base limit

    // Score-based multiplier
    if (creditScore >= 750) baseLimit = baseLimit.mul(5);
    else if (creditScore >= 650) baseLimit = baseLimit.mul(3);
    else if (creditScore >= 550) baseLimit = baseLimit.mul(2);

    // Income-based adjustment
    if (monthlyIncome) {
      const income = this.decimalService.create(monthlyIncome);
      const incomeMultiplier = income.mul(0.3); // 30% of monthly income
      baseLimit = baseLimit.add(incomeMultiplier);
    }

    return baseLimit;
  }

  private calculateRiskLevel(creditScore: number): string {
    if (creditScore >= 700) return 'LOW';
    if (creditScore >= 600) return 'MEDIUM';
    return 'HIGH';
  }

  private identifyRiskFactors(profileData: CreateCreditProfileDto): string[] {
    const factors: string[] = [];

    if (!profileData.incomeVerified) factors.push('UNVERIFIED_INCOME');
    if (profileData.employmentStatus === 'UNEMPLOYED') factors.push('UNEMPLOYED');
    if (!profileData.monthlyIncome || this.decimalService.create(profileData.monthlyIncome).lt(1000)) {
      factors.push('LOW_INCOME');
    }

    return factors;
  }

  private async performRiskAssessment(application: any): Promise<any> {
    const creditProfile = application.creditProfile;
    const requestedAmount = this.decimalService.create(application.requestedAmount);
    const availableCredit = this.decimalService.create(creditProfile.availableCredit);

    const assessment = {
      creditScore: creditProfile.creditScore,
      riskLevel: creditProfile.riskLevel,
      debtToIncomeRatio: this.calculateDebtToIncomeRatio(application.userId, requestedAmount),
      utilizationRatio: requestedAmount.div(availableCredit).mul(100).toNumber(),
      autoApprove: false,
      recommendedRate: '12.00', // Default rate
    };

    // Auto-approve criteria
    if (
      creditProfile.creditScore >= 700 &&
      assessment.utilizationRatio <= 50 &&
      requestedAmount.lte(5000)
    ) {
      assessment.autoApprove = true;
      assessment.recommendedRate = '8.00'; // Better rate for auto-approved
    }

    return assessment;
  }

  private async calculateDebtToIncomeRatio(userId: string, newDebt: Decimal): Promise<number> {
    // Calculate existing debt from active loans
    const activeLoans = await this.prisma.loan.findMany({
      where: { userId, status: LoanStatus.ACTIVE },
    });

    const existingDebt = activeLoans.reduce((sum, loan) => 
      sum.add(this.decimalService.create(loan.monthlyPayment)), 
      this.decimalService.create('0')
    );

    // Estimate new monthly payment (simplified)
    const estimatedNewPayment = newDebt.div(12); // Assume 12-month term
    const totalDebt = existingDebt.add(estimatedNewPayment);

    // Get user's income
    const creditProfile = await this.prisma.creditProfile.findUnique({
      where: { userId },
    });

    if (!creditProfile?.monthlyIncome) return 100; // High ratio if no income data

    const monthlyIncome = this.decimalService.create(creditProfile.monthlyIncome);
    return totalDebt.div(monthlyIncome).mul(100).toNumber();
  }

  private async createLoanFromApplication(application: any, processDto: ProcessLoanApplicationDto): Promise<void> {
    const approvedAmount = this.decimalService.create(processDto.approvedAmount!);
    const interestRate = this.decimalService.create(processDto.interestRate!);
    const termMonths = application.termMonths;

    // Calculate monthly payment
    const monthlyPayment = this.calculateMonthlyPayment(approvedAmount, interestRate, termMonths);

    // Create loan
    await this.prisma.loan.create({
      data: {
        userId: application.userId,
        creditProfileId: application.creditProfileId,
        applicationId: application.id,
        loanType: application.loanType,
        principalAmount: processDto.approvedAmount!,
        currency: application.currency,
        interestRate: processDto.interestRate!,
        termMonths,
        currentBalance: processDto.approvedAmount!,
        monthlyPayment: monthlyPayment.toString(),
        nextPaymentDate: this.calculateNextPaymentDate(new Date()),
        maturityDate: this.calculateMaturityDate(new Date(), termMonths),
        disbursedAt: new Date(),
      },
    });

    // Update credit profile
    await this.prisma.creditProfile.update({
      where: { id: application.creditProfileId },
      data: {
        availableCredit: this.decimalService.create(application.creditProfile.availableCredit)
          .sub(approvedAmount).toString(),
        usedCredit: this.decimalService.create(application.creditProfile.usedCredit)
          .add(approvedAmount).toString(),
      },
    });
  }

  private calculateMonthlyPayment(principal: Decimal, annualRate: Decimal, termMonths: number): Decimal {
    const monthlyRate = annualRate.div(100).div(12);
    const numerator = principal.mul(monthlyRate).mul(monthlyRate.add(1).pow(termMonths));
    const denominator = monthlyRate.add(1).pow(termMonths).sub(1);
    return numerator.div(denominator);
  }

  private calculateNextPaymentDate(currentDate: Date | null): Date {
    const date = currentDate ? new Date(currentDate) : new Date();
    date.setMonth(date.getMonth() + 1);
    return date;
  }

  private calculateMaturityDate(startDate: Date, termMonths: number): Date {
    const date = new Date(startDate);
    date.setMonth(date.getMonth() + termMonths);
    return date;
  }

  private calculatePaymentAllocation(loan: any, paymentAmount: Decimal): {
    principalAmount: Decimal;
    interestAmount: Decimal;
    lateFeeAmount: Decimal;
  } {
    // Simplified payment allocation
    const currentBalance = this.decimalService.create(loan.currentBalance);
    const monthlyRate = this.decimalService.create(loan.interestRate).div(100).div(12);
    
    const interestAmount = currentBalance.mul(monthlyRate);
    const lateFeeAmount = this.decimalService.create(loan.totalLateFees || '0');
    const principalAmount = paymentAmount.sub(interestAmount).sub(lateFeeAmount);

    return {
      principalAmount: principalAmount.gt(0) ? principalAmount : this.decimalService.create('0'),
      interestAmount,
      lateFeeAmount,
    };
  }

  private async updateAvailableCredit(creditProfileId: string, amount: string): Promise<void> {
    const creditProfile = await this.prisma.creditProfile.findUnique({
      where: { id: creditProfileId },
    });

    if (creditProfile) {
      const newAvailableCredit = this.decimalService.create(creditProfile.availableCredit)
        .add(this.decimalService.create(amount));
      
      await this.prisma.creditProfile.update({
        where: { id: creditProfileId },
        data: {
          availableCredit: newAvailableCredit.toString(),
          usedCredit: this.decimalService.create(creditProfile.usedCredit)
            .sub(this.decimalService.create(amount)).toString(),
        },
      });
    }
  }

  // Notification methods
  private async notifyCreditProfileCreated(creditProfile: any): Promise<void> {
    await this.websocketService.sendToUser(creditProfile.userId, 'credit_profile_created', {
      message: 'Your credit profile has been created',
      creditScore: creditProfile.creditScore,
      creditLimit: creditProfile.totalCreditLimit,
    });

    await this.rabbitMQService.publish({
      type: 'credit_profile_created',
      data: creditProfile,
    });
  }

  private async notifyLoanApplicationSubmitted(application: any): Promise<void> {
    await this.websocketService.sendToUser(application.userId, 'loan_application_submitted', {
      message: 'Your loan application has been submitted for review',
      applicationId: application.id,
      requestedAmount: application.requestedAmount,
    });
  }

  private async notifyLoanApplicationProcessed(application: any, decision: string): Promise<void> {
    const message = decision === 'APPROVED' 
      ? `Your loan application has been approved for ${application.approvedAmount}`
      : `Your loan application has been rejected: ${application.rejectionReason}`;

    await this.websocketService.sendToUser(application.userId, 'loan_application_processed', {
      message,
      applicationId: application.id,
      decision,
      approvedAmount: application.approvedAmount,
    });
  }

  private async notifyLoanPaymentProcessed(userId: string, payment: any): Promise<void> {
    await this.websocketService.sendToUser(userId, 'loan_payment_processed', {
      message: `Loan payment of ${payment.amount} has been processed`,
      paymentId: payment.id,
      amount: payment.amount,
      newBalance: payment.balanceAfter,
    });
  }

  // Mapping methods
  private mapToCreditProfileDto(creditProfile: any): CreditProfileDto {
    return {
      id: creditProfile.id,
      userId: creditProfile.userId,
      creditScore: creditProfile.creditScore,
      creditGrade: creditProfile.creditGrade,
      totalCreditLimit: creditProfile.totalCreditLimit,
      availableCredit: creditProfile.availableCredit,
      usedCredit: creditProfile.usedCredit,
      riskLevel: creditProfile.riskLevel,
      riskFactors: creditProfile.riskFactors,
      monthlyIncome: creditProfile.monthlyIncome,
      incomeVerified: creditProfile.incomeVerified,
      employmentStatus: creditProfile.employmentStatus,
      paymentHistory: creditProfile.paymentHistory,
      defaultHistory: creditProfile.defaultHistory,
      lastUpdated: creditProfile.lastUpdated,
      createdAt: creditProfile.createdAt,
      updatedAt: creditProfile.updatedAt,
    };
  }

  private mapToLoanApplicationDto(application: any): LoanApplicationDto {
    return {
      id: application.id,
      userId: application.userId,
      creditProfileId: application.creditProfileId,
      loanType: application.loanType,
      requestedAmount: application.requestedAmount,
      currency: application.currency,
      purpose: application.purpose,
      termMonths: application.termMonths,
      status: application.status,
      submittedAt: application.submittedAt,
      reviewedAt: application.reviewedAt,
      reviewedBy: application.reviewedBy,
      approvedAmount: application.approvedAmount,
      interestRate: application.interestRate,
      rejectionReason: application.rejectionReason,
      documents: application.documents,
      riskAssessment: application.riskAssessment,
      createdAt: application.createdAt,
      updatedAt: application.updatedAt,
    };
  }

  private mapToLoanDto(loan: any): LoanDto {
    return {
      id: loan.id,
      userId: loan.userId,
      creditProfileId: loan.creditProfileId,
      applicationId: loan.applicationId,
      loanType: loan.loanType,
      principalAmount: loan.principalAmount,
      currency: loan.currency,
      interestRate: loan.interestRate,
      termMonths: loan.termMonths,
      status: loan.status,
      currentBalance: loan.currentBalance,
      totalPaid: loan.totalPaid,
      monthlyPayment: loan.monthlyPayment,
      nextPaymentDate: loan.nextPaymentDate,
      lastPaymentDate: loan.lastPaymentDate,
      lateFeeRate: loan.lateFeeRate,
      totalLateFees: loan.totalLateFees,
      totalInterestPaid: loan.totalInterestPaid,
      disbursedAt: loan.disbursedAt,
      maturityDate: loan.maturityDate,
      closedAt: loan.closedAt,
      daysOverdue: loan.daysOverdue,
      defaultedAt: loan.defaultedAt,
      createdAt: loan.createdAt,
      updatedAt: loan.updatedAt,
    };
  }

  private mapToLoanPaymentDto(payment: any): LoanPaymentDto {
    return {
      id: payment.id,
      loanId: payment.loanId,
      amount: payment.amount,
      currency: payment.currency,
      principalAmount: payment.principalAmount,
      interestAmount: payment.interestAmount,
      lateFeeAmount: payment.lateFeeAmount,
      dueDate: payment.dueDate,
      paidDate: payment.paidDate,
      status: payment.status,
      paymentMethod: payment.paymentMethod,
      transactionId: payment.transactionId,
      balanceBefore: payment.balanceBefore,
      balanceAfter: payment.balanceAfter,
      createdAt: payment.createdAt,
      updatedAt: payment.updatedAt,
    };
  }
}
