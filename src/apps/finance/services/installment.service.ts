import { Injectable, NotFoundException, BadRequestException, Logger } from '@nestjs/common';
import { PrismaService } from '@core/prisma/prisma.service';
import { MongoDbService } from '@infra/mongodb/mongodb.service';
import { RabbitMQService } from '@infra/rabbitmq/rabbitmq.service';
import { WebsocketService } from '@infra/websocket/websocket.service';
import { DecimalService } from '@shared/decimal/decimal.service';
import { InstallmentPlanStatus, InstallmentStatus } from '@prisma/client';
import { CreateInstallmentPlanDto, ProcessInstallmentPaymentDto } from '../dto/installment.dto';
import { InstallmentPlanDto, InstallmentDto, InstallmentSummaryDto } from '../dto/installment-response.dto';
import Decimal from 'decimal.js';

@Injectable()
export class InstallmentService {
  private readonly logger = new Logger(InstallmentService.name);

  constructor(
    private readonly prisma: PrismaService,
    private readonly mongoDbService: MongoDbService,
    private readonly rabbitMQService: RabbitMQService,
    private readonly websocketService: WebsocketService,
    private readonly decimalService: DecimalService,
  ) {}

  async createInstallmentPlan(createInstallmentPlanDto: CreateInstallmentPlanDto): Promise<InstallmentPlanDto> {
    try {
      // Check if order already has an installment plan
      const existingPlan = await this.prisma.installmentPlan.findUnique({
        where: { orderId: createInstallmentPlanDto.orderId },
      });

      if (existingPlan) {
        throw new BadRequestException('Order already has an installment plan');
      }

      // Validate order exists
      const order = await this.prisma.order.findUnique({
        where: { id: createInstallmentPlanDto.orderId },
        include: { customer: true },
      });

      if (!order) {
        throw new NotFoundException(`Order with ID ${createInstallmentPlanDto.orderId} not found`);
      }

      // Calculate installment details
      const totalAmount = this.decimalService.create(createInstallmentPlanDto.totalAmount);
      const numberOfInstallments = createInstallmentPlanDto.numberOfInstallments;
      const interestRate = this.decimalService.create(createInstallmentPlanDto.interestRate || '0.00');
      const processingFee = this.decimalService.create(createInstallmentPlanDto.processingFee || '0.00');

      // Calculate total amount with interest and fees
      const totalWithInterest = this.calculateTotalWithInterest(totalAmount, interestRate, numberOfInstallments);
      const totalWithFees = totalWithInterest.add(processingFee);
      const installmentAmount = totalWithFees.div(numberOfInstallments);

      // Create installment plan
      const installmentPlan = await this.prisma.installmentPlan.create({
        data: {
          orderId: createInstallmentPlanDto.orderId,
          userId: order.customerId,
          totalAmount: createInstallmentPlanDto.totalAmount,
          currency: createInstallmentPlanDto.currency || 'USD',
          numberOfInstallments,
          installmentAmount: installmentAmount.toString(),
          interestRate: createInstallmentPlanDto.interestRate || '0.00',
          processingFee: createInstallmentPlanDto.processingFee || '0.00',
          firstPaymentDate: createInstallmentPlanDto.firstPaymentDate,
          paymentFrequency: createInstallmentPlanDto.paymentFrequency || 'MONTHLY',
          earlyPaymentDiscount: createInstallmentPlanDto.earlyPaymentDiscount || '0.00',
          lateFeeRate: createInstallmentPlanDto.lateFeeRate || '0.05',
          gracePeriodDays: createInstallmentPlanDto.gracePeriodDays || 3,
          activatedAt: new Date(),
        },
        include: { order: true, user: true },
      });

      // Create individual installments
      await this.createInstallments(installmentPlan);

      // Sync to MongoDB
      await this.mongoDbService.syncDocument('installmentPlans', installmentPlan);

      // Notify user
      await this.notifyInstallmentPlanCreated(installmentPlan);

      return this.mapToInstallmentPlanDto(installmentPlan);
    } catch (error) {
      this.logger.error(`Failed to create installment plan: ${error.message}`, error.stack);
      throw error;
    }
  }

  async getInstallmentPlan(planId: string): Promise<InstallmentPlanDto> {
    const plan = await this.prisma.installmentPlan.findUnique({
      where: { id: planId },
      include: {
        order: true,
        user: true,
        installments: { orderBy: { installmentNumber: 'asc' } },
      },
    });

    if (!plan) {
      throw new NotFoundException(`Installment plan with ID ${planId} not found`);
    }

    return this.mapToInstallmentPlanDto(plan);
  }

  async getInstallmentPlanByOrderId(orderId: string): Promise<InstallmentPlanDto | null> {
    const plan = await this.prisma.installmentPlan.findUnique({
      where: { orderId },
      include: {
        order: true,
        user: true,
        installments: { orderBy: { installmentNumber: 'asc' } },
      },
    });

    return plan ? this.mapToInstallmentPlanDto(plan) : null;
  }

  async getUserInstallmentPlans(userId: string): Promise<InstallmentPlanDto[]> {
    const plans = await this.prisma.installmentPlan.findMany({
      where: { userId },
      include: {
        order: true,
        user: true,
        installments: { orderBy: { installmentNumber: 'asc' } },
      },
      orderBy: { createdAt: 'desc' },
    });

    return plans.map(plan => this.mapToInstallmentPlanDto(plan));
  }

  async processInstallmentPayment(processPaymentDto: ProcessInstallmentPaymentDto): Promise<InstallmentDto> {
    try {
      const installment = await this.prisma.installment.findUnique({
        where: { id: processPaymentDto.installmentId },
        include: { plan: { include: { order: true, user: true } } },
      });

      if (!installment) {
        throw new NotFoundException(`Installment with ID ${processPaymentDto.installmentId} not found`);
      }

      if (installment.status !== InstallmentStatus.PENDING) {
        throw new BadRequestException(`Cannot process installment with status: ${installment.status}`);
      }

      const paymentAmount = this.decimalService.create(processPaymentDto.amount);
      const installmentAmount = this.decimalService.create(installment.amount);

      // Check if payment amount matches installment amount (allow small variance)
      if (paymentAmount.lt(installmentAmount.mul(0.99))) {
        throw new BadRequestException('Payment amount is insufficient for this installment');
      }

      // Calculate late fee if applicable
      const lateFee = this.calculateLateFee(installment);
      const totalDue = installmentAmount.add(lateFee);

      if (paymentAmount.lt(totalDue)) {
        throw new BadRequestException(`Payment amount insufficient. Total due: ${totalDue.toString()} (including late fee: ${lateFee.toString()})`);
      }

      // Process payment
      const updatedInstallment = await this.prisma.installment.update({
        where: { id: processPaymentDto.installmentId },
        data: {
          status: InstallmentStatus.PAID,
          paidDate: new Date(),
          paymentMethod: processPaymentDto.paymentMethod,
          transactionId: processPaymentDto.transactionId,
          lateFeeAmount: lateFee.toString(),
        },
        include: { plan: { include: { order: true, user: true } } },
      });

      // Update plan totals
      await this.updatePlanTotals(installment.planId, paymentAmount, lateFee);

      // Check if plan is completed
      await this.checkPlanCompletion(installment.planId);

      // Process escrow release if applicable
      if (processPaymentDto.releaseEscrow) {
        await this.processEscrowRelease(installment.plan, updatedInstallment);
      }

      // Sync to MongoDB
      await this.mongoDbService.syncDocument('installments', updatedInstallment);

      // Notify user
      await this.notifyInstallmentPaymentProcessed(updatedInstallment);

      return this.mapToInstallmentDto(updatedInstallment);
    } catch (error) {
      this.logger.error(`Failed to process installment payment: ${error.message}`, error.stack);
      throw error;
    }
  }

  async getUpcomingInstallments(userId: string, days = 7): Promise<InstallmentDto[]> {
    const futureDate = new Date();
    futureDate.setDate(futureDate.getDate() + days);

    const installments = await this.prisma.installment.findMany({
      where: {
        plan: { userId },
        status: InstallmentStatus.PENDING,
        dueDate: {
          lte: futureDate,
        },
      },
      include: { plan: { include: { order: true, user: true } } },
      orderBy: { dueDate: 'asc' },
    });

    return installments.map(installment => this.mapToInstallmentDto(installment));
  }

  async getOverdueInstallments(userId?: string): Promise<InstallmentDto[]> {
    const where: any = {
      status: InstallmentStatus.PENDING,
      dueDate: { lt: new Date() },
    };

    if (userId) {
      where.plan = { userId };
    }

    const installments = await this.prisma.installment.findMany({
      where,
      include: { plan: { include: { order: true, user: true } } },
      orderBy: { dueDate: 'asc' },
    });

    // Update status to overdue
    const overdueIds = installments.map(i => i.id);
    if (overdueIds.length > 0) {
      await this.prisma.installment.updateMany({
        where: { id: { in: overdueIds } },
        data: { status: InstallmentStatus.OVERDUE },
      });
    }

    return installments.map(installment => this.mapToInstallmentDto({
      ...installment,
      status: InstallmentStatus.OVERDUE,
    }));
  }

  async sendPaymentReminders(): Promise<void> {
    // Get installments due in 3 days
    const reminderDate = new Date();
    reminderDate.setDate(reminderDate.getDate() + 3);

    const upcomingInstallments = await this.prisma.installment.findMany({
      where: {
        status: InstallmentStatus.PENDING,
        dueDate: {
          gte: new Date(),
          lte: reminderDate,
        },
        remindersSent: { lt: 3 }, // Max 3 reminders
      },
      include: { plan: { include: { order: true, user: true } } },
    });

    for (const installment of upcomingInstallments) {
      await this.sendPaymentReminder(installment);
    }
  }

  async processEarlyPayment(planId: string, paymentAmount: string): Promise<InstallmentPlanDto> {
    try {
      const plan = await this.prisma.installmentPlan.findUnique({
        where: { id: planId },
        include: {
          installments: { where: { status: InstallmentStatus.PENDING }, orderBy: { installmentNumber: 'asc' } },
          order: true,
          user: true,
        },
      });

      if (!plan) {
        throw new NotFoundException(`Installment plan with ID ${planId} not found`);
      }

      const paymentAmountDecimal = this.decimalService.create(paymentAmount);
      const earlyPaymentDiscount = this.decimalService.create(plan.earlyPaymentDiscount);
      
      // Calculate remaining balance
      const remainingBalance = plan.installments.reduce((sum, installment) => 
        sum.add(this.decimalService.create(installment.amount)), 
        this.decimalService.create('0')
      );

      // Apply early payment discount
      const discountAmount = remainingBalance.mul(earlyPaymentDiscount.div(100));
      const finalPaymentAmount = remainingBalance.sub(discountAmount);

      if (paymentAmountDecimal.lt(finalPaymentAmount)) {
        throw new BadRequestException(`Insufficient payment for early settlement. Required: ${finalPaymentAmount.toString()}`);
      }

      // Mark all pending installments as paid
      await this.prisma.installment.updateMany({
        where: {
          planId,
          status: InstallmentStatus.PENDING,
        },
        data: {
          status: InstallmentStatus.PAID,
          paidDate: new Date(),
          paymentMethod: 'EARLY_PAYMENT',
        },
      });

      // Update plan status
      const updatedPlan = await this.prisma.installmentPlan.update({
        where: { id: planId },
        data: {
          status: InstallmentPlanStatus.COMPLETED,
          completedAt: new Date(),
          totalPaid: this.decimalService.create(plan.totalPaid).add(paymentAmountDecimal).toString(),
        },
        include: {
          installments: { orderBy: { installmentNumber: 'asc' } },
          order: true,
          user: true,
        },
      });

      // Notify user about early payment completion
      await this.notifyEarlyPaymentCompleted(updatedPlan, discountAmount.toString());

      return this.mapToInstallmentPlanDto(updatedPlan);
    } catch (error) {
      this.logger.error(`Failed to process early payment: ${error.message}`, error.stack);
      throw error;
    }
  }

  // Private helper methods
  private calculateTotalWithInterest(principal: Decimal, annualRate: Decimal, months: number): Decimal {
    if (annualRate.eq(0)) return principal;
    
    const monthlyRate = annualRate.div(100).div(12);
    const totalInterest = principal.mul(monthlyRate).mul(months);
    return principal.add(totalInterest);
  }

  private async createInstallments(plan: any): Promise<void> {
    const installmentAmount = this.decimalService.create(plan.installmentAmount);
    const firstPaymentDate = new Date(plan.firstPaymentDate);

    for (let i = 1; i <= plan.numberOfInstallments; i++) {
      const dueDate = new Date(firstPaymentDate);
      
      if (plan.paymentFrequency === 'MONTHLY') {
        dueDate.setMonth(dueDate.getMonth() + (i - 1));
      } else if (plan.paymentFrequency === 'WEEKLY') {
        dueDate.setDate(dueDate.getDate() + (i - 1) * 7);
      }

      await this.prisma.installment.create({
        data: {
          planId: plan.id,
          installmentNumber: i,
          amount: installmentAmount.toString(),
          currency: plan.currency,
          principalAmount: installmentAmount.toString(), // Simplified - could be more complex
          dueDate,
        },
      });
    }
  }

  private calculateLateFee(installment: any): Decimal {
    const currentDate = new Date();
    const dueDate = new Date(installment.dueDate);
    const gracePeriodEnd = new Date(dueDate);
    gracePeriodEnd.setDate(gracePeriodEnd.getDate() + installment.plan.gracePeriodDays);

    if (currentDate <= gracePeriodEnd) {
      return this.decimalService.create('0');
    }

    const installmentAmount = this.decimalService.create(installment.amount);
    const lateFeeRate = this.decimalService.create(installment.plan.lateFeeRate);
    
    return installmentAmount.mul(lateFeeRate);
  }

  private async updatePlanTotals(planId: string, paymentAmount: Decimal, lateFee: Decimal): Promise<void> {
    const plan = await this.prisma.installmentPlan.findUnique({ where: { id: planId } });
    if (!plan) return;

    const newTotalPaid = this.decimalService.create(plan.totalPaid).add(paymentAmount);
    const newTotalLateFees = this.decimalService.create(plan.totalLateFees).add(lateFee);

    await this.prisma.installmentPlan.update({
      where: { id: planId },
      data: {
        totalPaid: newTotalPaid.toString(),
        totalLateFees: newTotalLateFees.toString(),
      },
    });
  }

  private async checkPlanCompletion(planId: string): Promise<void> {
    const pendingInstallments = await this.prisma.installment.count({
      where: {
        planId,
        status: InstallmentStatus.PENDING,
      },
    });

    if (pendingInstallments === 0) {
      await this.prisma.installmentPlan.update({
        where: { id: planId },
        data: {
          status: InstallmentPlanStatus.COMPLETED,
          completedAt: new Date(),
        },
      });
    }
  }

  private async processEscrowRelease(plan: any, installment: any): Promise<void> {
    // Check if this is the final installment
    const remainingInstallments = await this.prisma.installment.count({
      where: {
        planId: plan.id,
        status: InstallmentStatus.PENDING,
      },
    });

    if (remainingInstallments === 0) {
      // Release full escrow amount
      // This would integrate with the EscrowService
      this.logger.log(`Releasing escrow for completed installment plan: ${plan.id}`);
    } else {
      // Release partial escrow amount
      const releaseAmount = this.decimalService.create(installment.amount);
      this.logger.log(`Releasing partial escrow amount: ${releaseAmount.toString()} for installment: ${installment.id}`);
    }
  }

  private async sendPaymentReminder(installment: any): Promise<void> {
    await this.prisma.paymentReminder.create({
      data: {
        userId: installment.plan.userId,
        installmentId: installment.id,
        type: 'INSTALLMENT',
        title: 'Upcoming Payment Due',
        message: `Your installment payment of ${installment.amount} is due on ${installment.dueDate.toDateString()}`,
        amount: installment.amount,
        dueDate: installment.dueDate,
      },
    });

    // Update reminder count
    await this.prisma.installment.update({
      where: { id: installment.id },
      data: {
        remindersSent: installment.remindersSent + 1,
        lastReminderAt: new Date(),
      },
    });

    // Send notification
    await this.websocketService.sendToUser(installment.plan.userId, 'payment_reminder', {
      message: `Payment reminder: ${installment.amount} due on ${installment.dueDate.toDateString()}`,
      installmentId: installment.id,
      amount: installment.amount,
      dueDate: installment.dueDate,
    });
  }

  private async notifyInstallmentPlanCreated(plan: any): Promise<void> {
    await this.websocketService.sendToUser(plan.userId, 'installment_plan_created', {
      message: `Installment plan created for order ${plan.orderId}`,
      planId: plan.id,
      numberOfInstallments: plan.numberOfInstallments,
      installmentAmount: plan.installmentAmount,
    });

    await this.rabbitMQService.publish({
      type: 'installment_plan_created',
      data: plan,
    });
  }

  private async notifyInstallmentPaymentProcessed(installment: any): Promise<void> {
    await this.websocketService.sendToUser(installment.plan.userId, 'installment_payment_processed', {
      message: `Installment payment of ${installment.amount} processed successfully`,
      installmentId: installment.id,
      amount: installment.amount,
      remainingInstallments: await this.prisma.installment.count({
        where: { planId: installment.planId, status: InstallmentStatus.PENDING },
      }),
    });
  }

  private async notifyEarlyPaymentCompleted(plan: any, discountAmount: string): Promise<void> {
    await this.websocketService.sendToUser(plan.userId, 'early_payment_completed', {
      message: `Early payment completed with discount of ${discountAmount}`,
      planId: plan.id,
      discountAmount,
      totalSaved: discountAmount,
    });
  }

  // Mapping methods
  private mapToInstallmentPlanDto(plan: any): InstallmentPlanDto {
    return {
      id: plan.id,
      orderId: plan.orderId,
      userId: plan.userId,
      totalAmount: plan.totalAmount,
      currency: plan.currency,
      numberOfInstallments: plan.numberOfInstallments,
      installmentAmount: plan.installmentAmount,
      status: plan.status,
      interestRate: plan.interestRate,
      processingFee: plan.processingFee,
      firstPaymentDate: plan.firstPaymentDate,
      paymentFrequency: plan.paymentFrequency,
      earlyPaymentDiscount: plan.earlyPaymentDiscount,
      lateFeeRate: plan.lateFeeRate,
      gracePeriodDays: plan.gracePeriodDays,
      totalPaid: plan.totalPaid,
      totalLateFees: plan.totalLateFees,
      totalInterest: plan.totalInterest,
      createdAt: plan.createdAt,
      activatedAt: plan.activatedAt,
      completedAt: plan.completedAt,
      cancelledAt: plan.cancelledAt,
      installments: plan.installments?.map((i: any) => this.mapToInstallmentDto(i)) || [],
    };
  }

  private mapToInstallmentDto(installment: any): InstallmentDto {
    return {
      id: installment.id,
      planId: installment.planId,
      installmentNumber: installment.installmentNumber,
      amount: installment.amount,
      currency: installment.currency,
      principalAmount: installment.principalAmount,
      interestAmount: installment.interestAmount,
      lateFeeAmount: installment.lateFeeAmount,
      dueDate: installment.dueDate,
      paidDate: installment.paidDate,
      status: installment.status,
      paymentMethod: installment.paymentMethod,
      transactionId: installment.transactionId,
      escrowReleaseId: installment.escrowReleaseId,
      remindersSent: installment.remindersSent,
      lastReminderAt: installment.lastReminderAt,
      createdAt: installment.createdAt,
      updatedAt: installment.updatedAt,
    };
  }
}
