import { Injectable, NotFoundException, BadRequestException } from '@nestjs/common';
import { PrismaService } from '@core/prisma/prisma.service';
import { CreateTransactionDto } from '../dto/create-transaction.dto';
import { CreateInvoiceDto } from '../dto/create-invoice.dto';
import { UpdateTransactionDto } from '../dto/update-transaction.dto';
import { InvoiceDto } from '../dto/invoice.dto';
import { TransactionStatus, TransactionType } from '../entities/transaction.entity';
import { MongoDbService } from '@infra/mongodb/mongodb.service';
import { RabbitMQService } from '@infra/rabbitmq/rabbitmq.service';
import { WebsocketService } from '@infra/websocket/websocket.service';
import { DecimalService } from '@shared/decimal/decimal.service';
import { LoggerService } from '@core/logger/logger.service';
import { StandardListParams, getPaginationMetadata } from '@shared/utils/query-params.util';
import { InvoiceStatus } from '../dto/invoice.dto';

@Injectable()
export class FinanceService {
  constructor(
    private readonly prisma: PrismaService,
    private readonly mongoDbService: MongoDbService,
    private readonly rabbitMQService: RabbitMQService,
    private readonly websocketService: WebsocketService,
    private readonly decimalService: DecimalService,
    private readonly loggerService: LoggerService,
  ) {
    this.loggerService.setContext('FinanceService');
  }

  // Transaction Methods
  async createTransaction(createTransactionDto: CreateTransactionDto) {
    try {
      // Convert decimal string to proper format
      const amount = this.decimalService.create(createTransactionDto.amount).toString();
      
      // Create transaction in PostgreSQL (source of truth)
      const transaction = await this.prisma.transaction.create({
        data: {
          type: createTransactionDto.type,
          status: createTransactionDto.status || TransactionStatus.PENDING,
          amount,
          currency: createTransactionDto.currency || 'USD',
          description: createTransactionDto.description,
          orderId: createTransactionDto.orderId,
          customerId: createTransactionDto.customerId,
          invoiceId: createTransactionDto.invoiceId,
          paymentMethod: createTransactionDto.paymentMethod,
          referenceNumber: createTransactionDto.referenceNumber,
          notes: createTransactionDto.notes,
          transactionDate: createTransactionDto.transactionDate || new Date(),
        },
      });
      
      // Sync to MongoDB for fast reads
      await this.syncTransactionToMongo(transaction);
      
      // Notify about new transaction
      await this.notifyTransactionCreation(transaction);
      
      return transaction;
    } catch (error) {
      throw new BadRequestException(`Failed to create transaction: ${error.message}`);
    }
  }

  async findAllTransactions() {
    // Read from MongoDB for better performance
    const transactions = await this.mongoDbService.find('transactions', {});
    return transactions;
  }

  async findOneTransaction(id: string) {
    // Read from MongoDB for better performance
    const transaction = await this.mongoDbService.findOne('transactions', { id });
    
    if (!transaction) {
      throw new NotFoundException(`Transaction with ID ${id} not found`);
    }
    
    return transaction;
  }

  async updateTransaction(id: string, updateTransactionDto: UpdateTransactionDto) {
    // Check if transaction exists
    const transaction = await this.prisma.transaction.findUnique({
      where: { id },
    });
    
    if (!transaction) {
      throw new NotFoundException(`Transaction with ID ${id} not found`);
    }
    
    // Prepare update data
    const updateData: any = { ...updateTransactionDto };
    
    // Convert amount to string if provided
    if (updateTransactionDto.amount) {
      updateData.amount = this.decimalService.create(updateTransactionDto.amount).toString();
    }
    
    // Update transaction in PostgreSQL
    const updatedTransaction = await this.prisma.transaction.update({
      where: { id },
      data: updateData,
    });
    
    // Sync to MongoDB for fast reads
    await this.syncTransactionToMongo(updatedTransaction);
    
    // Notify about transaction update
    await this.notifyTransactionUpdate(updatedTransaction);
    
    return updatedTransaction;
  }

  async updateTransactionStatus(id: string, status: TransactionStatus) {
    // Check if transaction exists
    const transaction = await this.prisma.transaction.findUnique({
      where: { id },
    });
    
    if (!transaction) {
      throw new NotFoundException(`Transaction with ID ${id} not found`);
    }
    
    // Update transaction status in PostgreSQL
    const updatedTransaction = await this.prisma.transaction.update({
      where: { id },
      data: { status },
    });
    
    // Sync to MongoDB for fast reads
    await this.syncTransactionToMongo(updatedTransaction);
    
    // Notify about transaction status update
    await this.notifyTransactionStatusUpdate(updatedTransaction);
    
    return updatedTransaction;
  }

  /**
   * Maps a database invoice entity to an InvoiceDto
   * @param invoice The database invoice entity
   * @returns InvoiceDto with properly formatted fields
   */
  private mapToInvoiceDto(invoice: any): InvoiceDto {
    if (!invoice) {
      throw new NotFoundException('Invoice not found');
    }
    
    const invoiceDto = new InvoiceDto();
    
    // Map basic fields
    invoiceDto.id = invoice.id;
    invoiceDto.invoiceNumber = invoice.invoiceNumber;
    invoiceDto.status = invoice.status;
    invoiceDto.customerId = invoice.customerId;
    invoiceDto.orderId = invoice.orderId;
    invoiceDto.createdAt = invoice.createdAt;
    invoiceDto.updatedAt = invoice.updatedAt;
    invoiceDto.issueDate = invoice.issueDate;
    invoiceDto.dueDate = invoice.dueDate;
    invoiceDto.paidDate = invoice.paidDate;
    
    // Map decimal fields
    invoiceDto.subtotal = invoice.subtotal;
    invoiceDto.tax = invoice.tax;
    invoiceDto.discount = invoice.discount || '0';
    invoiceDto.total = invoice.total;
    invoiceDto.totalAmount = invoice.total; // Map total to totalAmount for compatibility
    
    // Initialize empty items array (to be populated if needed)
    invoiceDto.items = [];
    
    // If invoice has items, map them
    if (invoice.items && Array.isArray(invoice.items)) {
      invoiceDto.items = invoice.items.map(item => ({
        id: item.id,
        invoiceId: item.invoiceId,
        description: item.description,
        quantity: item.quantity,
        unitPrice: item.unitPrice,
        totalPrice: item.total || item.totalPrice, // Handle both field names
        productId: item.productId,
        createdAt: item.createdAt,
        updatedAt: item.updatedAt
      }));
    }
    
    return invoiceDto;
  }

  // Invoice Methods
  async createInvoice(createInvoiceDto: CreateInvoiceDto): Promise<InvoiceDto> {
    try {
      this.loggerService.log(`Creating new invoice with number: ${createInvoiceDto.invoiceNumber}`);
      
      // Convert decimal strings to proper format
      const subtotal = this.decimalService.create(createInvoiceDto.subtotal).toString();
      const tax = this.decimalService.create(createInvoiceDto.tax).toString();
      const total = this.decimalService.create(createInvoiceDto.total).toString();
      const discount = createInvoiceDto.discount 
        ? this.decimalService.create(createInvoiceDto.discount).toString()
        : null;
      
      // Create invoice in PostgreSQL (source of truth)
      const invoice = await this.prisma.invoice.create({
        data: {
          customerId: createInvoiceDto.customerId,
          invoiceNumber: createInvoiceDto.invoiceNumber,
          subtotal,
          tax,
          discount,
          total,
          status: createInvoiceDto.status || InvoiceStatus.PENDING,
          orderId: createInvoiceDto.orderId,
          issueDate: createInvoiceDto.issueDate,
          dueDate: createInvoiceDto.dueDate,
        },
      });
      
      // Sync to MongoDB for fast reads
      await this.syncInvoiceToMongo(invoice);
      
      // Notify about new invoice
      await this.notifyInvoiceCreation(invoice);
      
      // For the DTO, we need to fetch the invoice with items from MongoDB
      // since it might have related items that were created separately
      const invoiceWithItems = await this.mongoDbService.findOne('invoices', { id: invoice.id });
      
      // Map to DTO before returning
      return this.mapToInvoiceDto(invoiceWithItems || invoice);
    } catch (error) {
      this.loggerService.error(`Failed to create invoice: ${error.message}`, error.stack);
      throw new BadRequestException(`Failed to create invoice: ${error.message}`);
    }
  }

  async findAllInvoices(params?: StandardListParams) {
    this.loggerService.log(`Finding all invoices with params: ${JSON.stringify(params)}`);
    
    // Set default pagination parameters
    const page = params?.page || 1;
    const limit = Math.min(params?.limit || 10, 100); // Cap at 100 items per page
    const skip = (page - 1) * limit;
    
    // Build query filters
    let filter: Record<string, any> = {};
    
    if (params?.where) {
      if (typeof params.where === 'string') {
        try {
          filter = JSON.parse(params.where);
        } catch (e) {
          this.loggerService.error(`Invalid JSON in where parameter: ${params.where}`);
        }
      } else if (typeof params.where === 'object') {
        filter = params.where;
      }
    }
    
    // Add search functionality if provided
    if (params?.search) {
      filter.$or = [
        { invoiceNumber: { $regex: params.search, $options: 'i' } },
        { 'customer.name': { $regex: params.search, $options: 'i' } }
      ];
    }
    
    // Build sort options
    let sort: Record<string, 1 | -1> = { createdAt: -1 }; // Default sort by creation date, newest first
    
    if (params?.order) {
      if (typeof params.order === 'string') {
        try {
          const orderObj = JSON.parse(params.order);
          sort = Object.entries(orderObj).reduce((acc, [key, value]) => {
            acc[key] = value === 'desc' ? -1 : 1;
            return acc;
          }, {} as Record<string, 1 | -1>);
        } catch (e) {
          this.loggerService.error(`Invalid JSON in order parameter: ${params.order}`);
        }
      } else if (typeof params.order === 'object') {
        sort = Object.entries(params.order).reduce((acc, [key, value]) => {
          acc[key] = value === 'desc' ? -1 : 1;
          return acc;
        }, {} as Record<string, 1 | -1>);
      }
    }
    
    try {
      // Read from MongoDB for better performance
      const invoices = await this.mongoDbService.find('invoices', filter, { skip, limit, sort });
      const total = await this.mongoDbService.count('invoices', filter);
      
      // Map each invoice to DTO
      const mappedInvoices = invoices.map(invoice => this.mapToInvoiceDto(invoice));
      
      // Return paginated result with metadata
      const paginationMeta = getPaginationMetadata({ page, limit }, total);
      return {
        data: mappedInvoices,
        total,
        ...paginationMeta
      };
    } catch (error) {
      this.loggerService.error(`Error finding invoices: ${error.message}`, error.stack);
      throw new BadRequestException(`Failed to fetch invoices: ${error.message}`);
    }
  }

  async findOneInvoice(id: string): Promise<InvoiceDto> {
    this.loggerService.log(`Finding invoice with ID: ${id}`);
    
    try {
      // Read from MongoDB for better performance
      const invoice = await this.mongoDbService.findOne('invoices', { id });
      
      if (!invoice) {
        this.loggerService.warn(`Invoice with ID ${id} not found`);
        throw new NotFoundException(`Invoice with ID ${id} not found`);
      }
      
      // Map to DTO before returning
      return this.mapToInvoiceDto(invoice);
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      this.loggerService.error(`Error finding invoice ${id}: ${error.message}`, error.stack);
      throw new BadRequestException(`Failed to fetch invoice: ${error.message}`);
    }
  }

  async markInvoiceAsPaid(id: string, paymentDate: Date, paymentReference: string): Promise<InvoiceDto> {
    this.loggerService.log(`Marking invoice ${id} as paid with payment reference ${paymentReference}`);
    
    try {
      // Find the invoice first
      const invoice = await this.prisma.invoice.findUnique({
        where: { id },
      });
      
      if (!invoice) {
        throw new NotFoundException(`Invoice with ID ${id} not found`);
      }
      
      if (invoice.status === InvoiceStatus.PAID) {
        throw new BadRequestException(`Invoice with ID ${id} is already marked as paid`);
      }
      
      // Update the invoice status to PAID
      const updatedInvoice = await this.prisma.invoice.update({
        where: { id },
        data: {
          status: InvoiceStatus.PAID,
          paidDate: paymentDate,
          // Store payment reference in a field that exists in the schema
          // Assuming there's no direct paymentReference field
        },
      });
      
      // Create a payment transaction for this invoice
      const paymentTransaction = await this.prisma.transaction.create({
        data: {
          type: TransactionType.OTHER, // Using OTHER type since PAYMENT doesn't exist in the enum
          status: TransactionStatus.COMPLETED,
          amount: invoice.total,
          currency: 'USD', // Default currency or get from invoice if available
          description: `Payment for invoice #${invoice.invoiceNumber} (Ref: ${paymentReference})`,
          invoiceId: id,
          customerId: invoice.customerId,
          paymentMethod: 'MANUAL', // Or get from parameters if needed
          referenceNumber: paymentReference,
          transactionDate: paymentDate || new Date(),
        },
      });
      
      // Sync to MongoDB for fast reads
      await this.syncInvoiceToMongo(updatedInvoice);
      await this.syncTransactionToMongo(paymentTransaction);
      
      // Notify about invoice payment
      await this.notifyInvoicePayment(updatedInvoice, paymentTransaction);
      
      // Fetch the updated invoice with items from MongoDB for the DTO
      const invoiceWithItems = await this.mongoDbService.findOne('invoices', { id });
      
      // Map to DTO before returning
      return this.mapToInvoiceDto(invoiceWithItems || updatedInvoice);
    } catch (error) {
      if (error instanceof NotFoundException || error instanceof BadRequestException) {
        throw error;
      }
      this.loggerService.error(`Error marking invoice as paid: ${error.message}`, error.stack);
      throw new BadRequestException(`Failed to mark invoice as paid: ${error.message}`);
    }
  }

  // Reports
  async getRevenueReport(startDate: Date, endDate: Date) {
    // Validate date range
    if (startDate > endDate) {
      throw new BadRequestException('Invalid date range');
    }
    
    // Get transactions in date range
    const transactions = await this.prisma.transaction.findMany({
      where: {
        type: TransactionType.SALE,
        status: TransactionStatus.COMPLETED,
        transactionDate: {
          gte: startDate,
          lte: endDate,
        },
      },
    });
    
    // Calculate total revenue
    const totalRevenue = transactions.reduce(
      (sum, transaction) => this.decimalService.add(
        sum.toString(),
        transaction.amount
      ),
      this.decimalService.create('0')
    );
    
    // Group transactions by day
    const revenueByDay = {};
    transactions.forEach(transaction => {
      const date = transaction.transactionDate.toISOString().split('T')[0];
      if (!revenueByDay[date]) {
        revenueByDay[date] = '0';
      }
      revenueByDay[date] = this.decimalService.add(
        revenueByDay[date],
        transaction.amount
      ).toString();
    });
    
    // Convert to the format expected by the GraphQL entity
    const revenueByDayArray = Object.keys(revenueByDay).map(date => ({
      date,
      amount: revenueByDay[date]
    }));
    
    return {
      startDate,
      endDate,
      totalRevenue: totalRevenue.toString(),
      transactionCount: transactions.length,
      revenueByDay: revenueByDayArray,
    };
  }

  // Private helper methods
  private async syncTransactionToMongo(transaction: any) {
    await this.mongoDbService.syncDocument('transactions', transaction);
  }

  private async syncInvoiceToMongo(invoice: any) {
    try {
      // First, check if the invoice exists in MongoDB
      const existingInvoice = await this.mongoDbService.findOne('invoices', { id: invoice.id });
      
      // If it exists, we need to preserve the items array if present
      if (existingInvoice && existingInvoice.items) {
        invoice.items = existingInvoice.items;
      }
      
      // Sync the document to MongoDB
      await this.mongoDbService.syncDocument('invoices', invoice);
      this.loggerService.log(`Synced invoice ${invoice.id} to MongoDB`);
    } catch (error) {
      this.loggerService.error(`Failed to sync invoice to MongoDB: ${error.message}`, error.stack);
      throw new Error(`Failed to sync invoice to MongoDB: ${error.message}`);
    }
  }
  
  // This method already exists in the file, so we're removing the duplicate implementation

  private async notifyTransactionCreation(transaction: any) {
    // Send message to RabbitMQ for event processing
    await this.rabbitMQService.publish({
      type: 'transaction_created',
      data: transaction,
    });

    // Notify admin/finance users via WebSocket
    this.websocketService.sendToRole('finance', 'transaction_created', {
      message: `New transaction created: ${transaction.id}`,
      transaction,
    });
  }

  private async notifyTransactionUpdate(transaction: any) {
    // Send message to RabbitMQ for event processing
    await this.rabbitMQService.publish({
      type: 'transaction_updated',
      data: transaction,
    });

    // Notify admin/finance users via WebSocket
    this.websocketService.sendToRole('finance', 'transaction_updated', {
      message: `Transaction updated: ${transaction.id}`,
      transaction,
    });
  }

  private async notifyTransactionStatusUpdate(transaction: any) {
    // Send message to RabbitMQ for event processing
    await this.rabbitMQService.publish({
      type: 'transaction_status_updated',
      data: transaction,
    });

    // Notify admin/finance users via WebSocket
    this.websocketService.sendToRole('finance', 'transaction_status_updated', {
      message: `Transaction status updated to ${transaction.status}: ${transaction.id}`,
      transaction,
    });

    // If this transaction is related to a customer, notify them too
    if (transaction.customerId) {
      this.websocketService.sendToUser(transaction.customerId, 'payment_status_updated', {
        message: `Your payment status has been updated to ${transaction.status}`,
        transaction: {
          id: transaction.id,
          status: transaction.status,
          amount: transaction.amount,
          currency: transaction.currency,
          date: transaction.transactionDate,
        },
      });
    }
  }

  private async notifyInvoiceCreation(invoice: any) {
    // Send message to RabbitMQ for event processing
    await this.rabbitMQService.publish({
      type: 'invoice_created',
      data: invoice,
    });

    // Notify admin/finance users via WebSocket
    this.websocketService.sendToRole('finance', 'invoice_created', {
      message: `New invoice created: ${invoice.invoiceNumber}`,
      invoice,
    });

    // Notify customer if customerId is provided
    if (invoice.customerId) {
      this.websocketService.sendToUser(invoice.customerId, 'invoice_issued', {
        message: `New invoice #${invoice.invoiceNumber} has been issued for your account`,
        invoice: {
          id: invoice.id,
          invoiceNumber: invoice.invoiceNumber,
          total: invoice.total,
          dueDate: invoice.dueDate,
        },
      });
    }
  }

  private async notifyInvoicePayment(invoice: any, transaction: any) {
    // Send message to RabbitMQ for event processing
    await this.rabbitMQService.publish({
      type: 'invoice_paid',
      data: { invoice, transaction },
    });

    // Notify admin/finance users via WebSocket
    this.websocketService.sendToRole('finance', 'invoice_paid', {
      message: `Invoice #${invoice.invoiceNumber} has been paid`,
      invoice,
      transaction,
    });

    // Notify customer if customerId is provided
    if (invoice.customerId) {
      this.websocketService.sendToUser(invoice.customerId, 'invoice_paid', {
        message: `Your invoice #${invoice.invoiceNumber} has been marked as paid`,
        invoice: {
          id: invoice.id,
          invoiceNumber: invoice.invoiceNumber,
          total: invoice.total,
          paidDate: invoice.paidDate,
        },
      });
    }
  }
}
