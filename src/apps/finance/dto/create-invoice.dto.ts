import { InputType, Field, ID } from '@nestjs/graphql';
import { IsNotEmpty, IsString, IsOptional, IsDate, IsUUID } from 'class-validator';
import { Type } from 'class-transformer';

@InputType()
export class CreateInvoiceDto {
  @Field(() => ID, { nullable: true })
  @IsUUID()
  @IsOptional()
  customerId?: string;

  @Field(() => String)
  @IsString()
  @IsNotEmpty({ message: 'Invoice number is required' })
  invoiceNumber: string;

  @Field(() => String)
  @IsNotEmpty({ message: 'Subtotal is required' })
  subtotal: string;

  @Field(() => String)
  @IsNotEmpty({ message: 'Tax is required' })
  tax: string;

  @Field(() => String, { nullable: true })
  @IsOptional()
  discount?: string;

  @Field(() => String)
  @IsNotEmpty({ message: 'Total is required' })
  total: string;

  @Field(() => String, { defaultValue: 'PENDING' })
  @IsString()
  @IsOptional()
  status?: string;

  @Field(() => ID, { nullable: true })
  @IsUUID()
  @IsOptional()
  orderId?: string;

  @Field(() => Date)
  @IsDate()
  @Type(() => Date)
  @IsNotEmpty({ message: 'Issue date is required' })
  issueDate: Date;

  @Field(() => Date)
  @IsDate()
  @Type(() => Date)
  @IsNotEmpty({ message: 'Due date is required' })
  dueDate: Date;
}
