import { ApiProperty } from '@nestjs/swagger';
import { IsDate, IsEnum, IsString, IsUUID } from 'class-validator';
import Decimal from 'decimal.js';

enum TransactionType {
  PAYMENT = 'PAYMENT',
  REFUND = 'REFUND',
  ADJUSTMENT = 'ADJUSTMENT',
}

enum TransactionStatus {
  PENDING = 'PENDING',
  COMPLETED = 'COMPLETED',
  FAILED = 'FAILED',
  CANCELLED = 'CANCELLED',
}

export class TransactionDto {
  @ApiProperty({
    description: 'Unique identifier of the transaction',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @IsUUID()
  id: string;

  @ApiProperty({
    description: 'Transaction reference number',
    example: 'TRX-2023-0001',
  })
  @IsString()
  transactionNumber: string;

  @ApiProperty({
    description: 'Type of transaction',
    enum: TransactionType,
    example: TransactionType.PAYMENT,
  })
  @IsEnum(TransactionType)
  type: TransactionType;

  @ApiProperty({
    description: 'Status of the transaction',
    enum: TransactionStatus,
    example: TransactionStatus.COMPLETED,
  })
  @IsEnum(TransactionStatus)
  status: TransactionStatus;

  @ApiProperty({
    description: 'Amount of the transaction',
    example: '159.98',
    type: String,
  })
  amount: Decimal;

  @ApiProperty({
    description: 'Currency of the transaction',
    example: 'USD',
  })
  @IsString()
  currency: string;

  @ApiProperty({
    description: 'User ID associated with this transaction',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @IsUUID()
  userId: string;

  @ApiProperty({
    description: 'Invoice ID associated with this transaction (if applicable)',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @IsUUID()
  invoiceId: string;

  @ApiProperty({
    description: 'Payment method used',
    example: 'Credit Card',
  })
  @IsString()
  paymentMethod: string;

  @ApiProperty({
    description: 'Date when the transaction was processed',
    example: '2023-01-15T08:30:00Z',
  })
  @IsDate()
  transactionDate: Date;

  @ApiProperty({
    description: 'Date when the transaction was created',
    example: '2023-01-15T08:30:00Z',
  })
  @IsDate()
  createdAt: Date;

  @ApiProperty({
    description: 'Date when the transaction was last updated',
    example: '2023-01-15T08:30:00Z',
  })
  @IsDate()
  updatedAt: Date;
}
