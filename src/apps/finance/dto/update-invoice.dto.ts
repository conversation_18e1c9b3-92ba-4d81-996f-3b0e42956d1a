import { ApiProperty } from '@nestjs/swagger';
import { IsDate, IsEnum, IsOptional, IsString, IsUUID, ValidateNested } from 'class-validator';
import { Type } from 'class-transformer';
import Decimal from 'decimal.js';

enum InvoiceStatus {
  DRAFT = 'DRAFT',
  SENT = 'SENT',
  PAID = 'PAID',
  OVERDUE = 'OVERDUE',
  CANCELLED = 'CANCELLED',
}

class UpdateInvoiceItemDto {
  @ApiProperty({
    description: 'Unique identifier of the invoice item',
    example: '123e4567-e89b-12d3-a456-************',
    required: false,
  })
  @IsUUID()
  @IsOptional()
  id?: string;

  @ApiProperty({
    description: 'Description of the item',
    example: 'Premium Cotton Shirt',
    required: false,
  })
  @IsString()
  @IsOptional()
  description?: string;

  @ApiProperty({
    description: 'Quantity of the item',
    example: 2,
    required: false,
  })
  @IsOptional()
  quantity?: number;

  @ApiProperty({
    description: 'Unit price of the item',
    example: '59.99',
    type: String,
    required: false,
  })
  @IsOptional()
  unitPrice?: Decimal;
}

export class UpdateInvoiceDto {
  @ApiProperty({
    description: 'Current status of the invoice',
    enum: InvoiceStatus,
    example: InvoiceStatus.SENT,
    required: false,
  })
  @IsEnum(InvoiceStatus)
  @IsOptional()
  status?: InvoiceStatus;

  @ApiProperty({
    description: 'Items included in this invoice',
    type: [UpdateInvoiceItemDto],
    required: false,
  })
  @ValidateNested({ each: true })
  @Type(() => UpdateInvoiceItemDto)
  @IsOptional()
  items?: UpdateInvoiceItemDto[];

  @ApiProperty({
    description: 'Due date for the invoice',
    example: '2023-02-15T08:30:00Z',
    required: false,
  })
  @IsDate()
  @IsOptional()
  dueDate?: Date;

  @ApiProperty({
    description: 'Optional notes for the invoice',
    example: 'Payment due within 30 days',
    required: false,
  })
  @IsString()
  @IsOptional()
  notes?: string;
}
