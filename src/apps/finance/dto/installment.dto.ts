import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsString, IsNotEmpty, IsOptional, IsEnum, Matches, IsUUID, IsNumber, IsBoolean, IsDateString, Min, Max } from 'class-validator';

export class CreateInstallmentPlanDto {
  @ApiProperty({
    description: 'Order ID for which to create installment plan',
    example: '123e4567-e89b-12d3-a456-************',
    format: 'uuid',
  })
  @IsString()
  @IsNotEmpty()
  @IsUUID()
  orderId: string;

  @ApiProperty({
    description: 'Total amount to be paid in installments (decimal string)',
    example: '1200.00',
    pattern: '^\\d+(\\.\\d{1,2})?$',
  })
  @IsString()
  @IsNotEmpty()
  @Matches(/^\d+(\.\d{1,2})?$/, {
    message: 'Total amount must be a valid decimal number',
  })
  totalAmount: string;

  @ApiPropertyOptional({
    description: 'Currency of the installment plan',
    example: 'USD',
    default: 'USD',
    pattern: '^[A-Z]{3}$',
  })
  @IsString()
  @IsOptional()
  currency?: string;

  @ApiProperty({
    description: 'Number of installments',
    example: 6,
    enum: [2, 3, 6, 12, 24],
  })
  @IsNumber()
  @IsNotEmpty()
  numberOfInstallments: number;

  @ApiPropertyOptional({
    description: 'Annual interest rate (decimal string)',
    example: '12.00',
    pattern: '^\\d+(\\.\\d{1,2})?$',
    default: '0.00',
  })
  @IsString()
  @IsOptional()
  @Matches(/^\d+(\.\d{1,2})?$/, {
    message: 'Interest rate must be a valid decimal number',
  })
  interestRate?: string;

  @ApiPropertyOptional({
    description: 'One-time processing fee (decimal string)',
    example: '25.00',
    pattern: '^\\d+(\\.\\d{1,2})?$',
    default: '0.00',
  })
  @IsString()
  @IsOptional()
  @Matches(/^\d+(\.\d{1,2})?$/, {
    message: 'Processing fee must be a valid decimal number',
  })
  processingFee?: string;

  @ApiProperty({
    description: 'First payment due date',
    example: '2023-02-15T00:00:00Z',
    format: 'date-time',
  })
  @IsDateString()
  @IsNotEmpty()
  firstPaymentDate: Date;

  @ApiPropertyOptional({
    description: 'Payment frequency',
    example: 'MONTHLY',
    enum: ['WEEKLY', 'MONTHLY'],
    default: 'MONTHLY',
  })
  @IsString()
  @IsOptional()
  paymentFrequency?: string;

  @ApiPropertyOptional({
    description: 'Early payment discount rate (decimal string)',
    example: '2.00',
    pattern: '^\\d+(\\.\\d{1,2})?$',
    default: '0.00',
  })
  @IsString()
  @IsOptional()
  @Matches(/^\d+(\.\d{1,2})?$/, {
    message: 'Early payment discount must be a valid decimal number',
  })
  earlyPaymentDiscount?: string;

  @ApiPropertyOptional({
    description: 'Late fee rate (decimal string)',
    example: '5.00',
    pattern: '^\\d+(\\.\\d{1,2})?$',
    default: '5.00',
  })
  @IsString()
  @IsOptional()
  @Matches(/^\d+(\.\d{1,2})?$/, {
    message: 'Late fee rate must be a valid decimal number',
  })
  lateFeeRate?: string;

  @ApiPropertyOptional({
    description: 'Grace period in days before late fee applies',
    example: 3,
    minimum: 0,
    maximum: 30,
    default: 3,
  })
  @IsNumber()
  @IsOptional()
  @Min(0)
  @Max(30)
  gracePeriodDays?: number;
}

export class ProcessInstallmentPaymentDto {
  @ApiProperty({
    description: 'Installment ID to process payment for',
    example: '123e4567-e89b-12d3-a456-************',
    format: 'uuid',
  })
  @IsString()
  @IsNotEmpty()
  @IsUUID()
  installmentId: string;

  @ApiProperty({
    description: 'Payment amount (decimal string)',
    example: '200.00',
    pattern: '^\\d+(\\.\\d{1,2})?$',
  })
  @IsString()
  @IsNotEmpty()
  @Matches(/^\d+(\.\d{1,2})?$/, {
    message: 'Payment amount must be a valid decimal number',
  })
  amount: string;

  @ApiProperty({
    description: 'Payment method used',
    example: 'WALLET',
    enum: ['WALLET', 'ESCROW', 'BANK_TRANSFER', 'CREDIT_CARD', 'DEBIT_CARD'],
  })
  @IsString()
  @IsNotEmpty()
  paymentMethod: string;

  @ApiPropertyOptional({
    description: 'Transaction ID reference',
    example: 'TXN_123456789',
    maxLength: 100,
  })
  @IsString()
  @IsOptional()
  transactionId?: string;

  @ApiPropertyOptional({
    description: 'Whether to release escrow funds for this installment',
    example: true,
    default: false,
  })
  @IsBoolean()
  @IsOptional()
  releaseEscrow?: boolean;

  @ApiPropertyOptional({
    description: 'Additional payment notes',
    example: 'Payment processed via wallet',
    maxLength: 255,
  })
  @IsString()
  @IsOptional()
  notes?: string;
}

export class ProcessEarlyPaymentDto {
  @ApiProperty({
    description: 'Installment plan ID for early payment',
    example: '123e4567-e89b-12d3-a456-************',
    format: 'uuid',
  })
  @IsString()
  @IsNotEmpty()
  @IsUUID()
  planId: string;

  @ApiProperty({
    description: 'Early payment amount (decimal string)',
    example: '1000.00',
    pattern: '^\\d+(\\.\\d{1,2})?$',
  })
  @IsString()
  @IsNotEmpty()
  @Matches(/^\d+(\.\d{1,2})?$/, {
    message: 'Payment amount must be a valid decimal number',
  })
  paymentAmount: string;

  @ApiProperty({
    description: 'Payment method used for early payment',
    example: 'WALLET',
    enum: ['WALLET', 'BANK_TRANSFER', 'CREDIT_CARD'],
  })
  @IsString()
  @IsNotEmpty()
  paymentMethod: string;

  @ApiPropertyOptional({
    description: 'Transaction ID reference',
    example: 'TXN_123456789',
    maxLength: 100,
  })
  @IsString()
  @IsOptional()
  transactionId?: string;
}

export class UpdateInstallmentPlanDto {
  @ApiPropertyOptional({
    description: 'New payment frequency',
    example: 'MONTHLY',
    enum: ['WEEKLY', 'MONTHLY'],
  })
  @IsString()
  @IsOptional()
  paymentFrequency?: string;

  @ApiPropertyOptional({
    description: 'New late fee rate (decimal string)',
    example: '7.50',
    pattern: '^\\d+(\\.\\d{1,2})?$',
  })
  @IsString()
  @IsOptional()
  @Matches(/^\d+(\.\d{1,2})?$/, {
    message: 'Late fee rate must be a valid decimal number',
  })
  lateFeeRate?: string;

  @ApiPropertyOptional({
    description: 'New grace period in days',
    example: 5,
    minimum: 0,
    maximum: 30,
  })
  @IsNumber()
  @IsOptional()
  @Min(0)
  @Max(30)
  gracePeriodDays?: number;

  @ApiPropertyOptional({
    description: 'Reason for plan modification',
    example: 'Customer requested extended grace period',
    maxLength: 500,
  })
  @IsString()
  @IsOptional()
  reason?: string;
}

export class CancelInstallmentPlanDto {
  @ApiProperty({
    description: 'Installment plan ID to cancel',
    example: '123e4567-e89b-12d3-a456-************',
    format: 'uuid',
  })
  @IsString()
  @IsNotEmpty()
  @IsUUID()
  planId: string;

  @ApiProperty({
    description: 'Reason for cancellation',
    example: 'Customer requested order cancellation',
    maxLength: 500,
  })
  @IsString()
  @IsNotEmpty()
  reason: string;

  @ApiPropertyOptional({
    description: 'Whether to process refunds for paid installments',
    example: true,
    default: false,
  })
  @IsBoolean()
  @IsOptional()
  processRefunds?: boolean;

  @ApiProperty({
    description: 'User ID who initiated the cancellation',
    example: '123e4567-e89b-12d3-a456-************',
    format: 'uuid',
  })
  @IsString()
  @IsNotEmpty()
  @IsUUID()
  cancelledBy: string;
}

export class RescheduleInstallmentDto {
  @ApiProperty({
    description: 'Installment ID to reschedule',
    example: '123e4567-e89b-12d3-a456-************',
    format: 'uuid',
  })
  @IsString()
  @IsNotEmpty()
  @IsUUID()
  installmentId: string;

  @ApiProperty({
    description: 'New due date for the installment',
    example: '2023-03-15T00:00:00Z',
    format: 'date-time',
  })
  @IsDateString()
  @IsNotEmpty()
  newDueDate: Date;

  @ApiPropertyOptional({
    description: 'Reason for rescheduling',
    example: 'Customer requested due date extension',
    maxLength: 500,
  })
  @IsString()
  @IsOptional()
  reason?: string;

  @ApiPropertyOptional({
    description: 'Whether to charge a rescheduling fee',
    example: false,
    default: false,
  })
  @IsBoolean()
  @IsOptional()
  chargeReschedulingFee?: boolean;
}
