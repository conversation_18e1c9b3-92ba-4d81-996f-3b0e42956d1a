import { ApiProperty } from '@nestjs/swagger';
import { IsDate, IsEnum, IsNumber, IsOptional, IsString, IsUUID, ValidateNested } from 'class-validator';
import { Type } from 'class-transformer';
import Decimal from 'decimal.js';

export enum InvoiceStatus {
  DRAFT = 'DRAFT',
  SENT = 'SENT',
  PAID = 'PAID',
  OVERDUE = 'OVERDUE',
  CANCELLED = 'CANCELLED',
  PENDING = 'PENDING',
}

export class InvoiceItemDto {
  @ApiProperty({
    description: 'Unique identifier of the invoice item',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @IsUUID()
  id: string;

  @ApiProperty({
    description: 'Description of the item',
    example: 'Premium Cotton Shirt',
  })
  @IsString()
  description: string;

  @ApiProperty({
    description: 'Quantity of the item',
    example: 2,
  })
  @IsNumber()
  quantity: number;

  @ApiProperty({
    description: 'Unit price of the item',
    example: '59.99',
    type: String,
  })
  unitPrice: Decimal;

  @ApiProperty({
    description: 'Total price for this item (quantity * unitPrice)',
    example: '119.98',
    type: String,
  })
  totalPrice: Decimal;
}

export class InvoiceDto {
  @ApiProperty({
    description: 'Unique identifier of the invoice',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @IsUUID()
  id: string;

  @ApiProperty({
    description: 'Invoice number',
    example: 'INV-2023-0001',
  })
  @IsString()
  invoiceNumber: string;

  @ApiProperty({
    description: 'User ID of the customer',
    example: '123e4567-e89b-12d3-a456-************',
    required: false,
  })
  @IsUUID()
  @IsOptional()
  customerId?: string;

  @ApiProperty({
    description: 'Related order ID',
    example: '123e4567-e89b-12d3-a456-************',
    required: false,
  })
  @IsUUID()
  @IsOptional()
  orderId?: string;

  @ApiProperty({
    description: 'Current status of the invoice',
    enum: InvoiceStatus,
    example: InvoiceStatus.SENT,
  })
  @IsEnum(InvoiceStatus)
  status: InvoiceStatus;

  @ApiProperty({
    description: 'Subtotal amount before tax',
    example: '100.00',
    type: String,
  })
  subtotal: string;

  @ApiProperty({
    description: 'Tax amount',
    example: '10.00',
    type: String,
  })
  tax: string;

  @ApiProperty({
    description: 'Discount amount if applicable',
    example: '5.00',
    type: String,
    required: false,
  })
  @IsOptional()
  discount?: string;

  @ApiProperty({
    description: 'Total amount of the invoice',
    example: '105.00',
    type: String,
  })
  total: string;

  @ApiProperty({
    description: 'Total amount of the invoice (alias for total)',
    example: '105.00',
    type: String,
  })
  totalAmount: string;

  @ApiProperty({
    description: 'Items included in this invoice',
    type: [InvoiceItemDto],
  })
  @ValidateNested({ each: true })
  @Type(() => InvoiceItemDto)
  items: InvoiceItemDto[];

  @ApiProperty({
    description: 'Date when the invoice was issued',
    example: '2023-01-15T08:30:00Z',
  })
  @IsDate()
  issueDate: Date;

  @ApiProperty({
    description: 'Due date for the invoice',
    example: '2023-02-15T08:30:00Z',
  })
  @IsDate()
  dueDate: Date;

  @ApiProperty({
    description: 'Date when the invoice was paid',
    example: '2023-02-10T14:30:00Z',
    required: false,
  })
  @IsDate()
  @IsOptional()
  paidDate?: Date;

  @ApiProperty({
    description: 'Date when the invoice was created',
    example: '2023-01-15T08:30:00Z',
  })
  @IsDate()
  createdAt: Date;

  @ApiProperty({
    description: 'Date when the invoice was last updated',
    example: '2023-02-20T10:15:00Z',
  })
  @IsDate()
  updatedAt: Date;

  @ApiProperty({
    description: 'Optional notes for the invoice',
    example: 'Payment due within 30 days',
    required: false,
  })
  @IsString()
  @IsOptional()
  notes?: string;
}
