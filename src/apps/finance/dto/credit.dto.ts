import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsString, IsNotEmpty, IsOptional, IsEnum, Matches, IsUUID, IsNumber, IsBoolean, IsArray, <PERSON>, <PERSON> } from 'class-validator';

export enum LoanType {
  PERSONAL = 'PERSONAL',
  BUSINESS = 'BUSINESS',
  ORDER_FINANCING = 'ORDER_FINANCING',
  EQUIPMENT = 'EQUIPMENT',
  WORKING_CAPITAL = 'WORKING_CAPITAL',
}

export class CreateCreditProfileDto {
  @ApiProperty({
    description: 'User ID for whom to create the credit profile',
    example: '123e4567-e89b-12d3-a456-************',
    format: 'uuid',
  })
  @IsString()
  @IsNotEmpty()
  @IsUUID()
  userId: string;

  @ApiPropertyOptional({
    description: 'Monthly income (decimal string)',
    example: '5000.00',
    pattern: '^\\d+(\\.\\d{1,2})?$',
  })
  @IsString()
  @IsOptional()
  @Matches(/^\d+(\.\d{1,2})?$/, {
    message: 'Monthly income must be a valid decimal number',
  })
  monthlyIncome?: string;

  @ApiPropertyOptional({
    description: 'Whether income is verified',
    example: true,
    default: false,
  })
  @IsBoolean()
  @IsOptional()
  incomeVerified?: boolean;

  @ApiPropertyOptional({
    description: 'Employment status',
    example: 'EMPLOYED',
    enum: ['EMPLOYED', 'SELF_EMPLOYED', 'UNEMPLOYED'],
  })
  @IsString()
  @IsOptional()
  employmentStatus?: string;

  @ApiPropertyOptional({
    description: 'Additional risk factors',
    example: ['FIRST_TIME_BORROWER'],
    type: [String],
  })
  @IsArray()
  @IsOptional()
  riskFactors?: string[];
}

export class CreateLoanApplicationDto {
  @ApiProperty({
    description: 'User ID applying for the loan',
    example: '123e4567-e89b-12d3-a456-************',
    format: 'uuid',
  })
  @IsString()
  @IsNotEmpty()
  @IsUUID()
  userId: string;

  @ApiProperty({
    description: 'Type of loan being requested',
    enum: LoanType,
    example: LoanType.PERSONAL,
  })
  @IsEnum(LoanType)
  loanType: LoanType;

  @ApiProperty({
    description: 'Requested loan amount (decimal string)',
    example: '5000.00',
    pattern: '^\\d+(\\.\\d{1,2})?$',
  })
  @IsString()
  @IsNotEmpty()
  @Matches(/^\d+(\.\d{1,2})?$/, {
    message: 'Requested amount must be a valid decimal number',
  })
  requestedAmount: string;

  @ApiPropertyOptional({
    description: 'Currency of the loan',
    example: 'USD',
    default: 'USD',
    pattern: '^[A-Z]{3}$',
  })
  @IsString()
  @IsOptional()
  currency?: string;

  @ApiProperty({
    description: 'Purpose of the loan',
    example: 'Equipment purchase for tailoring business',
    maxLength: 500,
  })
  @IsString()
  @IsNotEmpty()
  purpose: string;

  @ApiProperty({
    description: 'Loan term in months',
    example: 24,
    minimum: 3,
    maximum: 60,
  })
  @IsNumber()
  @Min(3)
  @Max(60)
  termMonths: number;

  @ApiPropertyOptional({
    description: 'Supporting documents (URLs or file references)',
    example: ['https://example.com/income_statement.pdf', 'https://example.com/bank_statement.pdf'],
    type: [String],
  })
  @IsArray()
  @IsOptional()
  documents?: string[];
}

export class ProcessLoanApplicationDto {
  @ApiProperty({
    description: 'Loan application ID to process',
    example: '123e4567-e89b-12d3-a456-************',
    format: 'uuid',
  })
  @IsString()
  @IsNotEmpty()
  @IsUUID()
  applicationId: string;

  @ApiProperty({
    description: 'Approval decision',
    example: 'APPROVED',
    enum: ['APPROVED', 'REJECTED'],
  })
  @IsEnum(['APPROVED', 'REJECTED'])
  decision: string;

  @ApiPropertyOptional({
    description: 'Approved loan amount (may differ from requested)',
    example: '4500.00',
    pattern: '^\\d+(\\.\\d{1,2})?$',
  })
  @IsString()
  @IsOptional()
  @Matches(/^\d+(\.\d{1,2})?$/, {
    message: 'Approved amount must be a valid decimal number',
  })
  approvedAmount?: string;

  @ApiPropertyOptional({
    description: 'Annual interest rate (decimal string)',
    example: '12.50',
    pattern: '^\\d+(\\.\\d{1,2})?$',
  })
  @IsString()
  @IsOptional()
  @Matches(/^\d+(\.\d{1,2})?$/, {
    message: 'Interest rate must be a valid decimal number',
  })
  interestRate?: string;

  @ApiPropertyOptional({
    description: 'Reason for rejection (required if rejected)',
    example: 'Insufficient credit history',
    maxLength: 500,
  })
  @IsString()
  @IsOptional()
  rejectionReason?: string;

  @ApiProperty({
    description: 'User ID who reviewed the application',
    example: '123e4567-e89b-12d3-a456-************',
    format: 'uuid',
  })
  @IsString()
  @IsNotEmpty()
  @IsUUID()
  reviewedBy: string;

  @ApiPropertyOptional({
    description: 'Additional notes about the decision',
    example: 'Approved with standard terms',
    maxLength: 1000,
  })
  @IsString()
  @IsOptional()
  notes?: string;
}

export class ProcessLoanPaymentDto {
  @ApiProperty({
    description: 'Loan ID to make payment for',
    example: '123e4567-e89b-12d3-a456-************',
    format: 'uuid',
  })
  @IsString()
  @IsNotEmpty()
  @IsUUID()
  loanId: string;

  @ApiProperty({
    description: 'Payment amount (decimal string)',
    example: '250.00',
    pattern: '^\\d+(\\.\\d{1,2})?$',
  })
  @IsString()
  @IsNotEmpty()
  @Matches(/^\d+(\.\d{1,2})?$/, {
    message: 'Payment amount must be a valid decimal number',
  })
  amount: string;

  @ApiProperty({
    description: 'Payment method used',
    example: 'WALLET',
    enum: ['WALLET', 'BANK_TRANSFER', 'CREDIT_CARD', 'DEBIT_CARD', 'AUTO_DEBIT'],
  })
  @IsString()
  @IsNotEmpty()
  paymentMethod: string;

  @ApiPropertyOptional({
    description: 'External transaction reference',
    example: 'TXN_123456789',
    maxLength: 100,
  })
  @IsString()
  @IsOptional()
  transactionReference?: string;

  @ApiPropertyOptional({
    description: 'Additional payment notes',
    example: 'Early payment with bonus',
    maxLength: 255,
  })
  @IsString()
  @IsOptional()
  notes?: string;
}

export class UpdateCreditLimitDto {
  @ApiProperty({
    description: 'User ID whose credit limit to update',
    example: '123e4567-e89b-12d3-a456-************',
    format: 'uuid',
  })
  @IsString()
  @IsNotEmpty()
  @IsUUID()
  userId: string;

  @ApiProperty({
    description: 'New total credit limit (decimal string)',
    example: '10000.00',
    pattern: '^\\d+(\\.\\d{1,2})?$',
  })
  @IsString()
  @IsNotEmpty()
  @Matches(/^\d+(\.\d{1,2})?$/, {
    message: 'Credit limit must be a valid decimal number',
  })
  newCreditLimit: string;

  @ApiPropertyOptional({
    description: 'Reason for credit limit change',
    example: 'Income increase verified',
    maxLength: 500,
  })
  @IsString()
  @IsOptional()
  reason?: string;

  @ApiProperty({
    description: 'Admin user ID making the change',
    example: '123e4567-e89b-12d3-a456-************',
    format: 'uuid',
  })
  @IsString()
  @IsNotEmpty()
  @IsUUID()
  updatedBy: string;
}

export class CreditScoreUpdateDto {
  @ApiProperty({
    description: 'User ID whose credit score to update',
    example: '123e4567-e89b-12d3-a456-************',
    format: 'uuid',
  })
  @IsString()
  @IsNotEmpty()
  @IsUUID()
  userId: string;

  @ApiProperty({
    description: 'New credit score',
    example: 720,
    minimum: 300,
    maximum: 850,
  })
  @IsNumber()
  @Min(300)
  @Max(850)
  newCreditScore: number;

  @ApiPropertyOptional({
    description: 'Reason for credit score update',
    example: 'Payment history improvement',
    maxLength: 500,
  })
  @IsString()
  @IsOptional()
  reason?: string;

  @ApiProperty({
    description: 'Source of the credit score update',
    example: 'MANUAL_REVIEW',
    enum: ['MANUAL_REVIEW', 'AUTOMATED_UPDATE', 'EXTERNAL_BUREAU', 'PAYMENT_HISTORY'],
  })
  @IsString()
  @IsNotEmpty()
  source: string;
}

export class LoanRefinanceDto {
  @ApiProperty({
    description: 'Existing loan ID to refinance',
    example: '123e4567-e89b-12d3-a456-************',
    format: 'uuid',
  })
  @IsString()
  @IsNotEmpty()
  @IsUUID()
  existingLoanId: string;

  @ApiProperty({
    description: 'New interest rate (decimal string)',
    example: '8.50',
    pattern: '^\\d+(\\.\\d{1,2})?$',
  })
  @IsString()
  @IsNotEmpty()
  @Matches(/^\d+(\.\d{1,2})?$/, {
    message: 'Interest rate must be a valid decimal number',
  })
  newInterestRate: string;

  @ApiPropertyOptional({
    description: 'New loan term in months',
    example: 36,
    minimum: 3,
    maximum: 60,
  })
  @IsNumber()
  @IsOptional()
  @Min(3)
  @Max(60)
  newTermMonths?: number;

  @ApiPropertyOptional({
    description: 'Additional amount to borrow',
    example: '2000.00',
    pattern: '^\\d+(\\.\\d{1,2})?$',
  })
  @IsString()
  @IsOptional()
  @Matches(/^\d+(\.\d{1,2})?$/, {
    message: 'Additional amount must be a valid decimal number',
  })
  additionalAmount?: string;

  @ApiPropertyOptional({
    description: 'Reason for refinancing',
    example: 'Lower interest rate available',
    maxLength: 500,
  })
  @IsString()
  @IsOptional()
  reason?: string;
}
