import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsString, IsNotEmpty, IsOptional, IsArray, IsNumber, <PERSON>, <PERSON>, <PERSON> } from 'class-validator';

export class CreateEscrowDto {
  @ApiProperty({
    description: 'Order ID for which escrow is being created',
    example: '123e4567-e89b-12d3-a456-************',
    format: 'uuid',
  })
  @IsString()
  @IsNotEmpty()
  orderId: string;

  @ApiProperty({
    description: 'Customer ID who made the payment',
    example: '123e4567-e89b-12d3-a456-************',
    format: 'uuid',
  })
  @IsString()
  @IsNotEmpty()
  customerId: string;

  @ApiProperty({
    description: 'Total amount to be held in escrow (decimal string)',
    example: '150.00',
    pattern: '^\\d+(\\.\\d{1,2})?$',
  })
  @IsString()
  @IsNotEmpty()
  @Matches(/^\d+(\.\d{1,2})?$/, {
    message: 'Amount must be a valid decimal number with up to 2 decimal places',
  })
  amount: string;

  @ApiPropertyOptional({
    description: 'Platform fee rate as decimal (e.g., 0.05 for 5%)',
    example: '0.05',
    default: '0.05',
    minimum: 0,
    maximum: 1,
  })
  @IsString()
  @IsOptional()
  @Matches(/^0(\.\d{1,4})?$|^1(\.0{1,4})?$/, {
    message: 'Platform fee rate must be between 0 and 1 with up to 4 decimal places',
  })
  platformFeeRate?: string;

  @ApiPropertyOptional({
    description: 'Reason for holding payment in escrow',
    example: 'Payment held pending order completion and customer confirmation',
    maxLength: 500,
  })
  @IsString()
  @IsOptional()
  holdReason?: string;

  @ApiPropertyOptional({
    description: 'Conditions that must be met before releasing escrow',
    example: ['ORDER_COMPLETED', 'CUSTOMER_CONFIRMATION'],
    type: [String],
  })
  @IsArray()
  @IsOptional()
  releaseConditions?: string[];

  @ApiProperty({
    description: 'Payment method used for the transaction',
    example: 'credit_card',
    enum: ['credit_card', 'debit_card', 'paypal', 'bank_transfer', 'stripe', 'other'],
  })
  @IsString()
  @IsNotEmpty()
  paymentMethod: string;

  @ApiPropertyOptional({
    description: 'Payment reference from customer',
    example: 'PAY_REF_123456789',
    maxLength: 100,
  })
  @IsString()
  @IsOptional()
  paymentReference?: string;

  @ApiPropertyOptional({
    description: 'Transaction ID from payment gateway',
    example: 'pi_1234567890abcdef',
    maxLength: 100,
  })
  @IsString()
  @IsOptional()
  gatewayTransactionId?: string;

  @ApiPropertyOptional({
    description: 'Currency code (ISO 4217)',
    example: 'USD',
    default: 'USD',
    pattern: '^[A-Z]{3}$',
  })
  @IsString()
  @IsOptional()
  currency?: string;

  @ApiPropertyOptional({
    description: 'Number of days until escrow expires (default 30)',
    example: 30,
    minimum: 1,
    maximum: 365,
    default: 30,
  })
  @IsNumber()
  @IsOptional()
  @Min(1)
  @Max(365)
  expiryDays?: number;
}

export class ReleaseEscrowDto {
  @ApiPropertyOptional({
    description: 'Notes about the escrow release',
    example: 'Order completed successfully, customer confirmed receipt',
    maxLength: 500,
  })
  @IsString()
  @IsOptional()
  notes?: string;

  @ApiPropertyOptional({
    description: 'User ID who triggered the release',
    example: '123e4567-e89b-12d3-a456-************',
    format: 'uuid',
  })
  @IsString()
  @IsOptional()
  triggeredBy?: string;
}

export class RefundEscrowDto {
  @ApiProperty({
    description: 'Reason for refunding the escrow',
    example: 'Order cancelled by customer',
    maxLength: 500,
  })
  @IsString()
  @IsNotEmpty()
  reason: string;

  @ApiPropertyOptional({
    description: 'User ID who triggered the refund',
    example: '123e4567-e89b-12d3-a456-************',
    format: 'uuid',
  })
  @IsString()
  @IsOptional()
  triggeredBy?: string;
}

export class DisputeEscrowDto {
  @ApiProperty({
    description: 'Reason for disputing the escrow',
    example: 'Quality issues with delivered product',
    maxLength: 1000,
  })
  @IsString()
  @IsNotEmpty()
  reason: string;

  @ApiPropertyOptional({
    description: 'Additional evidence or documentation',
    example: ['https://example.com/evidence1.jpg', 'https://example.com/evidence2.pdf'],
    type: [String],
  })
  @IsArray()
  @IsOptional()
  evidence?: string[];

  @ApiProperty({
    description: 'User ID who raised the dispute',
    example: '123e4567-e89b-12d3-a456-************',
    format: 'uuid',
  })
  @IsString()
  @IsNotEmpty()
  disputedBy: string;
}
