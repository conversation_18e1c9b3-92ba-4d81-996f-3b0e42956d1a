import { InputType, Field, ID } from '@nestjs/graphql';
import { IsNotEmpty, IsEnum, IsString, IsOptional, IsDate, IsUUID } from 'class-validator';
import { TransactionType, TransactionStatus } from '../entities/transaction.entity';
import { Type } from 'class-transformer';

@InputType()
export class CreateTransactionDto {
  @Field(() => TransactionType)
  @IsEnum(TransactionType)
  @IsNotEmpty({ message: 'Transaction type is required' })
  type: TransactionType;

  @Field(() => TransactionStatus, { defaultValue: TransactionStatus.PENDING })
  @IsEnum(TransactionStatus)
  @IsOptional()
  status?: TransactionStatus;

  @Field(() => String)
  @IsNotEmpty({ message: 'Amount is required' })
  amount: string;

  @Field(() => String, { defaultValue: 'USD' })
  @IsString()
  @IsOptional()
  currency?: string;

  @Field(() => String)
  @IsString()
  @IsNotEmpty({ message: 'Description is required' })
  description: string;

  @Field(() => ID, { nullable: true })
  @IsUUID()
  @IsOptional()
  orderId?: string;

  @Field(() => ID, { nullable: true })
  @IsUUID()
  @IsOptional()
  customerId?: string | null;

  @Field(() => ID, { nullable: true })
  @IsUUID()
  @IsOptional()
  invoiceId?: string | null;

  @Field(() => String, { nullable: true })
  @IsString()
  @IsOptional()
  paymentMethod?: string;

  @Field(() => String, { nullable: true })
  @IsString()
  @IsOptional()
  referenceNumber?: string;

  @Field(() => String, { nullable: true })
  @IsString()
  @IsOptional()
  notes?: string;

  @Field(() => Date, { nullable: true })
  @IsDate()
  @Type(() => Date)
  @IsOptional()
  transactionDate?: Date;
}
