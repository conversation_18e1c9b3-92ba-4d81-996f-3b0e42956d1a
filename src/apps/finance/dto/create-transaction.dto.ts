import { InputType, Field, ID } from '@nestjs/graphql';
import { IsNotEmpty, IsEnum, IsString, IsOptional, IsDate, IsUUID } from 'class-validator';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { TransactionType, TransactionStatus } from '../entities/transaction.entity';
import { Type } from 'class-transformer';
import { ApiDecimalProperty } from '@shared/decorators/decimal.decorator';

@InputType()
export class CreateTransactionDto {
  @ApiProperty({
    description: 'Type of transaction',
    enum: TransactionType,
    example: TransactionType.SALE,
  })
  @Field(() => TransactionType)
  @IsEnum(TransactionType)
  @IsNotEmpty({ message: 'Transaction type is required' })
  type: TransactionType;

  @ApiPropertyOptional({
    description: 'Current status of the transaction',
    enum: TransactionStatus,
    example: TransactionStatus.PENDING,
    default: TransactionStatus.PENDING,
  })
  @Field(() => TransactionStatus, { defaultValue: TransactionStatus.PENDING })
  @IsEnum(TransactionStatus)
  @IsOptional()
  status?: TransactionStatus;

  @ApiDecimalProperty({
    description: 'Transaction amount',
    example: '150.75',
    minimum: '0.01',
  })
  @Field(() => String)
  @IsNotEmpty({ message: 'Amount is required' })
  amount: string;

  @ApiPropertyOptional({
    description: 'Currency code (ISO 4217)',
    example: 'USD',
    default: 'USD',
    pattern: '^[A-Z]{3}$',
    maxLength: 3,
    minLength: 3,
  })
  @Field(() => String, { defaultValue: 'USD' })
  @IsString()
  @IsOptional()
  currency?: string;

  @ApiProperty({
    description: 'Description of the transaction',
    example: 'Payment for order #12345',
    maxLength: 500,
  })
  @Field(() => String)
  @IsString()
  @IsNotEmpty({ message: 'Description is required' })
  description: string;

  @ApiPropertyOptional({
    description: 'Related order ID',
    example: '123e4567-e89b-12d3-a456-************',
    format: 'uuid',
  })
  @Field(() => ID, { nullable: true })
  @IsUUID()
  @IsOptional()
  orderId?: string;

  @ApiPropertyOptional({
    description: 'Customer ID associated with this transaction',
    example: '123e4567-e89b-12d3-a456-************',
    format: 'uuid',
  })
  @Field(() => ID, { nullable: true })
  @IsUUID()
  @IsOptional()
  customerId?: string | null;

  @ApiPropertyOptional({
    description: 'Related invoice ID',
    example: '123e4567-e89b-12d3-a456-************',
    format: 'uuid',
  })
  @Field(() => ID, { nullable: true })
  @IsUUID()
  @IsOptional()
  invoiceId?: string | null;

  @ApiPropertyOptional({
    description: 'Payment method used for the transaction',
    example: 'credit_card',
    enum: ['credit_card', 'debit_card', 'paypal', 'bank_transfer', 'cash', 'check', 'other'],
  })
  @Field(() => String, { nullable: true })
  @IsString()
  @IsOptional()
  paymentMethod?: string;

  @ApiPropertyOptional({
    description: 'External reference number from payment processor',
    example: 'TXN_REF_123456789',
    maxLength: 100,
  })
  @Field(() => String, { nullable: true })
  @IsString()
  @IsOptional()
  referenceNumber?: string;

  @ApiPropertyOptional({
    description: 'Additional notes about the transaction',
    example: 'Partial payment for large order',
    maxLength: 1000,
  })
  @Field(() => String, { nullable: true })
  @IsString()
  @IsOptional()
  notes?: string;

  @ApiPropertyOptional({
    description: 'Date and time when the transaction occurred',
    example: '2023-01-15T14:30:00Z',
    format: 'date-time',
  })
  @Field(() => Date, { nullable: true })
  @IsDate()
  @Type(() => Date)
  @IsOptional()
  transactionDate?: Date;
}
