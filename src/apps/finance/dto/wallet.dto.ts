import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsString, IsNotEmpty, IsOptional, IsEnum, Matches, IsUUID } from 'class-validator';

export class CreateWalletDto {
  @ApiProperty({
    description: 'User ID for whom to create the wallet',
    example: '123e4567-e89b-12d3-a456-************',
    format: 'uuid',
  })
  @IsString()
  @IsNotEmpty()
  @IsUUID()
  userId: string;

  @ApiPropertyOptional({
    description: 'Initial balance for the wallet (decimal string)',
    example: '100.00',
    pattern: '^\\d+(\\.\\d{1,2})?$',
    default: '0.00',
  })
  @IsString()
  @IsOptional()
  @Matches(/^\d+(\.\d{1,2})?$/, {
    message: 'Initial balance must be a valid decimal number with up to 2 decimal places',
  })
  initialBalance?: string;

  @ApiPropertyOptional({
    description: 'Primary currency for the wallet',
    example: 'USD',
    default: 'USD',
    pattern: '^[A-Z]{3}$',
  })
  @IsString()
  @IsOptional()
  currency?: string;

  @ApiPropertyOptional({
    description: 'Daily transaction limit (decimal string)',
    example: '1000.00',
    pattern: '^\\d+(\\.\\d{1,2})?$',
  })
  @IsString()
  @IsOptional()
  @Matches(/^\d+(\.\d{1,2})?$/, {
    message: 'Daily limit must be a valid decimal number',
  })
  dailyLimit?: string;

  @ApiPropertyOptional({
    description: 'Monthly transaction limit (decimal string)',
    example: '10000.00',
    pattern: '^\\d+(\\.\\d{1,2})?$',
  })
  @IsString()
  @IsOptional()
  @Matches(/^\d+(\.\d{1,2})?$/, {
    message: 'Monthly limit must be a valid decimal number',
  })
  monthlyLimit?: string;
}

export class FundWalletDto {
  @ApiProperty({
    description: 'User ID whose wallet to fund',
    example: '123e4567-e89b-12d3-a456-************',
    format: 'uuid',
  })
  @IsString()
  @IsNotEmpty()
  @IsUUID()
  userId: string;

  @ApiProperty({
    description: 'Amount to add to wallet (decimal string)',
    example: '50.00',
    pattern: '^\\d+(\\.\\d{1,2})?$',
  })
  @IsString()
  @IsNotEmpty()
  @Matches(/^\d+(\.\d{1,2})?$/, {
    message: 'Amount must be a valid decimal number with up to 2 decimal places',
  })
  amount: string;

  @ApiProperty({
    description: 'Payment method used for funding',
    example: 'credit_card',
    enum: ['credit_card', 'debit_card', 'bank_transfer', 'paypal', 'stripe', 'apple_pay', 'google_pay'],
  })
  @IsString()
  @IsNotEmpty()
  paymentMethod: string;

  @ApiPropertyOptional({
    description: 'Currency of the funding amount',
    example: 'USD',
    default: 'USD',
    pattern: '^[A-Z]{3}$',
  })
  @IsString()
  @IsOptional()
  currency?: string;

  @ApiPropertyOptional({
    description: 'Description for the funding transaction',
    example: 'Wallet top-up via credit card',
    maxLength: 255,
  })
  @IsString()
  @IsOptional()
  description?: string;

  @ApiPropertyOptional({
    description: 'Source of the funding',
    example: 'USER_INITIATED',
    enum: ['USER_INITIATED', 'PROMOTIONAL', 'REFUND', 'CASHBACK', 'BONUS'],
  })
  @IsString()
  @IsOptional()
  source?: string;

  @ApiPropertyOptional({
    description: 'External payment reference',
    example: 'pay_1234567890',
    maxLength: 100,
  })
  @IsString()
  @IsOptional()
  paymentReference?: string;
}

export class WithdrawFundsDto {
  @ApiProperty({
    description: 'User ID whose wallet to withdraw from',
    example: '123e4567-e89b-12d3-a456-************',
    format: 'uuid',
  })
  @IsString()
  @IsNotEmpty()
  @IsUUID()
  userId: string;

  @ApiProperty({
    description: 'Amount to withdraw from wallet (decimal string)',
    example: '25.00',
    pattern: '^\\d+(\\.\\d{1,2})?$',
  })
  @IsString()
  @IsNotEmpty()
  @Matches(/^\d+(\.\d{1,2})?$/, {
    message: 'Amount must be a valid decimal number with up to 2 decimal places',
  })
  amount: string;

  @ApiProperty({
    description: 'Withdrawal method',
    example: 'bank_transfer',
    enum: ['bank_transfer', 'paypal', 'check', 'wire_transfer'],
  })
  @IsString()
  @IsNotEmpty()
  withdrawalMethod: string;

  @ApiPropertyOptional({
    description: 'Currency of the withdrawal amount',
    example: 'USD',
    default: 'USD',
    pattern: '^[A-Z]{3}$',
  })
  @IsString()
  @IsOptional()
  currency?: string;

  @ApiPropertyOptional({
    description: 'Description for the withdrawal transaction',
    example: 'Withdrawal to bank account',
    maxLength: 255,
  })
  @IsString()
  @IsOptional()
  description?: string;

  @ApiPropertyOptional({
    description: 'Destination account details (encrypted)',
    example: 'bank_account_****1234',
    maxLength: 255,
  })
  @IsString()
  @IsOptional()
  destination?: string;
}

export class TransferFundsDto {
  @ApiProperty({
    description: 'Sender user ID',
    example: '123e4567-e89b-12d3-a456-************',
    format: 'uuid',
  })
  @IsString()
  @IsNotEmpty()
  @IsUUID()
  senderUserId: string;

  @ApiProperty({
    description: 'Receiver user ID',
    example: '123e4567-e89b-12d3-a456-************',
    format: 'uuid',
  })
  @IsString()
  @IsNotEmpty()
  @IsUUID()
  receiverUserId: string;

  @ApiProperty({
    description: 'Amount to transfer (decimal string)',
    example: '75.00',
    pattern: '^\\d+(\\.\\d{1,2})?$',
  })
  @IsString()
  @IsNotEmpty()
  @Matches(/^\d+(\.\d{1,2})?$/, {
    message: 'Amount must be a valid decimal number with up to 2 decimal places',
  })
  amount: string;

  @ApiPropertyOptional({
    description: 'Currency of the transfer',
    example: 'USD',
    default: 'USD',
    pattern: '^[A-Z]{3}$',
  })
  @IsString()
  @IsOptional()
  currency?: string;

  @ApiPropertyOptional({
    description: 'Description for the transfer',
    example: 'Payment for tailoring services',
    maxLength: 255,
  })
  @IsString()
  @IsOptional()
  description?: string;

  @ApiPropertyOptional({
    description: 'Reference for the transfer (e.g., order ID)',
    example: 'ORDER_123456',
    maxLength: 100,
  })
  @IsString()
  @IsOptional()
  reference?: string;

  @ApiPropertyOptional({
    description: 'Transfer fee amount (decimal string)',
    example: '2.50',
    pattern: '^\\d+(\\.\\d{1,2})?$',
    default: '0.00',
  })
  @IsString()
  @IsOptional()
  @Matches(/^\d+(\.\d{1,2})?$/, {
    message: 'Transfer fee must be a valid decimal number',
  })
  transferFee?: string;

  @ApiPropertyOptional({
    description: 'Who pays the transfer fee',
    example: '123e4567-e89b-12d3-a456-************',
    format: 'uuid',
  })
  @IsString()
  @IsOptional()
  @IsUUID()
  feePayerId?: string;
}

export class UpdateWalletStatusDto {
  @ApiProperty({
    description: 'New wallet status',
    example: 'ACTIVE',
    enum: ['ACTIVE', 'SUSPENDED', 'FROZEN', 'CLOSED'],
  })
  @IsEnum(['ACTIVE', 'SUSPENDED', 'FROZEN', 'CLOSED'])
  status: string;

  @ApiPropertyOptional({
    description: 'Reason for status change',
    example: 'Account verification completed',
    maxLength: 500,
  })
  @IsString()
  @IsOptional()
  reason?: string;
}

export class UpdateWalletLimitsDto {
  @ApiPropertyOptional({
    description: 'New daily transaction limit (decimal string)',
    example: '2000.00',
    pattern: '^\\d+(\\.\\d{1,2})?$',
  })
  @IsString()
  @IsOptional()
  @Matches(/^\d+(\.\d{1,2})?$/, {
    message: 'Daily limit must be a valid decimal number',
  })
  dailyLimit?: string;

  @ApiPropertyOptional({
    description: 'New monthly transaction limit (decimal string)',
    example: '20000.00',
    pattern: '^\\d+(\\.\\d{1,2})?$',
  })
  @IsString()
  @IsOptional()
  @Matches(/^\d+(\.\d{1,2})?$/, {
    message: 'Monthly limit must be a valid decimal number',
  })
  monthlyLimit?: string;
}

export class ExchangeRateDto {
  @ApiProperty({
    description: 'Source currency code',
    example: 'USD',
    pattern: '^[A-Z]{3}$',
  })
  @IsString()
  @IsNotEmpty()
  fromCurrency: string;

  @ApiProperty({
    description: 'Target currency code',
    example: 'EUR',
    pattern: '^[A-Z]{3}$',
  })
  @IsString()
  @IsNotEmpty()
  toCurrency: string;

  @ApiProperty({
    description: 'Exchange rate (decimal string)',
    example: '0.85',
    pattern: '^\\d+(\\.\\d{1,6})?$',
  })
  @IsString()
  @IsNotEmpty()
  @Matches(/^\d+(\.\d{1,6})?$/, {
    message: 'Exchange rate must be a valid decimal number with up to 6 decimal places',
  })
  rate: string;

  @ApiProperty({
    description: 'Rate source/provider',
    example: 'FIXER_API',
    maxLength: 50,
  })
  @IsString()
  @IsNotEmpty()
  source: string;
}
