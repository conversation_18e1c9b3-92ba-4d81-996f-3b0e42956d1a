import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsString, IsArray, IsDate, IsEnum, IsOptional } from 'class-validator';

export enum EscrowStatus {
  HELD = 'HELD',
  RELEASED = 'RELEASED',
  REFUNDED = 'REFUNDED',
  DISPUTED = 'DISPUTED',
  EXPIRED = 'EXPIRED',
  PARTIALLY_RELEASED = 'PARTIALLY_RELEASED',
}

export enum EscrowEventType {
  PAYMENT_HELD = 'PAYMENT_HELD',
  RELEASE_REQUESTED = 'RELEASE_REQUESTED',
  RELEASED = 'RELEASED',
  REFUND_REQUESTED = 'REFUND_REQUESTED',
  REFUNDED = 'REFUNDED',
  DISPUTE_RAISED = 'DISPUTE_RAISED',
  DISPUTE_RESOLVED = 'DISPUTE_RESOLVED',
  EXPIRED = 'EXPIRED',
  MANUAL_REVIEW = 'MANUAL_REVIEW',
}

export class EscrowDto {
  @ApiProperty({
    description: 'Unique identifier of the escrow transaction',
    example: '123e4567-e89b-12d3-a456-************',
    format: 'uuid',
  })
  @IsString()
  id: string;

  @ApiProperty({
    description: 'Order ID associated with this escrow',
    example: '123e4567-e89b-12d3-a456-************',
    format: 'uuid',
  })
  @IsString()
  orderId: string;

  @ApiProperty({
    description: 'Customer ID who made the payment',
    example: '123e4567-e89b-12d3-a456-************',
    format: 'uuid',
  })
  @IsString()
  customerId: string;

  @ApiPropertyOptional({
    description: 'Tailor ID who will receive the payment',
    example: '123e4567-e89b-12d3-a456-************',
    format: 'uuid',
  })
  @IsString()
  @IsOptional()
  tailorId?: string;

  @ApiProperty({
    description: 'Total amount held in escrow',
    example: '150.00',
    type: 'string',
  })
  @IsString()
  amount: string;

  @ApiProperty({
    description: 'Platform fee amount',
    example: '7.50',
    type: 'string',
  })
  @IsString()
  platformFee: string;

  @ApiProperty({
    description: 'Amount to be released to tailor (after platform fee)',
    example: '142.50',
    type: 'string',
  })
  @IsString()
  tailorAmount: string;

  @ApiProperty({
    description: 'Current status of the escrow',
    enum: EscrowStatus,
    example: EscrowStatus.HELD,
  })
  @IsEnum(EscrowStatus)
  status: EscrowStatus;

  @ApiPropertyOptional({
    description: 'Reason for holding payment in escrow',
    example: 'Payment held pending order completion and customer confirmation',
  })
  @IsString()
  @IsOptional()
  holdReason?: string;

  @ApiPropertyOptional({
    description: 'Conditions that must be met before releasing escrow',
    example: ['ORDER_COMPLETED', 'CUSTOMER_CONFIRMATION'],
    type: [String],
  })
  @IsArray()
  @IsOptional()
  releaseConditions?: string[];

  @ApiProperty({
    description: 'Payment method used for the transaction',
    example: 'credit_card',
  })
  @IsString()
  paymentMethod: string;

  @ApiPropertyOptional({
    description: 'Payment reference from customer',
    example: 'PAY_REF_123456789',
  })
  @IsString()
  @IsOptional()
  paymentReference?: string;

  @ApiPropertyOptional({
    description: 'Transaction ID from payment gateway',
    example: 'pi_1234567890abcdef',
  })
  @IsString()
  @IsOptional()
  gatewayTransactionId?: string;

  @ApiProperty({
    description: 'Date when payment was held in escrow',
    example: '2023-01-15T08:30:00Z',
    format: 'date-time',
  })
  @IsDate()
  heldAt: Date;

  @ApiPropertyOptional({
    description: 'Date when payment was released',
    example: '2023-01-20T14:30:00Z',
    format: 'date-time',
  })
  @IsDate()
  @IsOptional()
  releasedAt?: Date;

  @ApiPropertyOptional({
    description: 'Date when payment was refunded',
    example: '2023-01-18T10:15:00Z',
    format: 'date-time',
  })
  @IsDate()
  @IsOptional()
  refundedAt?: Date;

  @ApiPropertyOptional({
    description: 'Date when escrow expires and requires manual review',
    example: '2023-02-15T08:30:00Z',
    format: 'date-time',
  })
  @IsDate()
  @IsOptional()
  expiresAt?: Date;

  @ApiProperty({
    description: 'Date when escrow was created',
    example: '2023-01-15T08:30:00Z',
    format: 'date-time',
  })
  @IsDate()
  createdAt: Date;

  @ApiProperty({
    description: 'Date when escrow was last updated',
    example: '2023-01-20T14:30:00Z',
    format: 'date-time',
  })
  @IsDate()
  updatedAt: Date;
}

export class EscrowEventDto {
  @ApiProperty({
    description: 'Unique identifier of the escrow event',
    example: '123e4567-e89b-12d3-a456-************',
    format: 'uuid',
  })
  @IsString()
  id: string;

  @ApiProperty({
    description: 'Escrow transaction ID this event belongs to',
    example: '123e4567-e89b-12d3-a456-************',
    format: 'uuid',
  })
  @IsString()
  escrowTransactionId: string;

  @ApiProperty({
    description: 'Type of escrow event',
    enum: EscrowEventType,
    example: EscrowEventType.PAYMENT_HELD,
  })
  @IsEnum(EscrowEventType)
  eventType: EscrowEventType;

  @ApiProperty({
    description: 'Description of the event',
    example: 'Payment of 150.00 USD held in escrow',
  })
  @IsString()
  description: string;

  @ApiPropertyOptional({
    description: 'Amount involved in this event',
    example: '150.00',
    type: 'string',
  })
  @IsString()
  @IsOptional()
  amount?: string;

  @ApiPropertyOptional({
    description: 'User ID who triggered the event',
    example: '123e4567-e89b-12d3-a456-************',
    format: 'uuid',
  })
  @IsString()
  @IsOptional()
  triggeredBy?: string;

  @ApiPropertyOptional({
    description: 'Additional metadata about the event',
    example: {
      paymentMethod: 'credit_card',
      platformFee: '7.50',
      tailorAmount: '142.50'
    },
  })
  @IsOptional()
  metadata?: any;

  @ApiProperty({
    description: 'Timestamp when the event occurred',
    example: '2023-01-15T08:30:00Z',
    format: 'date-time',
  })
  @IsDate()
  timestamp: Date;
}

export class EscrowSummaryDto {
  @ApiProperty({
    description: 'Total amount held in escrow across all transactions',
    example: '15000.00',
    type: 'string',
  })
  @IsString()
  totalHeld: string;

  @ApiProperty({
    description: 'Total amount released to tailors',
    example: '45000.00',
    type: 'string',
  })
  @IsString()
  totalReleased: string;

  @ApiProperty({
    description: 'Total amount refunded to customers',
    example: '2500.00',
    type: 'string',
  })
  @IsString()
  totalRefunded: string;

  @ApiProperty({
    description: 'Total platform fees collected',
    example: '2375.00',
    type: 'string',
  })
  @IsString()
  totalPlatformFees: string;

  @ApiProperty({
    description: 'Number of active escrow transactions',
    example: 25,
    type: 'integer',
  })
  activeTransactions: number;

  @ApiProperty({
    description: 'Number of disputed escrow transactions',
    example: 3,
    type: 'integer',
  })
  disputedTransactions: number;

  @ApiProperty({
    description: 'Number of expired escrow transactions requiring review',
    example: 1,
    type: 'integer',
  })
  expiredTransactions: number;
}
