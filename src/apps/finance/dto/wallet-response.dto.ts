import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

export enum WalletStatus {
  ACTIVE = 'ACTIVE',
  SUSPENDED = 'SUSPENDED',
  FROZEN = 'FROZEN',
  CLOSED = 'CLOSED',
}

export enum WalletTransactionType {
  DEPOSIT = 'DEPOSIT',
  WITHDRAWAL = 'WITHDRAWAL',
  TRANSFER_OUT = 'TRANSFER_OUT',
  TRANSFER_IN = 'TRANSFER_IN',
  PAYMENT = 'PAYMENT',
  REFUND = 'REFUND',
  FEE = 'FEE',
  INTEREST = 'INTEREST',
  LOAN_DISBURSEMENT = 'LOAN_DISBURSEMENT',
  LOAN_REPAYMENT = 'LOAN_REPAYMENT',
  INSTALLMENT = 'INSTALLMENT',
  PENALTY = 'PENALTY',
  BONUS = 'BONUS',
  CASHBACK = 'CASHBACK',
}

export enum WalletTransactionStatus {
  PENDING = 'PENDING',
  COMPLETED = 'COMPLETED',
  FAILED = 'FAILED',
  CANCELLED = 'CANCELLED',
  REVERSED = 'REVERSED',
}

export class WalletDto {
  @ApiProperty({
    description: 'Unique identifier of the wallet',
    example: '123e4567-e89b-12d3-a456-************',
    format: 'uuid',
  })
  id: string;

  @ApiProperty({
    description: 'User ID who owns the wallet',
    example: '123e4567-e89b-12d3-a456-************',
    format: 'uuid',
  })
  userId: string;

  @ApiProperty({
    description: 'Current wallet balance in primary currency',
    example: '1250.75',
    type: 'string',
  })
  balance: string;

  @ApiProperty({
    description: 'Primary currency of the wallet',
    example: 'USD',
  })
  currency: string;

  @ApiProperty({
    description: 'Current status of the wallet',
    enum: WalletStatus,
    example: WalletStatus.ACTIVE,
  })
  status: WalletStatus;

  @ApiPropertyOptional({
    description: 'Daily transaction limit',
    example: '1000.00',
    type: 'string',
  })
  dailyLimit?: string;

  @ApiPropertyOptional({
    description: 'Monthly transaction limit',
    example: '10000.00',
    type: 'string',
  })
  monthlyLimit?: string;

  @ApiProperty({
    description: 'Whether the wallet is verified',
    example: true,
  })
  isVerified: boolean;

  @ApiPropertyOptional({
    description: 'Multi-currency balances',
    example: {
      USD: '1250.75',
      EUR: '1050.25',
      GBP: '950.50'
    },
  })
  currencyBalances?: any;

  @ApiPropertyOptional({
    description: 'Last activity timestamp',
    example: '2023-01-20T14:30:00Z',
    format: 'date-time',
  })
  lastActivity?: Date;

  @ApiProperty({
    description: 'Wallet creation timestamp',
    example: '2023-01-15T08:30:00Z',
    format: 'date-time',
  })
  createdAt: Date;

  @ApiProperty({
    description: 'Last update timestamp',
    example: '2023-01-20T14:30:00Z',
    format: 'date-time',
  })
  updatedAt: Date;
}

export class WalletTransactionDto {
  @ApiProperty({
    description: 'Unique identifier of the transaction',
    example: '123e4567-e89b-12d3-a456-************',
    format: 'uuid',
  })
  id: string;

  @ApiProperty({
    description: 'Wallet ID this transaction belongs to',
    example: '123e4567-e89b-12d3-a456-************',
    format: 'uuid',
  })
  walletId: string;

  @ApiProperty({
    description: 'Type of transaction',
    enum: WalletTransactionType,
    example: WalletTransactionType.DEPOSIT,
  })
  type: WalletTransactionType;

  @ApiProperty({
    description: 'Current status of the transaction',
    enum: WalletTransactionStatus,
    example: WalletTransactionStatus.COMPLETED,
  })
  status: WalletTransactionStatus;

  @ApiProperty({
    description: 'Transaction amount',
    example: '100.00',
    type: 'string',
  })
  amount: string;

  @ApiProperty({
    description: 'Currency of the transaction',
    example: 'USD',
  })
  currency: string;

  @ApiProperty({
    description: 'Wallet balance before transaction',
    example: '1150.75',
    type: 'string',
  })
  balanceBefore: string;

  @ApiProperty({
    description: 'Wallet balance after transaction',
    example: '1250.75',
    type: 'string',
  })
  balanceAfter: string;

  @ApiProperty({
    description: 'Transaction description',
    example: 'Wallet funding via credit card',
  })
  description: string;

  @ApiPropertyOptional({
    description: 'External reference (order ID, transfer ID, etc.)',
    example: 'ORDER_123456',
  })
  reference?: string;

  @ApiPropertyOptional({
    description: 'Additional transaction metadata',
    example: {
      paymentMethod: 'credit_card',
      gatewayTransactionId: 'gw_1234567890'
    },
  })
  metadata?: any;

  @ApiPropertyOptional({
    description: 'Fee amount charged for transaction',
    example: '2.50',
    type: 'string',
  })
  feeAmount?: string;

  @ApiPropertyOptional({
    description: 'Description of the fee',
    example: 'Processing fee',
  })
  feeDescription?: string;

  @ApiPropertyOptional({
    description: 'Payment gateway transaction ID',
    example: 'pi_1234567890abcdef',
  })
  gatewayTransactionId?: string;

  @ApiPropertyOptional({
    description: 'When the transaction was processed',
    example: '2023-01-20T14:30:00Z',
    format: 'date-time',
  })
  processedAt?: Date;

  @ApiProperty({
    description: 'Transaction creation timestamp',
    example: '2023-01-20T14:30:00Z',
    format: 'date-time',
  })
  createdAt: Date;

  @ApiProperty({
    description: 'Last update timestamp',
    example: '2023-01-20T14:30:00Z',
    format: 'date-time',
  })
  updatedAt: Date;
}

export class WalletTransferDto {
  @ApiProperty({
    description: 'Unique identifier of the transfer',
    example: '123e4567-e89b-12d3-a456-************',
    format: 'uuid',
  })
  id: string;

  @ApiProperty({
    description: 'Sender wallet ID',
    example: '123e4567-e89b-12d3-a456-************',
    format: 'uuid',
  })
  senderWalletId: string;

  @ApiProperty({
    description: 'Receiver wallet ID',
    example: '123e4567-e89b-12d3-a456-************',
    format: 'uuid',
  })
  receiverWalletId: string;

  @ApiProperty({
    description: 'Transfer amount',
    example: '75.00',
    type: 'string',
  })
  amount: string;

  @ApiProperty({
    description: 'Currency of the transfer',
    example: 'USD',
  })
  currency: string;

  @ApiPropertyOptional({
    description: 'Transfer description',
    example: 'Payment for tailoring services',
  })
  description?: string;

  @ApiPropertyOptional({
    description: 'Transfer reference',
    example: 'ORDER_123456',
  })
  reference?: string;

  @ApiProperty({
    description: 'Current status of the transfer',
    enum: WalletTransactionStatus,
    example: WalletTransactionStatus.COMPLETED,
  })
  status: WalletTransactionStatus;

  @ApiPropertyOptional({
    description: 'Transfer fee amount',
    example: '2.50',
    type: 'string',
  })
  transferFee?: string;

  @ApiPropertyOptional({
    description: 'User ID who pays the fee',
    example: '123e4567-e89b-12d3-a456-************',
    format: 'uuid',
  })
  feePayerId?: string;

  @ApiPropertyOptional({
    description: 'Exchange rate used for currency conversion',
    example: '0.85',
    type: 'string',
  })
  exchangeRate?: string;

  @ApiPropertyOptional({
    description: 'Amount in receiver currency after conversion',
    example: '63.75',
    type: 'string',
  })
  convertedAmount?: string;

  @ApiProperty({
    description: 'When the transfer was initiated',
    example: '2023-01-20T14:30:00Z',
    format: 'date-time',
  })
  initiatedAt: Date;

  @ApiPropertyOptional({
    description: 'When the transfer was completed',
    example: '2023-01-20T14:31:00Z',
    format: 'date-time',
  })
  completedAt?: Date;

  @ApiProperty({
    description: 'Transfer creation timestamp',
    example: '2023-01-20T14:30:00Z',
    format: 'date-time',
  })
  createdAt: Date;

  @ApiProperty({
    description: 'Last update timestamp',
    example: '2023-01-20T14:31:00Z',
    format: 'date-time',
  })
  updatedAt: Date;
}

export class WalletSummaryDto {
  @ApiProperty({
    description: 'Total number of active wallets',
    example: 1250,
  })
  totalWallets: number;

  @ApiProperty({
    description: 'Total value held in all wallets',
    example: '2500000.00',
    type: 'string',
  })
  totalValue: string;

  @ApiProperty({
    description: 'Total number of transactions today',
    example: 450,
  })
  dailyTransactions: number;

  @ApiProperty({
    description: 'Total transaction volume today',
    example: '125000.00',
    type: 'string',
  })
  dailyVolume: string;

  @ApiProperty({
    description: 'Average wallet balance',
    example: '2000.00',
    type: 'string',
  })
  averageBalance: string;

  @ApiProperty({
    description: 'Currency breakdown of total value',
    example: {
      USD: '2000000.00',
      EUR: '300000.00',
      GBP: '200000.00'
    },
  })
  currencyBreakdown: any;
}

export class ExchangeRateResponseDto {
  @ApiProperty({
    description: 'Unique identifier of the exchange rate record',
    example: '123e4567-e89b-12d3-a456-************',
    format: 'uuid',
  })
  id: string;

  @ApiProperty({
    description: 'Source currency code',
    example: 'USD',
  })
  fromCurrency: string;

  @ApiProperty({
    description: 'Target currency code',
    example: 'EUR',
  })
  toCurrency: string;

  @ApiProperty({
    description: 'Exchange rate',
    example: '0.85',
    type: 'string',
  })
  rate: string;

  @ApiProperty({
    description: 'Rate source/provider',
    example: 'FIXER_API',
  })
  source: string;

  @ApiProperty({
    description: 'When the rate becomes effective',
    example: '2023-01-20T14:30:00Z',
    format: 'date-time',
  })
  effectiveAt: Date;

  @ApiProperty({
    description: 'Rate creation timestamp',
    example: '2023-01-20T14:30:00Z',
    format: 'date-time',
  })
  createdAt: Date;
}
