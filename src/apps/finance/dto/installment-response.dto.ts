import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

export enum InstallmentPlanStatus {
  ACTIVE = 'ACTIVE',
  COMPLETED = 'COMPLETED',
  DEFAULTED = 'DEFAULTED',
  CANCELLED = 'CANCELLED',
}

export enum InstallmentStatus {
  PENDING = 'PENDING',
  PAID = 'PAID',
  OVERDUE = 'OVERDUE',
  FAILED = 'FAILED',
  WAIVED = 'WAIVED',
}

export class InstallmentPlanDto {
  @ApiProperty({
    description: 'Unique identifier of the installment plan',
    example: '123e4567-e89b-12d3-a456-************',
    format: 'uuid',
  })
  id: string;

  @ApiProperty({
    description: 'Order ID this plan belongs to',
    example: '123e4567-e89b-12d3-a456-************',
    format: 'uuid',
  })
  orderId: string;

  @ApiProperty({
    description: 'User ID who owns this plan',
    example: '123e4567-e89b-12d3-a456-************',
    format: 'uuid',
  })
  userId: string;

  @ApiProperty({
    description: 'Total amount to be paid',
    example: '1200.00',
    type: 'string',
  })
  totalAmount: string;

  @ApiProperty({
    description: 'Currency of the plan',
    example: 'USD',
  })
  currency: string;

  @ApiProperty({
    description: 'Number of installments',
    example: 6,
  })
  numberOfInstallments: number;

  @ApiProperty({
    description: 'Amount per installment',
    example: '200.00',
    type: 'string',
  })
  installmentAmount: string;

  @ApiProperty({
    description: 'Current status of the plan',
    enum: InstallmentPlanStatus,
    example: InstallmentPlanStatus.ACTIVE,
  })
  status: InstallmentPlanStatus;

  @ApiProperty({
    description: 'Annual interest rate',
    example: '12.00',
    type: 'string',
  })
  interestRate: string;

  @ApiProperty({
    description: 'One-time processing fee',
    example: '25.00',
    type: 'string',
  })
  processingFee: string;

  @ApiProperty({
    description: 'First payment due date',
    example: '2023-02-15T00:00:00Z',
    format: 'date-time',
  })
  firstPaymentDate: Date;

  @ApiProperty({
    description: 'Payment frequency',
    example: 'MONTHLY',
    enum: ['WEEKLY', 'MONTHLY'],
  })
  paymentFrequency: string;

  @ApiProperty({
    description: 'Early payment discount rate',
    example: '2.00',
    type: 'string',
  })
  earlyPaymentDiscount: string;

  @ApiProperty({
    description: 'Late fee rate',
    example: '5.00',
    type: 'string',
  })
  lateFeeRate: string;

  @ApiProperty({
    description: 'Grace period in days',
    example: 3,
  })
  gracePeriodDays: number;

  @ApiProperty({
    description: 'Total amount paid so far',
    example: '400.00',
    type: 'string',
  })
  totalPaid: string;

  @ApiProperty({
    description: 'Total late fees charged',
    example: '10.00',
    type: 'string',
  })
  totalLateFees: string;

  @ApiProperty({
    description: 'Total interest charged',
    example: '50.00',
    type: 'string',
  })
  totalInterest: string;

  @ApiProperty({
    description: 'Plan creation timestamp',
    example: '2023-01-15T08:30:00Z',
    format: 'date-time',
  })
  createdAt: Date;

  @ApiPropertyOptional({
    description: 'When plan was activated',
    example: '2023-01-15T08:30:00Z',
    format: 'date-time',
  })
  activatedAt?: Date;

  @ApiPropertyOptional({
    description: 'When plan was completed',
    example: '2023-07-15T14:30:00Z',
    format: 'date-time',
  })
  completedAt?: Date;

  @ApiPropertyOptional({
    description: 'When plan was cancelled',
    example: null,
    format: 'date-time',
  })
  cancelledAt?: Date;

  @ApiPropertyOptional({
    description: 'Individual installments in this plan',
    type: 'array',
    items: { type: 'object' },
  })
  installments?: any[];
}

export class InstallmentDto {
  @ApiProperty({
    description: 'Unique identifier of the installment',
    example: '123e4567-e89b-12d3-a456-************',
    format: 'uuid',
  })
  id: string;

  @ApiProperty({
    description: 'Plan ID this installment belongs to',
    example: '123e4567-e89b-12d3-a456-************',
    format: 'uuid',
  })
  planId: string;

  @ApiProperty({
    description: 'Installment number in sequence',
    example: 2,
  })
  installmentNumber: number;

  @ApiProperty({
    description: 'Installment amount',
    example: '200.00',
    type: 'string',
  })
  amount: string;

  @ApiProperty({
    description: 'Currency of the installment',
    example: 'USD',
  })
  currency: string;

  @ApiProperty({
    description: 'Principal portion of installment',
    example: '180.00',
    type: 'string',
  })
  principalAmount: string;

  @ApiPropertyOptional({
    description: 'Interest portion of installment',
    example: '20.00',
    type: 'string',
  })
  interestAmount?: string;

  @ApiProperty({
    description: 'Late fee amount charged',
    example: '0.00',
    type: 'string',
  })
  lateFeeAmount: string;

  @ApiProperty({
    description: 'Due date for this installment',
    example: '2023-02-15T00:00:00Z',
    format: 'date-time',
  })
  dueDate: Date;

  @ApiPropertyOptional({
    description: 'Actual payment date',
    example: '2023-02-14T14:30:00Z',
    format: 'date-time',
  })
  paidDate?: Date;

  @ApiProperty({
    description: 'Current status of the installment',
    enum: InstallmentStatus,
    example: InstallmentStatus.PAID,
  })
  status: InstallmentStatus;

  @ApiPropertyOptional({
    description: 'Payment method used',
    example: 'WALLET',
    enum: ['WALLET', 'ESCROW', 'BANK_TRANSFER', 'CREDIT_CARD'],
  })
  paymentMethod?: string;

  @ApiPropertyOptional({
    description: 'Transaction ID reference',
    example: 'TXN_123456789',
  })
  transactionId?: string;

  @ApiPropertyOptional({
    description: 'Escrow release ID if applicable',
    example: 'ESC_REL_123456',
  })
  escrowReleaseId?: string;

  @ApiProperty({
    description: 'Number of reminders sent',
    example: 1,
  })
  remindersSent: number;

  @ApiPropertyOptional({
    description: 'Last reminder sent timestamp',
    example: '2023-02-13T10:00:00Z',
    format: 'date-time',
  })
  lastReminderAt?: Date;

  @ApiProperty({
    description: 'Installment creation timestamp',
    example: '2023-01-15T08:30:00Z',
    format: 'date-time',
  })
  createdAt: Date;

  @ApiProperty({
    description: 'Last update timestamp',
    example: '2023-02-14T14:30:00Z',
    format: 'date-time',
  })
  updatedAt: Date;
}

export class InstallmentSummaryDto {
  @ApiProperty({
    description: 'Total number of active installment plans',
    example: 150,
  })
  activePlans: number;

  @ApiProperty({
    description: 'Total number of completed plans',
    example: 75,
  })
  completedPlans: number;

  @ApiProperty({
    description: 'Total number of defaulted plans',
    example: 5,
  })
  defaultedPlans: number;

  @ApiProperty({
    description: 'Total outstanding installment amount',
    example: '125000.00',
    type: 'string',
  })
  totalOutstanding: string;

  @ApiProperty({
    description: 'Total amount collected through installments',
    example: '450000.00',
    type: 'string',
  })
  totalCollected: string;

  @ApiProperty({
    description: 'Number of overdue installments',
    example: 12,
  })
  overdueInstallments: number;

  @ApiProperty({
    description: 'Total late fees collected',
    example: '2500.00',
    type: 'string',
  })
  totalLateFees: string;

  @ApiProperty({
    description: 'Average installment plan size',
    example: '850.00',
    type: 'string',
  })
  averagePlanSize: string;

  @ApiProperty({
    description: 'Default rate percentage',
    example: 2.17,
  })
  defaultRate: number;

  @ApiProperty({
    description: 'Collection efficiency percentage',
    example: 94.5,
  })
  collectionEfficiency: number;

  @ApiProperty({
    description: 'Installment plan distribution by number of payments',
    example: {
      '2': 25,
      '3': 40,
      '6': 60,
      '12': 45,
      '24': 20
    },
  })
  planDistribution: any;
}

export class PaymentReminderDto {
  @ApiProperty({
    description: 'Unique identifier of the reminder',
    example: '123e4567-e89b-12d3-a456-************',
    format: 'uuid',
  })
  id: string;

  @ApiProperty({
    description: 'User ID who should receive the reminder',
    example: '123e4567-e89b-12d3-a456-************',
    format: 'uuid',
  })
  userId: string;

  @ApiPropertyOptional({
    description: 'Installment ID for installment reminders',
    example: '123e4567-e89b-12d3-a456-************',
    format: 'uuid',
  })
  installmentId?: string;

  @ApiPropertyOptional({
    description: 'Loan payment ID for loan reminders',
    example: '123e4567-e89b-12d3-a456-************',
    format: 'uuid',
  })
  loanPaymentId?: string;

  @ApiProperty({
    description: 'Type of reminder',
    example: 'INSTALLMENT',
    enum: ['INSTALLMENT', 'LOAN_PAYMENT', 'WALLET_LOW_BALANCE'],
  })
  type: string;

  @ApiProperty({
    description: 'Reminder title',
    example: 'Upcoming Payment Due',
  })
  title: string;

  @ApiProperty({
    description: 'Reminder message',
    example: 'Your installment payment of $200.00 is due on February 15, 2023',
  })
  message: string;

  @ApiPropertyOptional({
    description: 'Amount due',
    example: '200.00',
    type: 'string',
  })
  amount?: string;

  @ApiPropertyOptional({
    description: 'Payment due date',
    example: '2023-02-15T00:00:00Z',
    format: 'date-time',
  })
  dueDate?: Date;

  @ApiProperty({
    description: 'Reminder status',
    example: 'SENT',
    enum: ['PENDING', 'SENT', 'ACKNOWLEDGED'],
  })
  status: string;

  @ApiPropertyOptional({
    description: 'When reminder was sent',
    example: '2023-02-13T10:00:00Z',
    format: 'date-time',
  })
  sentAt?: Date;

  @ApiPropertyOptional({
    description: 'When reminder was acknowledged',
    example: '2023-02-13T15:30:00Z',
    format: 'date-time',
  })
  acknowledgedAt?: Date;

  @ApiProperty({
    description: 'Whether email was sent',
    example: true,
  })
  emailSent: boolean;

  @ApiProperty({
    description: 'Whether SMS was sent',
    example: false,
  })
  smsSent: boolean;

  @ApiProperty({
    description: 'Whether push notification was sent',
    example: true,
  })
  pushSent: boolean;

  @ApiProperty({
    description: 'Whether in-app notification was sent',
    example: true,
  })
  inAppSent: boolean;

  @ApiProperty({
    description: 'Reminder creation timestamp',
    example: '2023-02-13T09:00:00Z',
    format: 'date-time',
  })
  createdAt: Date;

  @ApiProperty({
    description: 'Last update timestamp',
    example: '2023-02-13T15:30:00Z',
    format: 'date-time',
  })
  updatedAt: Date;
}
