import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

export enum LoanStatus {
  DRAFT = 'DRAFT',
  SUBMITTED = 'SUBMITTED',
  UNDER_REVIEW = 'UNDER_REVIEW',
  APPROVED = 'APPROVED',
  REJECTED = 'REJECTED',
  ACTIVE = 'ACTIVE',
  COMPLETED = 'COMPLETED',
  DEFAULTED = 'DEFAULTED',
  WRITTEN_OFF = 'WRITTEN_OFF',
}

export enum LoanType {
  PERSONAL = 'PERSONAL',
  BUSINESS = 'BUSINESS',
  ORDER_FINANCING = 'ORDER_FINANCING',
  EQUIPMENT = 'EQUIPMENT',
  WORKING_CAPITAL = 'WORKING_CAPITAL',
}

export enum CreditScoreGrade {
  EXCELLENT = 'EXCELLENT',
  GOOD = 'GOOD',
  FAIR = 'FAIR',
  POOR = 'POOR',
  NO_CREDIT = 'NO_CREDIT',
}

export class CreditProfileDto {
  @ApiProperty({
    description: 'Unique identifier of the credit profile',
    example: '123e4567-e89b-12d3-a456-************',
    format: 'uuid',
  })
  id: string;

  @ApiProperty({
    description: 'User ID who owns this credit profile',
    example: '123e4567-e89b-12d3-a456-************',
    format: 'uuid',
  })
  userId: string;

  @ApiPropertyOptional({
    description: 'Current credit score',
    example: 720,
    minimum: 300,
    maximum: 850,
  })
  creditScore?: number;

  @ApiPropertyOptional({
    description: 'Credit grade based on score',
    enum: CreditScoreGrade,
    example: CreditScoreGrade.GOOD,
  })
  creditGrade?: CreditScoreGrade;

  @ApiProperty({
    description: 'Total credit limit available',
    example: '10000.00',
    type: 'string',
  })
  totalCreditLimit: string;

  @ApiProperty({
    description: 'Available credit remaining',
    example: '7500.00',
    type: 'string',
  })
  availableCredit: string;

  @ApiProperty({
    description: 'Currently used credit',
    example: '2500.00',
    type: 'string',
  })
  usedCredit: string;

  @ApiPropertyOptional({
    description: 'Risk level assessment',
    example: 'MEDIUM',
    enum: ['LOW', 'MEDIUM', 'HIGH'],
  })
  riskLevel?: string;

  @ApiPropertyOptional({
    description: 'Identified risk factors',
    example: ['FIRST_TIME_BORROWER'],
    type: [String],
  })
  riskFactors?: string[];

  @ApiPropertyOptional({
    description: 'Monthly income',
    example: '5000.00',
    type: 'string',
  })
  monthlyIncome?: string;

  @ApiProperty({
    description: 'Whether income is verified',
    example: true,
  })
  incomeVerified: boolean;

  @ApiPropertyOptional({
    description: 'Employment status',
    example: 'EMPLOYED',
    enum: ['EMPLOYED', 'SELF_EMPLOYED', 'UNEMPLOYED'],
  })
  employmentStatus?: string;

  @ApiPropertyOptional({
    description: 'Payment history data',
    example: {
      onTimePayments: 24,
      latePayments: 1,
      missedPayments: 0
    },
  })
  paymentHistory?: any;

  @ApiPropertyOptional({
    description: 'Default history data',
    example: {
      totalDefaults: 0,
      lastDefaultDate: null
    },
  })
  defaultHistory?: any;

  @ApiPropertyOptional({
    description: 'Last credit score update',
    example: '2023-01-20T14:30:00Z',
    format: 'date-time',
  })
  lastUpdated?: Date;

  @ApiProperty({
    description: 'Profile creation timestamp',
    example: '2023-01-15T08:30:00Z',
    format: 'date-time',
  })
  createdAt: Date;

  @ApiProperty({
    description: 'Last update timestamp',
    example: '2023-01-20T14:30:00Z',
    format: 'date-time',
  })
  updatedAt: Date;
}

export class LoanApplicationDto {
  @ApiProperty({
    description: 'Unique identifier of the loan application',
    example: '123e4567-e89b-12d3-a456-************',
    format: 'uuid',
  })
  id: string;

  @ApiProperty({
    description: 'User ID who submitted the application',
    example: '123e4567-e89b-12d3-a456-************',
    format: 'uuid',
  })
  userId: string;

  @ApiProperty({
    description: 'Credit profile ID',
    example: '123e4567-e89b-12d3-a456-************',
    format: 'uuid',
  })
  creditProfileId: string;

  @ApiProperty({
    description: 'Type of loan requested',
    enum: LoanType,
    example: LoanType.PERSONAL,
  })
  loanType: LoanType;

  @ApiProperty({
    description: 'Requested loan amount',
    example: '5000.00',
    type: 'string',
  })
  requestedAmount: string;

  @ApiProperty({
    description: 'Currency of the loan',
    example: 'USD',
  })
  currency: string;

  @ApiProperty({
    description: 'Purpose of the loan',
    example: 'Equipment purchase for tailoring business',
  })
  purpose: string;

  @ApiProperty({
    description: 'Requested loan term in months',
    example: 24,
  })
  termMonths: number;

  @ApiProperty({
    description: 'Current application status',
    enum: LoanStatus,
    example: LoanStatus.UNDER_REVIEW,
  })
  status: LoanStatus;

  @ApiPropertyOptional({
    description: 'When application was submitted',
    example: '2023-01-15T10:30:00Z',
    format: 'date-time',
  })
  submittedAt?: Date;

  @ApiPropertyOptional({
    description: 'When application was reviewed',
    example: '2023-01-16T14:30:00Z',
    format: 'date-time',
  })
  reviewedAt?: Date;

  @ApiPropertyOptional({
    description: 'User ID who reviewed the application',
    example: '123e4567-e89b-12d3-a456-************',
    format: 'uuid',
  })
  reviewedBy?: string;

  @ApiPropertyOptional({
    description: 'Approved loan amount (may differ from requested)',
    example: '4500.00',
    type: 'string',
  })
  approvedAmount?: string;

  @ApiPropertyOptional({
    description: 'Approved interest rate',
    example: '12.50',
    type: 'string',
  })
  interestRate?: string;

  @ApiPropertyOptional({
    description: 'Reason for rejection',
    example: 'Insufficient credit history',
  })
  rejectionReason?: string;

  @ApiPropertyOptional({
    description: 'Supporting documents',
    example: ['https://example.com/income_statement.pdf'],
    type: [String],
  })
  documents?: string[];

  @ApiPropertyOptional({
    description: 'Risk assessment data',
    example: {
      creditScore: 720,
      riskLevel: 'MEDIUM',
      autoApprove: false,
      recommendedRate: '12.00'
    },
  })
  riskAssessment?: any;

  @ApiProperty({
    description: 'Application creation timestamp',
    example: '2023-01-15T08:30:00Z',
    format: 'date-time',
  })
  createdAt: Date;

  @ApiProperty({
    description: 'Last update timestamp',
    example: '2023-01-16T14:30:00Z',
    format: 'date-time',
  })
  updatedAt: Date;
}

export class LoanDto {
  @ApiProperty({
    description: 'Unique identifier of the loan',
    example: '123e4567-e89b-12d3-a456-************',
    format: 'uuid',
  })
  id: string;

  @ApiProperty({
    description: 'User ID who owns the loan',
    example: '123e4567-e89b-12d3-a456-************',
    format: 'uuid',
  })
  userId: string;

  @ApiProperty({
    description: 'Credit profile ID',
    example: '123e4567-e89b-12d3-a456-************',
    format: 'uuid',
  })
  creditProfileId: string;

  @ApiPropertyOptional({
    description: 'Original loan application ID',
    example: '123e4567-e89b-12d3-a456-************',
    format: 'uuid',
  })
  applicationId?: string;

  @ApiProperty({
    description: 'Type of loan',
    enum: LoanType,
    example: LoanType.PERSONAL,
  })
  loanType: LoanType;

  @ApiProperty({
    description: 'Original principal amount',
    example: '5000.00',
    type: 'string',
  })
  principalAmount: string;

  @ApiProperty({
    description: 'Currency of the loan',
    example: 'USD',
  })
  currency: string;

  @ApiProperty({
    description: 'Annual interest rate',
    example: '12.50',
    type: 'string',
  })
  interestRate: string;

  @ApiProperty({
    description: 'Loan term in months',
    example: 24,
  })
  termMonths: number;

  @ApiProperty({
    description: 'Current loan status',
    enum: LoanStatus,
    example: LoanStatus.ACTIVE,
  })
  status: LoanStatus;

  @ApiProperty({
    description: 'Current outstanding balance',
    example: '3750.00',
    type: 'string',
  })
  currentBalance: string;

  @ApiProperty({
    description: 'Total amount paid so far',
    example: '1250.00',
    type: 'string',
  })
  totalPaid: string;

  @ApiProperty({
    description: 'Monthly payment amount',
    example: '230.50',
    type: 'string',
  })
  monthlyPayment: string;

  @ApiPropertyOptional({
    description: 'Next payment due date',
    example: '2023-02-15T00:00:00Z',
    format: 'date-time',
  })
  nextPaymentDate?: Date;

  @ApiPropertyOptional({
    description: 'Last payment received date',
    example: '2023-01-15T14:30:00Z',
    format: 'date-time',
  })
  lastPaymentDate?: Date;

  @ApiProperty({
    description: 'Late fee rate (percentage)',
    example: '5.00',
    type: 'string',
  })
  lateFeeRate: string;

  @ApiProperty({
    description: 'Total late fees charged',
    example: '25.00',
    type: 'string',
  })
  totalLateFees: string;

  @ApiProperty({
    description: 'Total interest paid',
    example: '150.00',
    type: 'string',
  })
  totalInterestPaid: string;

  @ApiPropertyOptional({
    description: 'When loan was disbursed',
    example: '2023-01-15T08:30:00Z',
    format: 'date-time',
  })
  disbursedAt?: Date;

  @ApiPropertyOptional({
    description: 'Loan maturity date',
    example: '2025-01-15T00:00:00Z',
    format: 'date-time',
  })
  maturityDate?: Date;

  @ApiPropertyOptional({
    description: 'When loan was closed/completed',
    example: '2024-12-15T14:30:00Z',
    format: 'date-time',
  })
  closedAt?: Date;

  @ApiProperty({
    description: 'Number of days overdue',
    example: 0,
  })
  daysOverdue: number;

  @ApiPropertyOptional({
    description: 'When loan went into default',
    example: null,
    format: 'date-time',
  })
  defaultedAt?: Date;

  @ApiProperty({
    description: 'Loan creation timestamp',
    example: '2023-01-15T08:30:00Z',
    format: 'date-time',
  })
  createdAt: Date;

  @ApiProperty({
    description: 'Last update timestamp',
    example: '2023-01-20T14:30:00Z',
    format: 'date-time',
  })
  updatedAt: Date;
}

export class LoanPaymentDto {
  @ApiProperty({
    description: 'Unique identifier of the payment',
    example: '123e4567-e89b-12d3-a456-************',
    format: 'uuid',
  })
  id: string;

  @ApiProperty({
    description: 'Loan ID this payment belongs to',
    example: '123e4567-e89b-12d3-a456-************',
    format: 'uuid',
  })
  loanId: string;

  @ApiProperty({
    description: 'Total payment amount',
    example: '230.50',
    type: 'string',
  })
  amount: string;

  @ApiProperty({
    description: 'Currency of the payment',
    example: 'USD',
  })
  currency: string;

  @ApiProperty({
    description: 'Principal portion of payment',
    example: '200.00',
    type: 'string',
  })
  principalAmount: string;

  @ApiProperty({
    description: 'Interest portion of payment',
    example: '30.50',
    type: 'string',
  })
  interestAmount: string;

  @ApiProperty({
    description: 'Late fee portion of payment',
    example: '0.00',
    type: 'string',
  })
  lateFeeAmount: string;

  @ApiProperty({
    description: 'Original due date for this payment',
    example: '2023-02-15T00:00:00Z',
    format: 'date-time',
  })
  dueDate: Date;

  @ApiPropertyOptional({
    description: 'Actual payment date',
    example: '2023-02-14T14:30:00Z',
    format: 'date-time',
  })
  paidDate?: Date;

  @ApiProperty({
    description: 'Payment status',
    example: 'PAID',
    enum: ['PENDING', 'PAID', 'FAILED', 'OVERDUE'],
  })
  status: string;

  @ApiPropertyOptional({
    description: 'Payment method used',
    example: 'WALLET',
    enum: ['WALLET', 'BANK_TRANSFER', 'CREDIT_CARD', 'AUTO_DEBIT'],
  })
  paymentMethod?: string;

  @ApiPropertyOptional({
    description: 'Transaction ID reference',
    example: 'TXN_123456789',
  })
  transactionId?: string;

  @ApiProperty({
    description: 'Loan balance before payment',
    example: '3950.00',
    type: 'string',
  })
  balanceBefore: string;

  @ApiProperty({
    description: 'Loan balance after payment',
    example: '3750.00',
    type: 'string',
  })
  balanceAfter: string;

  @ApiProperty({
    description: 'Payment creation timestamp',
    example: '2023-02-14T14:30:00Z',
    format: 'date-time',
  })
  createdAt: Date;

  @ApiProperty({
    description: 'Last update timestamp',
    example: '2023-02-14T14:30:00Z',
    format: 'date-time',
  })
  updatedAt: Date;
}

export class CreditSummaryDto {
  @ApiProperty({
    description: 'Total number of credit profiles',
    example: 1250,
  })
  totalProfiles: number;

  @ApiProperty({
    description: 'Total credit limit across all profiles',
    example: '25000000.00',
    type: 'string',
  })
  totalCreditLimit: string;

  @ApiProperty({
    description: 'Total credit currently in use',
    example: '8500000.00',
    type: 'string',
  })
  totalUsedCredit: string;

  @ApiProperty({
    description: 'Average credit score',
    example: 685,
  })
  averageCreditScore: number;

  @ApiProperty({
    description: 'Total number of active loans',
    example: 450,
  })
  activeLoans: number;

  @ApiProperty({
    description: 'Total outstanding loan balance',
    example: '5250000.00',
    type: 'string',
  })
  totalOutstandingBalance: string;

  @ApiProperty({
    description: 'Number of loans in default',
    example: 12,
  })
  defaultedLoans: number;

  @ApiProperty({
    description: 'Default rate percentage',
    example: 2.67,
  })
  defaultRate: number;

  @ApiProperty({
    description: 'Credit grade distribution',
    example: {
      EXCELLENT: 125,
      GOOD: 450,
      FAIR: 350,
      POOR: 200,
      NO_CREDIT: 125
    },
  })
  creditGradeDistribution: any;
}
