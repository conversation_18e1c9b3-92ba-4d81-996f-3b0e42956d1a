import { Field, ObjectType, GraphQLISODateTime } from '@nestjs/graphql';

@ObjectType()
export class DailyRevenue {
  @Field()
  date: string;

  @Field()
  amount: string;
}

@ObjectType()
export class RevenueReport {
  @Field(() => GraphQLISODateTime)
  startDate: Date;

  @Field(() => GraphQLISODateTime)
  endDate: Date;

  @Field()
  totalRevenue: string;

  @Field()
  transactionCount: number;

  @Field(() => [DailyRevenue])
  revenueByDay: DailyRevenue[];
}