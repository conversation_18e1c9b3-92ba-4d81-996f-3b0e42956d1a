import { ObjectType, Field, ID, registerEnumType } from '@nestjs/graphql';

export enum TransactionType {
  SALE = 'SALE',
  PURCHASE = 'PURCHASE',
  REFUND = 'REFUND',
  EXPENSE = 'EXPENSE',
  PAYROLL = 'PAYROLL',
  TAX = 'TAX',
  OTHER = 'OTHER',
}

registerEnumType(TransactionType, {
  name: 'TransactionType',
  description: 'Types of financial transactions',
});

export enum TransactionStatus {
  PENDING = 'PENDING',
  COMPLETED = 'COMPLETED',
  FAILED = 'FAILED',
  VOIDED = 'VOIDED',
}

registerEnumType(TransactionStatus, {
  name: 'TransactionStatus',
  description: 'Status of financial transactions',
});

@ObjectType()
export class Transaction {
  @Field(() => ID)
  id: string;

  @Field(() => TransactionType)
  type: TransactionType;

  @Field(() => TransactionStatus)
  status: TransactionStatus;

  @Field(() => String)
  amount: string;

  @Field(() => String, { nullable: true })
  currency?: string;

  @Field(() => String)
  description: string;

  @Field(() => ID, { nullable: true })
  orderId?: string;

  @Field(() => ID, { nullable: true })
  customerId?: string;

  @Field(() => ID, { nullable: true })
  invoiceId?: string;

  @Field(() => String, { nullable: true })
  paymentMethod?: string;

  @Field(() => String, { nullable: true })
  referenceNumber?: string;

  @Field(() => String, { nullable: true })
  notes?: string;

  @Field(() => Date)
  transactionDate: Date;

  @Field(() => Date)
  createdAt: Date;

  @Field(() => Date)
  updatedAt: Date;
}

@ObjectType()
export class Invoice {
  @Field(() => ID)
  id: string;

  @Field(() => ID, { nullable: true })
  customerId?: string;

  @Field(() => String)
  invoiceNumber: string;

  @Field(() => String)
  subtotal: string;

  @Field(() => String)
  tax: string;

  @Field(() => String, { nullable: true })
  discount?: string;

  @Field(() => String)
  total: string;

  @Field(() => String, { nullable: true })
  status?: string;

  @Field(() => ID, { nullable: true })
  orderId?: string;

  @Field(() => Date)
  issueDate: Date;

  @Field(() => Date)
  dueDate: Date;

  @Field(() => Date, { nullable: true })
  paidDate?: Date;

  @Field(() => Date)
  createdAt: Date;

  @Field(() => Date)
  updatedAt: Date;
}
