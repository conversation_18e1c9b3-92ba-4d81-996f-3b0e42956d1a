import { Resolver, Query, Mutation, Args, ID } from '@nestjs/graphql';
import { UseGuards } from '@nestjs/common';
import { FinanceService } from '../services/finance.service';
import { Transaction, Invoice } from '../entities/transaction.entity';
import { CreateTransactionDto } from '../dto/create-transaction.dto';
import { CreateInvoiceDto } from '../dto/create-invoice.dto';
import { UpdateTransactionDto } from '../dto/update-transaction.dto';
import { JwtAuthGuard } from '../../../core/auth/guards/jwt-auth.guard';
import { RolesGuard } from '../../../core/auth/guards/roles.guard';
import { Roles } from '../../../core/auth/decorators/roles.decorator';
import { TransactionStatus } from '../entities/transaction.entity';
import { RevenueReport } from '../entities/revenue-report.entity';

@Resolver()
export class FinanceResolver {
  constructor(private readonly financeService: FinanceService) {}

  // Transaction Resolvers
  @Mutation(() => Transaction)
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles('admin', 'finance')
  createTransaction(@Args('createTransactionInput') createTransactionDto: CreateTransactionDto) {
    return this.financeService.createTransaction(createTransactionDto);
  }

  @Query(() => [Transaction], { name: 'transactions' })
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles('admin', 'finance')
  findAllTransactions() {
    return this.financeService.findAllTransactions();
  }

  @Query(() => Transaction, { name: 'transaction' })
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles('admin', 'finance')
  findOneTransaction(@Args('id', { type: () => ID }) id: string) {
    return this.financeService.findOneTransaction(id);
  }

  @Mutation(() => Transaction)
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles('admin', 'finance')
  updateTransaction(
    @Args('id', { type: () => ID }) id: string,
    @Args('updateTransactionInput') updateTransactionDto: UpdateTransactionDto,
  ) {
    return this.financeService.updateTransaction(id, updateTransactionDto);
  }

  @Mutation(() => Transaction)
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles('admin', 'finance')
  updateTransactionStatus(
    @Args('id', { type: () => ID }) id: string,
    @Args('status', { type: () => String }) status: TransactionStatus,
  ) {
    return this.financeService.updateTransactionStatus(id, status);
  }

  // Invoice Resolvers
  @Mutation(() => Invoice)
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles('admin', 'finance')
  createInvoice(@Args('createInvoiceInput') createInvoiceDto: CreateInvoiceDto) {
    return this.financeService.createInvoice(createInvoiceDto);
  }

  @Query(() => [Invoice], { name: 'invoices' })
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles('admin', 'finance')
  findAllInvoices() {
    return this.financeService.findAllInvoices();
  }

  @Query(() => Invoice, { name: 'invoice' })
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles('admin', 'finance')
  findOneInvoice(@Args('id', { type: () => ID }) id: string) {
    return this.financeService.findOneInvoice(id);
  }

  @Mutation(() => Invoice, { name: 'markInvoiceAsPaid' })
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles('admin', 'finance')
  markInvoiceAsPaid(
    @Args('id', { type: () => ID }) id: string,
    @Args('paymentDate', { type: () => Date, nullable: true }) paymentDate: Date,
    @Args('paymentReference', { type: () => String, nullable: true }) paymentReference: string,
  ) {
    return this.financeService.markInvoiceAsPaid(id, paymentDate, paymentReference);
  }

  // Reports
  @Query(() => RevenueReport, { name: 'revenueReport' })
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles('admin', 'finance')
  getRevenueReport(
    @Args('startDate', { type: () => Date }) startDate: Date,
    @Args('endDate', { type: () => Date }) endDate: Date,
  ) {
    return this.financeService.getRevenueReport(startDate, endDate);
  }
}
