import { Module } from '@nestjs/common';
import { FinanceService } from './services/finance.service';
import { EscrowService } from './services/escrow.service';
import { FinanceController } from './controllers/finance.controller';
import { EscrowController } from './controllers/escrow.controller';
import { FinanceResolver } from './resolvers/finance.resolver';
import { CoreModule } from '@core/core.module';
import { SharedModule } from '@shared/shared.module';
import { OrderModule } from '@apps/order/order.module';
import { MongoDbModule } from '@infra/mongodb/mongodb.module';
import { WebsocketModule } from '@infra/websocket/websocket.module';
import { RabbitMQModule } from '@infra/rabbitmq/rabbitmq.module';

@Module({
  imports: [
    CoreModule, 
    SharedModule, 
    OrderModule,
    MongoDbModule,
    WebsocketModule,
    RabbitMQModule,
  ],
  providers: [FinanceService, EscrowService, FinanceResolver],
  controllers: [FinanceController, EscrowController],
  exports: [FinanceService, EscrowService],
})
export class FinanceModule {}
