import { Module } from '@nestjs/common';
import { FinanceService } from './services/finance.service';
import { EscrowService } from './services/escrow.service';
import { WalletService } from './services/wallet.service';
import { CreditService } from './services/credit.service';
import { InstallmentService } from './services/installment.service';
import { FinanceController } from './controllers/finance.controller';
import { EscrowController } from './controllers/escrow.controller';
import { WalletController } from './controllers/wallet.controller';
import { FinanceResolver } from './resolvers/finance.resolver';
import { CoreModule } from '@core/core.module';
import { SharedModule } from '@shared/shared.module';
import { OrderModule } from '@apps/order/order.module';
import { MongoDbModule } from '@infra/mongodb/mongodb.module';
import { WebsocketModule } from '@infra/websocket/websocket.module';
import { RabbitMQModule } from '@infra/rabbitmq/rabbitmq.module';

@Module({
  imports: [
    CoreModule, 
    SharedModule, 
    OrderModule,
    MongoDbModule,
    WebsocketModule,
    RabbitMQModule,
  ],
  providers: [
    FinanceService,
    EscrowService,
    WalletService,
    CreditService,
    InstallmentService,
    FinanceResolver
  ],
  controllers: [
    FinanceController,
    EscrowController,
    WalletController
  ],
  exports: [
    FinanceService,
    EscrowService,
    WalletService,
    CreditService,
    InstallmentService
  ],
})
export class FinanceModule {}
