import { Controller, Get, Post, Body, Patch, Param, Query, UseGuards } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth, ApiParam, ApiBody, ApiQuery } from '@nestjs/swagger';
import { InstallmentService } from '../services/installment.service';
import { 
  CreateInstallmentPlanDto, 
  ProcessInstallmentPaymentDto,
  ProcessEarlyPaymentDto,
  UpdateInstallmentPlanDto,
  CancelInstallmentPlanDto,
  RescheduleInstallmentDto
} from '../dto/installment.dto';
import { 
  InstallmentPlanDto, 
  InstallmentDto, 
  InstallmentSummaryDto,
  PaymentReminderDto,
  InstallmentPlanStatus,
  InstallmentStatus
} from '../dto/installment-response.dto';
import { JwtAuthGuard } from '@core/auth/guards/jwt-auth.guard';
import { RolesGuard } from '@core/auth/guards/roles.guard';
import { Roles } from '@core/auth/decorators/roles.decorator';
import { CurrentUser } from '@core/auth/decorators/current-user.decorator';
import { User } from '@apps/user/entities/user.entity';
import { ErrorResponseDto, UnauthorizedResponseDto, ForbiddenResponseDto, NotFoundResponseDto } from '@shared/dto/paginated-response.dto';

@ApiTags('installments')
@ApiBearerAuth('JWT-auth')
@Controller('finance/installments')
export class InstallmentController {
  constructor(private readonly installmentService: InstallmentService) {}

  @Post()
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles('admin', 'finance', 'staff')
  @ApiOperation({
    summary: 'Create installment plan',
    description: 'Creates a new installment payment plan for an order.',
  })
  @ApiResponse({
    status: 201,
    description: 'Installment plan created successfully.',
    type: InstallmentPlanDto,
    example: {
      id: '123e4567-e89b-12d3-a456-************',
      orderId: '123e4567-e89b-12d3-a456-************',
      userId: '123e4567-e89b-12d3-a456-************',
      totalAmount: '1200.00',
      numberOfInstallments: 6,
      installmentAmount: '200.00',
      status: 'ACTIVE',
      interestRate: '12.00',
      firstPaymentDate: '2023-02-15T00:00:00Z'
    }
  })
  @ApiResponse({
    status: 400,
    description: 'Bad Request - Order already has installment plan or invalid data.',
    type: ErrorResponseDto
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - Invalid or missing authentication token.',
    type: UnauthorizedResponseDto
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Requires admin, finance, or staff role.',
    type: ForbiddenResponseDto
  })
  @ApiResponse({
    status: 404,
    description: 'Order not found.',
    type: NotFoundResponseDto
  })
  @ApiBody({
    type: CreateInstallmentPlanDto,
    description: 'Installment plan data',
    examples: {
      'standard-plan': {
        summary: 'Standard 6-Month Plan',
        description: 'Standard installment plan with monthly payments',
        value: {
          orderId: '123e4567-e89b-12d3-a456-************',
          totalAmount: '1200.00',
          currency: 'USD',
          numberOfInstallments: 6,
          interestRate: '12.00',
          processingFee: '25.00',
          firstPaymentDate: '2023-02-15T00:00:00Z',
          paymentFrequency: 'MONTHLY',
          earlyPaymentDiscount: '2.00',
          lateFeeRate: '5.00',
          gracePeriodDays: 3
        }
      },
      'no-interest-plan': {
        summary: 'No Interest Plan',
        description: 'Interest-free installment plan for promotional offer',
        value: {
          orderId: '123e4567-e89b-12d3-a456-************',
          totalAmount: '800.00',
          currency: 'USD',
          numberOfInstallments: 3,
          interestRate: '0.00',
          processingFee: '0.00',
          firstPaymentDate: '2023-02-15T00:00:00Z',
          paymentFrequency: 'MONTHLY',
          earlyPaymentDiscount: '0.00',
          lateFeeRate: '3.00',
          gracePeriodDays: 5
        }
      }
    }
  })
  createInstallmentPlan(@Body() createInstallmentPlanDto: CreateInstallmentPlanDto): Promise<InstallmentPlanDto> {
    return this.installmentService.createInstallmentPlan(createInstallmentPlanDto);
  }

  @Get(':id')
  @UseGuards(JwtAuthGuard)
  @ApiOperation({
    summary: 'Get installment plan',
    description: 'Retrieve a specific installment plan with all installments.',
  })
  @ApiResponse({
    status: 200,
    description: 'Successfully retrieved installment plan.',
    type: InstallmentPlanDto,
  })
  @ApiResponse({
    status: 404,
    description: 'Installment plan not found.',
    type: NotFoundResponseDto
  })
  @ApiParam({
    name: 'id',
    description: 'Installment plan ID',
    format: 'uuid',
    example: '123e4567-e89b-12d3-a456-************'
  })
  getInstallmentPlan(@Param('id') id: string): Promise<InstallmentPlanDto> {
    return this.installmentService.getInstallmentPlan(id);
  }

  @Get('order/:orderId')
  @UseGuards(JwtAuthGuard)
  @ApiOperation({
    summary: 'Get installment plan by order',
    description: 'Retrieve installment plan for a specific order.',
  })
  @ApiResponse({
    status: 200,
    description: 'Successfully retrieved installment plan.',
    type: InstallmentPlanDto,
  })
  @ApiResponse({
    status: 404,
    description: 'Installment plan not found for this order.',
    type: NotFoundResponseDto
  })
  @ApiParam({
    name: 'orderId',
    description: 'Order ID to find installment plan for',
    format: 'uuid',
    example: '123e4567-e89b-12d3-a456-************'
  })
  getInstallmentPlanByOrder(@Param('orderId') orderId: string): Promise<InstallmentPlanDto | null> {
    return this.installmentService.getInstallmentPlanByOrderId(orderId);
  }

  @Get('user/:userId')
  @UseGuards(JwtAuthGuard)
  @ApiOperation({
    summary: 'Get user installment plans',
    description: 'Retrieve all installment plans for a specific user.',
  })
  @ApiResponse({
    status: 200,
    description: 'Successfully retrieved installment plans.',
    type: [InstallmentPlanDto],
  })
  @ApiParam({
    name: 'userId',
    description: 'User ID to get installment plans for',
    format: 'uuid',
    example: '123e4567-e89b-12d3-a456-************'
  })
  getUserInstallmentPlans(@Param('userId') userId: string): Promise<InstallmentPlanDto[]> {
    return this.installmentService.getUserInstallmentPlans(userId);
  }

  @Get('my-plans')
  @UseGuards(JwtAuthGuard)
  @ApiOperation({
    summary: 'Get current user installment plans',
    description: 'Retrieve all installment plans for the authenticated user.',
  })
  @ApiResponse({
    status: 200,
    description: 'Successfully retrieved installment plans.',
    type: [InstallmentPlanDto],
  })
  getMyInstallmentPlans(@CurrentUser() user: User): Promise<InstallmentPlanDto[]> {
    return this.installmentService.getUserInstallmentPlans(user.id);
  }

  @Post('payment')
  @UseGuards(JwtAuthGuard)
  @ApiOperation({
    summary: 'Process installment payment',
    description: 'Process a payment for a specific installment.',
  })
  @ApiResponse({
    status: 201,
    description: 'Installment payment processed successfully.',
    type: InstallmentDto,
  })
  @ApiResponse({
    status: 400,
    description: 'Bad Request - Installment not pending or insufficient payment amount.',
    type: ErrorResponseDto
  })
  @ApiResponse({
    status: 404,
    description: 'Installment not found.',
    type: NotFoundResponseDto
  })
  @ApiBody({
    type: ProcessInstallmentPaymentDto,
    description: 'Installment payment data',
    examples: {
      'wallet-payment': {
        summary: 'Wallet Payment',
        description: 'Pay installment using wallet balance',
        value: {
          installmentId: '123e4567-e89b-12d3-a456-************',
          amount: '200.00',
          paymentMethod: 'WALLET',
          releaseEscrow: true,
          notes: 'Payment processed via digital wallet'
        }
      },
      'bank-payment': {
        summary: 'Bank Transfer Payment',
        description: 'Pay installment using bank transfer',
        value: {
          installmentId: '123e4567-e89b-12d3-a456-************',
          amount: '200.00',
          paymentMethod: 'BANK_TRANSFER',
          transactionId: 'TXN_123456789',
          releaseEscrow: false,
          notes: 'Bank transfer payment confirmation'
        }
      }
    }
  })
  processInstallmentPayment(@Body() processPaymentDto: ProcessInstallmentPaymentDto): Promise<InstallmentDto> {
    return this.installmentService.processInstallmentPayment(processPaymentDto);
  }

  @Post('early-payment')
  @UseGuards(JwtAuthGuard)
  @ApiOperation({
    summary: 'Process early payment',
    description: 'Process early payment to complete installment plan with discount.',
  })
  @ApiResponse({
    status: 200,
    description: 'Early payment processed successfully.',
    type: InstallmentPlanDto,
  })
  @ApiResponse({
    status: 400,
    description: 'Bad Request - Insufficient payment amount or plan not eligible.',
    type: ErrorResponseDto
  })
  @ApiResponse({
    status: 404,
    description: 'Installment plan not found.',
    type: NotFoundResponseDto
  })
  @ApiBody({
    type: ProcessEarlyPaymentDto,
    description: 'Early payment data',
    examples: {
      'full-early-payment': {
        summary: 'Full Early Payment',
        description: 'Pay off remaining balance early with discount',
        value: {
          planId: '123e4567-e89b-12d3-a456-************',
          paymentAmount: '980.00',
          paymentMethod: 'WALLET',
          transactionId: 'TXN_EARLY_123456'
        }
      }
    }
  })
  processEarlyPayment(@Body() processEarlyPaymentDto: ProcessEarlyPaymentDto): Promise<InstallmentPlanDto> {
    return this.installmentService.processEarlyPayment(
      processEarlyPaymentDto.planId,
      processEarlyPaymentDto.paymentAmount
    );
  }

  @Get('upcoming')
  @UseGuards(JwtAuthGuard)
  @ApiOperation({
    summary: 'Get upcoming installments',
    description: 'Retrieve upcoming installments for the authenticated user.',
  })
  @ApiResponse({
    status: 200,
    description: 'Successfully retrieved upcoming installments.',
    type: [InstallmentDto],
  })
  @ApiQuery({
    name: 'days',
    required: false,
    type: Number,
    description: 'Number of days to look ahead',
    example: 7
  })
  getUpcomingInstallments(
    @CurrentUser() user: User,
    @Query('days') days?: number,
  ): Promise<InstallmentDto[]> {
    return this.installmentService.getUpcomingInstallments(user.id, days);
  }

  @Get('overdue')
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles('admin', 'finance')
  @ApiOperation({
    summary: 'Get overdue installments',
    description: 'Retrieve all overdue installments with optional user filtering.',
  })
  @ApiResponse({
    status: 200,
    description: 'Successfully retrieved overdue installments.',
    type: [InstallmentDto],
  })
  @ApiQuery({
    name: 'userId',
    required: false,
    type: String,
    description: 'Filter by user ID',
    example: '123e4567-e89b-12d3-a456-************'
  })
  getOverdueInstallments(@Query('userId') userId?: string): Promise<InstallmentDto[]> {
    return this.installmentService.getOverdueInstallments(userId);
  }

  @Get('my-overdue')
  @UseGuards(JwtAuthGuard)
  @ApiOperation({
    summary: 'Get current user overdue installments',
    description: 'Retrieve overdue installments for the authenticated user.',
  })
  @ApiResponse({
    status: 200,
    description: 'Successfully retrieved overdue installments.',
    type: [InstallmentDto],
  })
  getMyOverdueInstallments(@CurrentUser() user: User): Promise<InstallmentDto[]> {
    return this.installmentService.getOverdueInstallments(user.id);
  }

  @Post('send-reminders')
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles('admin', 'finance', 'system')
  @ApiOperation({
    summary: 'Send payment reminders',
    description: 'Manually trigger payment reminders for upcoming installments.',
  })
  @ApiResponse({
    status: 200,
    description: 'Payment reminders sent successfully.',
    schema: {
      type: 'object',
      properties: {
        message: { type: 'string', example: 'Payment reminders sent successfully' },
        remindersSent: { type: 'number', example: 15 }
      }
    }
  })
  async sendPaymentReminders(): Promise<{ message: string; remindersSent: number }> {
    const remindersSent = await this.installmentService.sendPaymentReminders();
    return {
      message: 'Payment reminders sent successfully',
      remindersSent
    };
  }

  @Get('summary')
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles('admin', 'finance')
  @ApiOperation({
    summary: 'Get installment system summary',
    description: 'Retrieve summary statistics for the installment system.',
  })
  @ApiResponse({
    status: 200,
    description: 'Successfully retrieved installment summary.',
    type: InstallmentSummaryDto,
    example: {
      activePlans: 150,
      completedPlans: 75,
      defaultedPlans: 5,
      totalOutstanding: '125000.00',
      totalCollected: '450000.00',
      overdueInstallments: 12,
      totalLateFees: '2500.00',
      averagePlanSize: '850.00',
      defaultRate: 2.17,
      collectionEfficiency: 94.5
    }
  })
  getInstallmentSummary(): Promise<any> {
    return this.installmentService.getInstallmentSummary();
  }
}
