import { Controller, Get, Post, Body, Patch, Param, Query, UseGuards } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth, ApiParam, ApiBody, ApiQuery } from '@nestjs/swagger';
import { WalletService } from '../services/wallet.service';
import { CreateWalletDto, FundWalletDto, TransferFundsDto, WithdrawFundsDto, UpdateWalletStatusDto, UpdateWalletLimitsDto } from '../dto/wallet.dto';
import { WalletDto, WalletTransactionDto, WalletTransferDto, WalletSummaryDto } from '../dto/wallet-response.dto';
import { JwtAuthGuard } from '@core/auth/guards/jwt-auth.guard';
import { RolesGuard } from '@core/auth/guards/roles.guard';
import { Roles } from '@core/auth/decorators/roles.decorator';
import { CurrentUser } from '@core/auth/decorators/current-user.decorator';
import { User } from '@apps/user/entities/user.entity';
import { ErrorResponseDto, UnauthorizedResponseDto, ForbiddenResponseDto, NotFoundResponseDto } from '@shared/dto/paginated-response.dto';

@ApiTags('wallet')
@ApiBearerAuth('JWT-auth')
@Controller('finance/wallet')
export class WalletController {
  constructor(private readonly walletService: WalletService) {}

  @Post()
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles('admin', 'finance')
  @ApiOperation({
    summary: 'Create digital wallet',
    description: 'Creates a new digital wallet for a user with optional initial balance.',
  })
  @ApiResponse({
    status: 201,
    description: 'Wallet created successfully.',
    type: WalletDto,
    example: {
      id: '123e4567-e89b-12d3-a456-426614174000',
      userId: '123e4567-e89b-12d3-a456-************',
      balance: '100.00',
      currency: 'USD',
      status: 'ACTIVE',
      dailyLimit: '1000.00',
      monthlyLimit: '10000.00',
      isVerified: false,
      currencyBalances: { USD: '100.00' },
      lastActivity: null,
      createdAt: '2023-01-15T08:30:00Z',
      updatedAt: '2023-01-15T08:30:00Z'
    }
  })
  @ApiResponse({
    status: 400,
    description: 'Bad Request - User already has a wallet or invalid data.',
    type: ErrorResponseDto
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - Invalid or missing authentication token.',
    type: UnauthorizedResponseDto
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Requires admin or finance role.',
    type: ForbiddenResponseDto
  })
  @ApiBody({
    type: CreateWalletDto,
    description: 'Wallet creation data',
    examples: {
      'basic-wallet': {
        summary: 'Basic Wallet Creation',
        description: 'Create a wallet with zero initial balance',
        value: {
          userId: '123e4567-e89b-12d3-a456-************',
          currency: 'USD',
          dailyLimit: '1000.00',
          monthlyLimit: '10000.00'
        }
      },
      'funded-wallet': {
        summary: 'Wallet with Initial Balance',
        description: 'Create a wallet with initial funding',
        value: {
          userId: '123e4567-e89b-12d3-a456-************',
          initialBalance: '500.00',
          currency: 'USD',
          dailyLimit: '2000.00',
          monthlyLimit: '20000.00'
        }
      }
    }
  })
  create(@Body() createWalletDto: CreateWalletDto): Promise<WalletDto> {
    return this.walletService.createWallet(createWalletDto);
  }

  @Get('user/:userId')
  @UseGuards(JwtAuthGuard)
  @ApiOperation({
    summary: 'Get user wallet',
    description: 'Retrieve wallet information for a specific user.',
  })
  @ApiResponse({
    status: 200,
    description: 'Successfully retrieved wallet.',
    type: WalletDto,
  })
  @ApiResponse({
    status: 404,
    description: 'Wallet not found for this user.',
    type: NotFoundResponseDto
  })
  @ApiParam({
    name: 'userId',
    description: 'User ID to get wallet for',
    format: 'uuid',
    example: '123e4567-e89b-12d3-a456-************'
  })
  getWallet(@Param('userId') userId: string): Promise<WalletDto> {
    return this.walletService.getWallet(userId);
  }

  @Get('my-wallet')
  @UseGuards(JwtAuthGuard)
  @ApiOperation({
    summary: 'Get current user wallet',
    description: 'Retrieve wallet information for the authenticated user.',
  })
  @ApiResponse({
    status: 200,
    description: 'Successfully retrieved wallet.',
    type: WalletDto,
  })
  @ApiResponse({
    status: 404,
    description: 'Wallet not found for current user.',
    type: NotFoundResponseDto
  })
  getMyWallet(@CurrentUser() user: User): Promise<WalletDto> {
    return this.walletService.getWallet(user.id);
  }

  @Post('fund')
  @UseGuards(JwtAuthGuard)
  @ApiOperation({
    summary: 'Fund wallet',
    description: 'Add funds to a user wallet using various payment methods.',
  })
  @ApiResponse({
    status: 201,
    description: 'Wallet funded successfully.',
    type: WalletTransactionDto,
  })
  @ApiResponse({
    status: 400,
    description: 'Bad Request - Invalid amount, payment method, or wallet status.',
    type: ErrorResponseDto
  })
  @ApiResponse({
    status: 404,
    description: 'Wallet not found.',
    type: NotFoundResponseDto
  })
  @ApiBody({
    type: FundWalletDto,
    description: 'Wallet funding data',
    examples: {
      'credit-card-funding': {
        summary: 'Credit Card Funding',
        description: 'Fund wallet using credit card',
        value: {
          userId: '123e4567-e89b-12d3-a456-************',
          amount: '100.00',
          paymentMethod: 'credit_card',
          currency: 'USD',
          description: 'Wallet top-up via credit card',
          source: 'USER_INITIATED'
        }
      },
      'bank-transfer-funding': {
        summary: 'Bank Transfer Funding',
        description: 'Fund wallet using bank transfer',
        value: {
          userId: '123e4567-e89b-12d3-a456-************',
          amount: '500.00',
          paymentMethod: 'bank_transfer',
          currency: 'USD',
          description: 'Large wallet funding via bank transfer',
          source: 'USER_INITIATED'
        }
      }
    }
  })
  fundWallet(@Body() fundWalletDto: FundWalletDto): Promise<WalletTransactionDto> {
    return this.walletService.fundWallet(fundWalletDto);
  }

  @Post('withdraw')
  @UseGuards(JwtAuthGuard)
  @ApiOperation({
    summary: 'Withdraw funds',
    description: 'Withdraw funds from user wallet to external account.',
  })
  @ApiResponse({
    status: 201,
    description: 'Withdrawal processed successfully.',
    type: WalletTransactionDto,
  })
  @ApiResponse({
    status: 400,
    description: 'Bad Request - Insufficient balance, invalid amount, or wallet status.',
    type: ErrorResponseDto
  })
  @ApiResponse({
    status: 404,
    description: 'Wallet not found.',
    type: NotFoundResponseDto
  })
  @ApiBody({
    type: WithdrawFundsDto,
    description: 'Withdrawal data',
    examples: {
      'bank-withdrawal': {
        summary: 'Bank Account Withdrawal',
        description: 'Withdraw funds to bank account',
        value: {
          userId: '123e4567-e89b-12d3-a456-************',
          amount: '250.00',
          withdrawalMethod: 'bank_transfer',
          currency: 'USD',
          description: 'Withdrawal to primary bank account',
          destination: 'bank_account_****1234'
        }
      }
    }
  })
  withdrawFunds(@Body() withdrawFundsDto: WithdrawFundsDto): Promise<WalletTransactionDto> {
    return this.walletService.withdrawFunds(withdrawFundsDto);
  }

  @Post('transfer')
  @UseGuards(JwtAuthGuard)
  @ApiOperation({
    summary: 'Transfer funds between wallets',
    description: 'Transfer funds from one user wallet to another with optional currency conversion.',
  })
  @ApiResponse({
    status: 201,
    description: 'Transfer completed successfully.',
    type: WalletTransferDto,
  })
  @ApiResponse({
    status: 400,
    description: 'Bad Request - Insufficient balance, invalid users, or wallet status.',
    type: ErrorResponseDto
  })
  @ApiResponse({
    status: 404,
    description: 'Sender or receiver wallet not found.',
    type: NotFoundResponseDto
  })
  @ApiBody({
    type: TransferFundsDto,
    description: 'Transfer data',
    examples: {
      'order-payment': {
        summary: 'Order Payment Transfer',
        description: 'Transfer funds for order payment',
        value: {
          senderUserId: '123e4567-e89b-12d3-a456-************',
          receiverUserId: '123e4567-e89b-12d3-a456-426614174002',
          amount: '150.00',
          currency: 'USD',
          description: 'Payment for custom tailoring order',
          reference: 'ORDER_789012',
          transferFee: '2.50'
        }
      },
      'peer-transfer': {
        summary: 'Peer-to-Peer Transfer',
        description: 'Simple transfer between users',
        value: {
          senderUserId: '123e4567-e89b-12d3-a456-************',
          receiverUserId: '123e4567-e89b-12d3-a456-426614174002',
          amount: '50.00',
          currency: 'USD',
          description: 'Friendly transfer',
          transferFee: '0.00'
        }
      }
    }
  })
  transferFunds(@Body() transferFundsDto: TransferFundsDto): Promise<WalletTransferDto> {
    return this.walletService.transferFunds(transferFundsDto);
  }

  @Get('user/:userId/transactions')
  @UseGuards(JwtAuthGuard)
  @ApiOperation({
    summary: 'Get wallet transaction history',
    description: 'Retrieve transaction history for a user wallet with pagination.',
  })
  @ApiResponse({
    status: 200,
    description: 'Successfully retrieved transaction history.',
    type: [WalletTransactionDto],
  })
  @ApiResponse({
    status: 404,
    description: 'Wallet not found.',
    type: NotFoundResponseDto
  })
  @ApiParam({
    name: 'userId',
    description: 'User ID to get transactions for',
    format: 'uuid',
    example: '123e4567-e89b-12d3-a456-************'
  })
  @ApiQuery({
    name: 'limit',
    required: false,
    type: Number,
    description: 'Number of transactions to return',
    example: 50
  })
  @ApiQuery({
    name: 'offset',
    required: false,
    type: Number,
    description: 'Number of transactions to skip',
    example: 0
  })
  getTransactionHistory(
    @Param('userId') userId: string,
    @Query('limit') limit?: number,
    @Query('offset') offset?: number,
  ): Promise<WalletTransactionDto[]> {
    return this.walletService.getTransactionHistory(userId, limit, offset);
  }

  @Get('my-transactions')
  @UseGuards(JwtAuthGuard)
  @ApiOperation({
    summary: 'Get current user transaction history',
    description: 'Retrieve transaction history for the authenticated user wallet.',
  })
  @ApiResponse({
    status: 200,
    description: 'Successfully retrieved transaction history.',
    type: [WalletTransactionDto],
  })
  @ApiQuery({
    name: 'limit',
    required: false,
    type: Number,
    description: 'Number of transactions to return',
    example: 50
  })
  @ApiQuery({
    name: 'offset',
    required: false,
    type: Number,
    description: 'Number of transactions to skip',
    example: 0
  })
  getMyTransactionHistory(
    @CurrentUser() user: User,
    @Query('limit') limit?: number,
    @Query('offset') offset?: number,
  ): Promise<WalletTransactionDto[]> {
    return this.walletService.getTransactionHistory(user.id, limit, offset);
  }

  @Patch('user/:userId/status')
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles('admin', 'finance')
  @ApiOperation({
    summary: 'Update wallet status',
    description: 'Update the status of a user wallet (admin only).',
  })
  @ApiResponse({
    status: 200,
    description: 'Wallet status updated successfully.',
    type: WalletDto,
  })
  @ApiResponse({
    status: 404,
    description: 'Wallet not found.',
    type: NotFoundResponseDto
  })
  @ApiParam({
    name: 'userId',
    description: 'User ID whose wallet status to update',
    format: 'uuid',
    example: '123e4567-e89b-12d3-a456-************'
  })
  @ApiBody({
    type: UpdateWalletStatusDto,
    description: 'Status update data',
    examples: {
      'suspend-wallet': {
        summary: 'Suspend Wallet',
        value: {
          status: 'SUSPENDED',
          reason: 'Suspicious activity detected'
        }
      },
      'activate-wallet': {
        summary: 'Activate Wallet',
        value: {
          status: 'ACTIVE',
          reason: 'Account verification completed'
        }
      }
    }
  })
  updateWalletStatus(
    @Param('userId') userId: string,
    @Body() updateStatusDto: UpdateWalletStatusDto,
  ): Promise<WalletDto> {
    return this.walletService.updateWalletStatus(
      userId,
      updateStatusDto.status as any,
      updateStatusDto.reason
    );
  }

  @Patch('user/:userId/limits')
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles('admin', 'finance')
  @ApiOperation({
    summary: 'Update wallet limits',
    description: 'Update daily and monthly transaction limits for a user wallet.',
  })
  @ApiResponse({
    status: 200,
    description: 'Wallet limits updated successfully.',
    type: WalletDto,
  })
  @ApiResponse({
    status: 404,
    description: 'Wallet not found.',
    type: NotFoundResponseDto
  })
  @ApiParam({
    name: 'userId',
    description: 'User ID whose wallet limits to update',
    format: 'uuid',
    example: '123e4567-e89b-12d3-a456-************'
  })
  @ApiBody({
    type: UpdateWalletLimitsDto,
    description: 'Limits update data',
    examples: {
      'increase-limits': {
        summary: 'Increase Transaction Limits',
        value: {
          dailyLimit: '5000.00',
          monthlyLimit: '50000.00'
        }
      }
    }
  })
  updateWalletLimits(
    @Param('userId') userId: string,
    @Body() updateLimitsDto: UpdateWalletLimitsDto,
  ): Promise<WalletDto> {
    return this.walletService.updateWalletLimits(
      userId,
      updateLimitsDto.dailyLimit,
      updateLimitsDto.monthlyLimit
    );
  }

  @Get('summary')
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles('admin', 'finance')
  @ApiOperation({
    summary: 'Get wallet system summary',
    description: 'Retrieve summary statistics for the entire wallet system.',
  })
  @ApiResponse({
    status: 200,
    description: 'Successfully retrieved wallet summary.',
    type: WalletSummaryDto,
    example: {
      totalWallets: 1250,
      totalValue: '2500000.00',
      dailyTransactions: 450,
      dailyVolume: '125000.00',
      averageBalance: '2000.00',
      currencyBreakdown: {
        USD: '2000000.00',
        EUR: '300000.00',
        GBP: '200000.00'
      }
    }
  })
  getWalletSummary(): Promise<any> {
    return this.walletService.getWalletSummary();
  }
}
