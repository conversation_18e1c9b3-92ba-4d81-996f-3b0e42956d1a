import { Controller, Get, Query, UseGuards, Param } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth, ApiQuery, ApiParam } from '@nestjs/swagger';
import { FinancialReportsService } from '../services/financial-reports.service';
import { JwtAuthGuard } from '@core/auth/guards/jwt-auth.guard';
import { RolesGuard } from '@core/auth/guards/roles.guard';
import { Roles } from '@core/auth/decorators/roles.decorator';
import { CurrentUser } from '@core/auth/decorators/current-user.decorator';
import { User } from '@apps/user/entities/user.entity';
import { ErrorResponseDto, UnauthorizedResponseDto, ForbiddenResponseDto } from '@shared/dto/paginated-response.dto';

@ApiTags('financial-reports')
@ApiBearerAuth('JWT-auth')
@Controller('finance/reports')
export class FinancialReportsController {
  constructor(private readonly financialReportsService: FinancialReportsService) {}

  @Get('dashboard')
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles('admin', 'finance')
  @ApiOperation({
    summary: 'Get financial dashboard',
    description: 'Retrieve comprehensive financial dashboard with key metrics and charts.',
  })
  @ApiResponse({
    status: 200,
    description: 'Successfully retrieved financial dashboard.',
    schema: {
      type: 'object',
      properties: {
        totalRevenue: { type: 'string', example: '2500000.00' },
        totalTransactions: { type: 'number', example: 15420 },
        activeWallets: { type: 'number', example: 1250 },
        activeLoans: { type: 'number', example: 450 },
        activeInstallmentPlans: { type: 'number', example: 150 },
        totalWalletBalance: { type: 'string', example: '850000.00' },
        totalLoanBalance: { type: 'string', example: '5250000.00' },
        totalInstallmentBalance: { type: 'string', example: '125000.00' },
        monthlyGrowth: { type: 'number', example: 12.5 },
        defaultRate: { type: 'number', example: 2.3 },
        collectionEfficiency: { type: 'number', example: 94.8 },
        revenueByMonth: { type: 'array', items: { type: 'object' } },
        transactionsByType: { type: 'object' },
        riskDistribution: { type: 'object' }
      }
    }
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - Invalid or missing authentication token.',
    type: UnauthorizedResponseDto
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Requires admin or finance role.',
    type: ForbiddenResponseDto
  })
  @ApiQuery({
    name: 'period',
    required: false,
    type: String,
    description: 'Time period for dashboard data',
    enum: ['7d', '30d', '90d', '1y'],
    example: '30d'
  })
  getFinancialDashboard(@Query('period') period = '30d'): Promise<any> {
    return this.financialReportsService.getFinancialDashboard(period);
  }

  @Get('transactions')
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles('admin', 'finance')
  @ApiOperation({
    summary: 'Get transaction report',
    description: 'Generate detailed transaction report with filtering and pagination.',
  })
  @ApiResponse({
    status: 200,
    description: 'Successfully retrieved transaction report.',
    schema: {
      type: 'object',
      properties: {
        transactions: { type: 'array', items: { type: 'object' } },
        summary: {
          type: 'object',
          properties: {
            totalTransactions: { type: 'number', example: 1250 },
            totalVolume: { type: 'string', example: '125000.00' },
            averageTransactionSize: { type: 'string', example: '100.00' },
            transactionsByType: { type: 'object' },
            transactionsByStatus: { type: 'object' }
          }
        },
        pagination: {
          type: 'object',
          properties: {
            page: { type: 'number', example: 1 },
            limit: { type: 'number', example: 50 },
            total: { type: 'number', example: 1250 },
            totalPages: { type: 'number', example: 25 }
          }
        }
      }
    }
  })
  @ApiQuery({
    name: 'startDate',
    required: false,
    type: String,
    description: 'Start date for report (ISO format)',
    example: '2023-01-01T00:00:00Z'
  })
  @ApiQuery({
    name: 'endDate',
    required: false,
    type: String,
    description: 'End date for report (ISO format)',
    example: '2023-12-31T23:59:59Z'
  })
  @ApiQuery({
    name: 'type',
    required: false,
    type: String,
    description: 'Filter by transaction type',
    example: 'DEPOSIT'
  })
  @ApiQuery({
    name: 'status',
    required: false,
    type: String,
    description: 'Filter by transaction status',
    example: 'COMPLETED'
  })
  @ApiQuery({
    name: 'userId',
    required: false,
    type: String,
    description: 'Filter by user ID',
    example: '123e4567-e89b-12d3-a456-************'
  })
  @ApiQuery({
    name: 'page',
    required: false,
    type: Number,
    description: 'Page number',
    example: 1
  })
  @ApiQuery({
    name: 'limit',
    required: false,
    type: Number,
    description: 'Items per page',
    example: 50
  })
  getTransactionReport(
    @Query('startDate') startDate?: string,
    @Query('endDate') endDate?: string,
    @Query('type') type?: string,
    @Query('status') status?: string,
    @Query('userId') userId?: string,
    @Query('page') page = 1,
    @Query('limit') limit = 50,
  ): Promise<any> {
    return this.financialReportsService.getTransactionReport({
      startDate: startDate ? new Date(startDate) : undefined,
      endDate: endDate ? new Date(endDate) : undefined,
      type,
      status,
      userId,
      page,
      limit,
    });
  }

  @Get('credit-risk')
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles('admin', 'finance')
  @ApiOperation({
    summary: 'Get credit risk report',
    description: 'Generate comprehensive credit risk analysis report.',
  })
  @ApiResponse({
    status: 200,
    description: 'Successfully retrieved credit risk report.',
    schema: {
      type: 'object',
      properties: {
        overview: {
          type: 'object',
          properties: {
            totalCreditProfiles: { type: 'number', example: 1250 },
            totalCreditLimit: { type: 'string', example: '25000000.00' },
            totalUsedCredit: { type: 'string', example: '8500000.00' },
            utilizationRate: { type: 'number', example: 34.0 },
            averageCreditScore: { type: 'number', example: 685 },
            defaultRate: { type: 'number', example: 2.67 }
          }
        },
        riskDistribution: {
          type: 'object',
          properties: {
            LOW: { type: 'number', example: 450 },
            MEDIUM: { type: 'number', example: 650 },
            HIGH: { type: 'number', example: 150 }
          }
        },
        creditGradeDistribution: { type: 'object' },
        loanPerformance: { type: 'object' },
        riskTrends: { type: 'array', items: { type: 'object' } }
      }
    }
  })
  getCreditRiskReport(): Promise<any> {
    return this.financialReportsService.getCreditRiskReport();
  }

  @Get('installment-performance')
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles('admin', 'finance')
  @ApiOperation({
    summary: 'Get installment performance report',
    description: 'Generate detailed installment payment performance analysis.',
  })
  @ApiResponse({
    status: 200,
    description: 'Successfully retrieved installment performance report.',
    schema: {
      type: 'object',
      properties: {
        overview: {
          type: 'object',
          properties: {
            totalPlans: { type: 'number', example: 230 },
            activePlans: { type: 'number', example: 150 },
            completedPlans: { type: 'number', example: 75 },
            defaultedPlans: { type: 'number', example: 5 },
            totalOutstanding: { type: 'string', example: '125000.00' },
            collectionEfficiency: { type: 'number', example: 94.5 }
          }
        },
        paymentPerformance: {
          type: 'object',
          properties: {
            onTimePayments: { type: 'number', example: 850 },
            latePayments: { type: 'number', example: 45 },
            missedPayments: { type: 'number', example: 12 },
            totalLateFees: { type: 'string', example: '2500.00' }
          }
        },
        planDistribution: { type: 'object' },
        performanceTrends: { type: 'array', items: { type: 'object' } }
      }
    }
  })
  getInstallmentPerformanceReport(): Promise<any> {
    return this.financialReportsService.getInstallmentPerformanceReport();
  }

  @Get('user/:userId/statement')
  @UseGuards(JwtAuthGuard)
  @ApiOperation({
    summary: 'Get user financial statement',
    description: 'Generate comprehensive financial statement for a specific user.',
  })
  @ApiResponse({
    status: 200,
    description: 'Successfully retrieved user financial statement.',
    schema: {
      type: 'object',
      properties: {
        user: { type: 'object' },
        wallet: { type: 'object' },
        creditProfile: { type: 'object' },
        loans: { type: 'array', items: { type: 'object' } },
        installmentPlans: { type: 'array', items: { type: 'object' } },
        transactions: { type: 'array', items: { type: 'object' } },
        summary: {
          type: 'object',
          properties: {
            totalWalletBalance: { type: 'string', example: '1250.75' },
            totalCreditUsed: { type: 'string', example: '2500.00' },
            totalLoanBalance: { type: 'string', example: '3750.00' },
            totalInstallmentBalance: { type: 'string', example: '800.00' },
            monthlyPaymentObligations: { type: 'string', example: '450.00' }
          }
        }
      }
    }
  })
  @ApiParam({
    name: 'userId',
    description: 'User ID to generate statement for',
    format: 'uuid',
    example: '123e4567-e89b-12d3-a456-************'
  })
  @ApiQuery({
    name: 'startDate',
    required: false,
    type: String,
    description: 'Start date for statement period',
    example: '2023-01-01T00:00:00Z'
  })
  @ApiQuery({
    name: 'endDate',
    required: false,
    type: String,
    description: 'End date for statement period',
    example: '2023-12-31T23:59:59Z'
  })
  getUserFinancialStatement(
    @Param('userId') userId: string,
    @Query('startDate') startDate?: string,
    @Query('endDate') endDate?: string,
  ): Promise<any> {
    return this.financialReportsService.getUserFinancialStatement(userId, {
      startDate: startDate ? new Date(startDate) : undefined,
      endDate: endDate ? new Date(endDate) : undefined,
    });
  }

  @Get('my-statement')
  @UseGuards(JwtAuthGuard)
  @ApiOperation({
    summary: 'Get current user financial statement',
    description: 'Generate financial statement for the authenticated user.',
  })
  @ApiResponse({
    status: 200,
    description: 'Successfully retrieved user financial statement.',
    schema: {
      type: 'object',
      properties: {
        user: { type: 'object' },
        wallet: { type: 'object' },
        creditProfile: { type: 'object' },
        loans: { type: 'array', items: { type: 'object' } },
        installmentPlans: { type: 'array', items: { type: 'object' } },
        transactions: { type: 'array', items: { type: 'object' } },
        summary: { type: 'object' }
      }
    }
  })
  @ApiQuery({
    name: 'startDate',
    required: false,
    type: String,
    description: 'Start date for statement period',
    example: '2023-01-01T00:00:00Z'
  })
  @ApiQuery({
    name: 'endDate',
    required: false,
    type: String,
    description: 'End date for statement period',
    example: '2023-12-31T23:59:59Z'
  })
  getMyFinancialStatement(
    @CurrentUser() user: User,
    @Query('startDate') startDate?: string,
    @Query('endDate') endDate?: string,
  ): Promise<any> {
    return this.financialReportsService.getUserFinancialStatement(user.id, {
      startDate: startDate ? new Date(startDate) : undefined,
      endDate: endDate ? new Date(endDate) : undefined,
    });
  }

  @Get('revenue-analysis')
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles('admin', 'finance')
  @ApiOperation({
    summary: 'Get revenue analysis report',
    description: 'Generate detailed revenue analysis with trends and projections.',
  })
  @ApiResponse({
    status: 200,
    description: 'Successfully retrieved revenue analysis.',
    schema: {
      type: 'object',
      properties: {
        totalRevenue: { type: 'string', example: '2500000.00' },
        revenueBySource: {
          type: 'object',
          properties: {
            platformFees: { type: 'string', example: '125000.00' },
            interestIncome: { type: 'string', example: '85000.00' },
            transactionFees: { type: 'string', example: '45000.00' },
            lateFees: { type: 'string', example: '12000.00' },
            processingFees: { type: 'string', example: '8000.00' }
          }
        },
        monthlyTrends: { type: 'array', items: { type: 'object' } },
        projections: { type: 'object' },
        growthMetrics: { type: 'object' }
      }
    }
  })
  @ApiQuery({
    name: 'period',
    required: false,
    type: String,
    description: 'Analysis period',
    enum: ['30d', '90d', '1y'],
    example: '90d'
  })
  getRevenueAnalysis(@Query('period') period = '90d'): Promise<any> {
    return this.financialReportsService.getRevenueAnalysis(period);
  }
}
