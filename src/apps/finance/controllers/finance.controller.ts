import { Controller, Get, Post, Body, Patch, Param, Delete, UseGuards, Query, UseInterceptors } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth, ApiParam, ApiBody, ApiQuery } from '@nestjs/swagger';
import { FinanceService } from '../services/finance.service';
import { CreateTransactionDto } from '../dto/create-transaction.dto';
import { UpdateTransactionDto } from '../dto/update-transaction.dto';
import { CreateInvoiceDto } from '../dto/create-invoice.dto';
import { InvoiceDto } from '../dto/invoice.dto';
import { JwtAuthGuard } from '@core/auth/guards/jwt-auth.guard';
import { RolesGuard } from '@core/auth/guards/roles.guard';
import { Roles } from '@core/auth/decorators/roles.decorator';
import { RequirePermissions, Resources, ResourceActions, createPermission } from '@shared/decorators/permissions.decorator';
import { PermissionsGuard } from '@shared/guards/permissions.guard';
import { CacheInterceptor } from '@nestjs/cache-manager';
import { StandardListParams } from '@shared/utils/query-params.util';
import { TransactionStatus } from '../entities/transaction.entity';

@ApiTags('finance')
@ApiBearerAuth()
@Controller('finance')
export class FinanceController {
  constructor(private readonly financeService: FinanceService) {}

  // Transactions
  @Post('transactions')
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles('admin', 'finance')
  @ApiOperation({ summary: 'Create a new transaction' })
  @ApiResponse({ status: 201, description: 'Transaction successfully created.' })
  @ApiResponse({ status: 400, description: 'Bad Request.' })
  @ApiResponse({ status: 401, description: 'Unauthorized.' })
  @ApiResponse({ status: 403, description: 'Forbidden. Requires admin or finance role.' })
  @ApiBody({ type: CreateTransactionDto })
  createTransaction(@Body() createTransactionDto: CreateTransactionDto) {
    return this.financeService.createTransaction(createTransactionDto);
  }

  @Get('transactions')
  @UseGuards(JwtAuthGuard, PermissionsGuard)
  @RequirePermissions(createPermission(Resources.INVOICES, ResourceActions.LIST))
  @UseInterceptors(CacheInterceptor)
  @ApiOperation({ summary: 'Get all transactions' })
  @ApiResponse({ status: 200, description: 'Return all transactions.' })
  @ApiResponse({ status: 401, description: 'Unauthorized.' })
  @ApiResponse({ status: 403, description: 'Forbidden. Insufficient permissions.' })
  findAllTransactions() {
    return this.financeService.findAllTransactions();
  }

  @Get('transactions/:id')
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles('admin', 'finance')
  @ApiOperation({ summary: 'Get a transaction by ID' })
  @ApiParam({ name: 'id', description: 'Transaction ID' })
  @ApiResponse({ status: 200, description: 'Return the transaction.' })
  @ApiResponse({ status: 401, description: 'Unauthorized.' })
  @ApiResponse({ status: 403, description: 'Forbidden. Requires admin or finance role.' })
  @ApiResponse({ status: 404, description: 'Transaction not found.' })
  findOneTransaction(@Param('id') id: string) {
    return this.financeService.findOneTransaction(id);
  }

  @Patch('transactions/:id')
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles('admin', 'finance')
  @ApiOperation({ summary: 'Update a transaction' })
  @ApiParam({ name: 'id', description: 'Transaction ID' })
  @ApiBody({ type: UpdateTransactionDto })
  @ApiResponse({ status: 200, description: 'Transaction successfully updated.' })
  @ApiResponse({ status: 400, description: 'Bad Request.' })
  @ApiResponse({ status: 401, description: 'Unauthorized.' })
  @ApiResponse({ status: 403, description: 'Forbidden. Requires admin or finance role.' })
  @ApiResponse({ status: 404, description: 'Transaction not found.' })
  updateTransaction(
    @Param('id') id: string,
    @Body() updateTransactionDto: UpdateTransactionDto,
  ) {
    return this.financeService.updateTransaction(id, updateTransactionDto);
  }

  @Patch('transactions/:id/status')
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles('admin', 'finance')
  @ApiOperation({ summary: 'Update transaction status' })
  @ApiParam({ name: 'id', description: 'Transaction ID' })
  @ApiBody({ schema: {
    type: 'object',
    properties: { 
      status: { 
        type: 'string', 
        enum: Object.values(TransactionStatus),
        example: TransactionStatus.COMPLETED 
      } 
    }
  }})
  @ApiResponse({ status: 200, description: 'Transaction status successfully updated.' })
  @ApiResponse({ status: 400, description: 'Bad Request.' })
  @ApiResponse({ status: 401, description: 'Unauthorized.' })
  @ApiResponse({ status: 403, description: 'Forbidden. Requires admin or finance role.' })
  @ApiResponse({ status: 404, description: 'Transaction not found.' })
  updateTransactionStatus(
    @Param('id') id: string,
    @Body('status') status: TransactionStatus,
  ) {
    return this.financeService.updateTransactionStatus(id, status);
  }

  // Invoices
  @Post('invoices')
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles('admin', 'finance')
  @ApiOperation({ summary: 'Create a new invoice' })
  @ApiResponse({ status: 201, description: 'Invoice successfully created.', type: InvoiceDto })
  @ApiResponse({ status: 400, description: 'Bad Request.' })
  @ApiResponse({ status: 401, description: 'Unauthorized.' })
  @ApiResponse({ status: 403, description: 'Forbidden. Requires admin or finance role.' })
  @ApiBody({ type: CreateInvoiceDto })
  createInvoice(@Body() createInvoiceDto: CreateInvoiceDto): Promise<InvoiceDto> {
    return this.financeService.createInvoice(createInvoiceDto);
  }

  @Get('invoices')
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles('admin', 'finance')
  @ApiOperation({ summary: 'Get all invoices' })
  @ApiResponse({ status: 200, description: 'Return all invoices.', type: [InvoiceDto] })
  @ApiResponse({ status: 401, description: 'Unauthorized.' })
  @ApiResponse({ status: 403, description: 'Forbidden. Requires admin or finance role.' })
  @ApiQuery({ name: 'page', required: false, type: Number, description: 'Page number' })
  @ApiQuery({ name: 'limit', required: false, type: Number, description: 'Items per page (max 100)' })
  @ApiQuery({ name: 'where', required: false, type: String, description: 'JSON filter conditions' })
  findAllInvoices(
    @Query('page') page?: number,
    @Query('limit') limit?: number,
    @Query('where') where?: string,
    @Query('search') search?: string,
    @Query('order') order?: string
  ): Promise<{ data: InvoiceDto[]; total: number; page: number; limit: number }> {
    // Parse query params into StandardListParams
    const params: StandardListParams = {
      page: page ? Number(page) : undefined,
      limit: limit ? Number(limit) : undefined,
      where: where ? JSON.parse(where) : undefined,
      search,
      order: order ? JSON.parse(order) : undefined
    };
    
    return this.financeService.findAllInvoices(params);
  }

  @Get('invoices/:id')
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles('admin', 'finance')
  @ApiOperation({ summary: 'Get an invoice by ID' })
  @ApiParam({ name: 'id', description: 'Invoice ID' })
  @ApiResponse({ status: 200, description: 'Return the invoice.', type: InvoiceDto })
  @ApiResponse({ status: 401, description: 'Unauthorized.' })
  @ApiResponse({ status: 403, description: 'Forbidden. Requires admin or finance role.' })
  @ApiResponse({ status: 404, description: 'Invoice not found.' })
  findOneInvoice(@Param('id') id: string): Promise<InvoiceDto> {
    return this.financeService.findOneInvoice(id);
  }
  
  @Patch('invoices/:id/mark-paid')
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles('admin', 'finance')
  @ApiOperation({ summary: 'Mark an invoice as paid' })
  @ApiParam({ name: 'id', description: 'Invoice ID' })
  @ApiBody({ schema: { 
    type: 'object', 
    properties: { 
      paymentDate: { type: 'string', format: 'date-time', example: new Date().toISOString() },
      paymentReference: { type: 'string', example: 'TXN12345' }
    } 
  }})
  @ApiResponse({ status: 200, description: 'Invoice successfully marked as paid.' })
  @ApiResponse({ status: 400, description: 'Bad Request.' })
  @ApiResponse({ status: 401, description: 'Unauthorized.' })
  @ApiResponse({ status: 403, description: 'Forbidden. Requires admin or finance role.' })
  @ApiResponse({ status: 404, description: 'Invoice not found.' })
  markInvoiceAsPaid(
    @Param('id') id: string,
    @Body('paymentDate') paymentDate: Date,
    @Body('paymentReference') paymentReference: string,
  ) {
    return this.financeService.markInvoiceAsPaid(id, paymentDate, paymentReference);
  }

  // Reports
  @Get('reports/revenue')
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles('admin', 'finance')
  @ApiOperation({ summary: 'Get revenue report' })
  @ApiResponse({ status: 200, description: 'Return revenue report.' })
  @ApiResponse({ status: 401, description: 'Unauthorized.' })
  @ApiResponse({ status: 403, description: 'Forbidden. Requires admin or finance role.' })
  getRevenueReport(
    @Query('startDate') startDate: string,
    @Query('endDate') endDate: string,
  ) {
    return this.financeService.getRevenueReport(new Date(startDate), new Date(endDate));
  }
}
