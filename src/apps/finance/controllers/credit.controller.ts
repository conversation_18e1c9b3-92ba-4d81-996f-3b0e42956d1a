import { Controller, Get, Post, Body, Patch, Param, Query, UseGuards } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth, ApiParam, ApiBody, ApiQuery } from '@nestjs/swagger';
import { CreditService } from '../services/credit.service';
import { 
  CreateCreditProfileDto, 
  CreateLoanApplicationDto, 
  ProcessLoanApplicationDto, 
  ProcessLoanPaymentDto,
  UpdateCreditLimitDto,
  CreditScoreUpdateDto,
  LoanRefinanceDto
} from '../dto/credit.dto';
import { 
  CreditProfileDto, 
  LoanApplicationDto, 
  LoanDto, 
  LoanPaymentDto,
  CreditSummaryDto,
  LoanStatus
} from '../dto/credit-response.dto';
import { JwtAuthGuard } from '@core/auth/guards/jwt-auth.guard';
import { RolesGuard } from '@core/auth/guards/roles.guard';
import { Roles } from '@core/auth/decorators/roles.decorator';
import { CurrentUser } from '@core/auth/decorators/current-user.decorator';
import { User } from '@apps/user/entities/user.entity';
import { ErrorResponseDto, UnauthorizedResponseDto, ForbiddenResponseDto, NotFoundResponseDto } from '@shared/dto/paginated-response.dto';

@ApiTags('credit')
@ApiBearerAuth('JWT-auth')
@Controller('finance/credit')
export class CreditController {
  constructor(private readonly creditService: CreditService) {}

  @Post('profile')
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles('admin', 'finance')
  @ApiOperation({
    summary: 'Create credit profile',
    description: 'Creates a new credit profile for a user with initial credit assessment.',
  })
  @ApiResponse({
    status: 201,
    description: 'Credit profile created successfully.',
    type: CreditProfileDto,
    example: {
      id: '123e4567-e89b-12d3-a456-************',
      userId: '123e4567-e89b-12d3-a456-************',
      creditScore: 720,
      creditGrade: 'GOOD',
      totalCreditLimit: '10000.00',
      availableCredit: '10000.00',
      usedCredit: '0.00',
      riskLevel: 'MEDIUM',
      monthlyIncome: '5000.00',
      incomeVerified: true,
      employmentStatus: 'EMPLOYED'
    }
  })
  @ApiResponse({
    status: 400,
    description: 'Bad Request - User already has credit profile or invalid data.',
    type: ErrorResponseDto
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - Invalid or missing authentication token.',
    type: UnauthorizedResponseDto
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Requires admin or finance role.',
    type: ForbiddenResponseDto
  })
  @ApiBody({
    type: CreateCreditProfileDto,
    description: 'Credit profile data',
    examples: {
      'employed-user': {
        summary: 'Employed User Profile',
        description: 'Credit profile for employed user with verified income',
        value: {
          userId: '123e4567-e89b-12d3-a456-************',
          monthlyIncome: '5000.00',
          incomeVerified: true,
          employmentStatus: 'EMPLOYED'
        }
      },
      'self-employed': {
        summary: 'Self-Employed User',
        description: 'Credit profile for self-employed user',
        value: {
          userId: '123e4567-e89b-12d3-a456-************',
          monthlyIncome: '3500.00',
          incomeVerified: false,
          employmentStatus: 'SELF_EMPLOYED',
          riskFactors: ['UNVERIFIED_INCOME']
        }
      }
    }
  })
  createCreditProfile(@Body() createCreditProfileDto: CreateCreditProfileDto): Promise<CreditProfileDto> {
    return this.creditService.createCreditProfile(createCreditProfileDto);
  }

  @Get('profile/user/:userId')
  @UseGuards(JwtAuthGuard)
  @ApiOperation({
    summary: 'Get user credit profile',
    description: 'Retrieve credit profile information for a specific user.',
  })
  @ApiResponse({
    status: 200,
    description: 'Successfully retrieved credit profile.',
    type: CreditProfileDto,
  })
  @ApiResponse({
    status: 404,
    description: 'Credit profile not found for this user.',
    type: NotFoundResponseDto
  })
  @ApiParam({
    name: 'userId',
    description: 'User ID to get credit profile for',
    format: 'uuid',
    example: '123e4567-e89b-12d3-a456-************'
  })
  getCreditProfile(@Param('userId') userId: string): Promise<CreditProfileDto> {
    return this.creditService.getCreditProfile(userId);
  }

  @Get('profile/my-profile')
  @UseGuards(JwtAuthGuard)
  @ApiOperation({
    summary: 'Get current user credit profile',
    description: 'Retrieve credit profile information for the authenticated user.',
  })
  @ApiResponse({
    status: 200,
    description: 'Successfully retrieved credit profile.',
    type: CreditProfileDto,
  })
  @ApiResponse({
    status: 404,
    description: 'Credit profile not found for current user.',
    type: NotFoundResponseDto
  })
  getMyCreditProfile(@CurrentUser() user: User): Promise<CreditProfileDto> {
    return this.creditService.getCreditProfile(user.id);
  }

  @Post('apply')
  @UseGuards(JwtAuthGuard)
  @ApiOperation({
    summary: 'Apply for loan',
    description: 'Submit a new loan application for review.',
  })
  @ApiResponse({
    status: 201,
    description: 'Loan application created successfully.',
    type: LoanApplicationDto,
  })
  @ApiResponse({
    status: 400,
    description: 'Bad Request - No credit profile, insufficient credit, or pending applications.',
    type: ErrorResponseDto
  })
  @ApiResponse({
    status: 404,
    description: 'User credit profile not found.',
    type: NotFoundResponseDto
  })
  @ApiBody({
    type: CreateLoanApplicationDto,
    description: 'Loan application data',
    examples: {
      'personal-loan': {
        summary: 'Personal Loan Application',
        description: 'Application for personal loan',
        value: {
          userId: '123e4567-e89b-12d3-a456-************',
          loanType: 'PERSONAL',
          requestedAmount: '5000.00',
          currency: 'USD',
          purpose: 'Home improvement and personal expenses',
          termMonths: 24,
          documents: ['https://example.com/income_statement.pdf']
        }
      },
      'business-loan': {
        summary: 'Business Loan Application',
        description: 'Application for business equipment loan',
        value: {
          userId: '123e4567-e89b-12d3-a456-************',
          loanType: 'EQUIPMENT',
          requestedAmount: '15000.00',
          currency: 'USD',
          purpose: 'Purchase of industrial sewing machines for tailoring business',
          termMonths: 36,
          documents: [
            'https://example.com/business_plan.pdf',
            'https://example.com/equipment_quote.pdf'
          ]
        }
      }
    }
  })
  applyForLoan(@Body() createLoanApplicationDto: CreateLoanApplicationDto): Promise<LoanApplicationDto> {
    return this.creditService.createLoanApplication(createLoanApplicationDto);
  }

  @Post('applications/:id/submit')
  @UseGuards(JwtAuthGuard)
  @ApiOperation({
    summary: 'Submit loan application',
    description: 'Submit a draft loan application for review and risk assessment.',
  })
  @ApiResponse({
    status: 200,
    description: 'Loan application submitted successfully.',
    type: LoanApplicationDto,
  })
  @ApiResponse({
    status: 400,
    description: 'Bad Request - Application not in draft status.',
    type: ErrorResponseDto
  })
  @ApiResponse({
    status: 404,
    description: 'Loan application not found.',
    type: NotFoundResponseDto
  })
  @ApiParam({
    name: 'id',
    description: 'Loan application ID',
    format: 'uuid',
    example: '123e4567-e89b-12d3-a456-************'
  })
  submitLoanApplication(@Param('id') id: string): Promise<LoanApplicationDto> {
    return this.creditService.submitLoanApplication(id);
  }

  @Post('applications/process')
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles('admin', 'finance')
  @ApiOperation({
    summary: 'Process loan application',
    description: 'Review and approve or reject a loan application.',
  })
  @ApiResponse({
    status: 200,
    description: 'Loan application processed successfully.',
    type: LoanApplicationDto,
  })
  @ApiResponse({
    status: 400,
    description: 'Bad Request - Application not in reviewable status.',
    type: ErrorResponseDto
  })
  @ApiResponse({
    status: 404,
    description: 'Loan application not found.',
    type: NotFoundResponseDto
  })
  @ApiBody({
    type: ProcessLoanApplicationDto,
    description: 'Application processing data',
    examples: {
      'approve-application': {
        summary: 'Approve Application',
        description: 'Approve loan application with terms',
        value: {
          applicationId: '123e4567-e89b-12d3-a456-************',
          decision: 'APPROVED',
          approvedAmount: '4500.00',
          interestRate: '12.50',
          reviewedBy: '123e4567-e89b-12d3-a456-426614174002',
          notes: 'Approved with standard terms based on credit score'
        }
      },
      'reject-application': {
        summary: 'Reject Application',
        description: 'Reject loan application',
        value: {
          applicationId: '123e4567-e89b-12d3-a456-************',
          decision: 'REJECTED',
          rejectionReason: 'Insufficient credit history and high debt-to-income ratio',
          reviewedBy: '123e4567-e89b-12d3-a456-426614174002',
          notes: 'Recommend reapplying after 6 months with improved credit'
        }
      }
    }
  })
  processLoanApplication(@Body() processLoanApplicationDto: ProcessLoanApplicationDto): Promise<LoanApplicationDto> {
    return this.creditService.processLoanApplication(processLoanApplicationDto);
  }

  @Get('applications')
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles('admin', 'finance')
  @ApiOperation({
    summary: 'Get loan applications',
    description: 'Retrieve loan applications with optional filtering by user and status.',
  })
  @ApiResponse({
    status: 200,
    description: 'Successfully retrieved loan applications.',
    type: [LoanApplicationDto],
  })
  @ApiQuery({
    name: 'userId',
    required: false,
    type: String,
    description: 'Filter by user ID',
    example: '123e4567-e89b-12d3-a456-************'
  })
  @ApiQuery({
    name: 'status',
    required: false,
    enum: LoanStatus,
    description: 'Filter by application status',
    example: LoanStatus.UNDER_REVIEW
  })
  getLoanApplications(
    @Query('userId') userId?: string,
    @Query('status') status?: LoanStatus,
  ): Promise<LoanApplicationDto[]> {
    return this.creditService.getLoanApplications(userId, status);
  }

  @Get('applications/my-applications')
  @UseGuards(JwtAuthGuard)
  @ApiOperation({
    summary: 'Get current user loan applications',
    description: 'Retrieve loan applications for the authenticated user.',
  })
  @ApiResponse({
    status: 200,
    description: 'Successfully retrieved loan applications.',
    type: [LoanApplicationDto],
  })
  getMyLoanApplications(@CurrentUser() user: User): Promise<LoanApplicationDto[]> {
    return this.creditService.getLoanApplications(user.id);
  }

  @Get('loans/user/:userId')
  @UseGuards(JwtAuthGuard)
  @ApiOperation({
    summary: 'Get user loans',
    description: 'Retrieve all loans for a specific user.',
  })
  @ApiResponse({
    status: 200,
    description: 'Successfully retrieved user loans.',
    type: [LoanDto],
  })
  @ApiParam({
    name: 'userId',
    description: 'User ID to get loans for',
    format: 'uuid',
    example: '123e4567-e89b-12d3-a456-************'
  })
  getUserLoans(@Param('userId') userId: string): Promise<LoanDto[]> {
    return this.creditService.getUserLoans(userId);
  }

  @Get('loans/my-loans')
  @UseGuards(JwtAuthGuard)
  @ApiOperation({
    summary: 'Get current user loans',
    description: 'Retrieve all loans for the authenticated user.',
  })
  @ApiResponse({
    status: 200,
    description: 'Successfully retrieved user loans.',
    type: [LoanDto],
  })
  getMyLoans(@CurrentUser() user: User): Promise<LoanDto[]> {
    return this.creditService.getUserLoans(user.id);
  }

  @Post('loans/:id/payment')
  @UseGuards(JwtAuthGuard)
  @ApiOperation({
    summary: 'Make loan payment',
    description: 'Process a payment for an active loan.',
  })
  @ApiResponse({
    status: 201,
    description: 'Loan payment processed successfully.',
    type: LoanPaymentDto,
  })
  @ApiResponse({
    status: 400,
    description: 'Bad Request - Loan not active or invalid payment amount.',
    type: ErrorResponseDto
  })
  @ApiResponse({
    status: 404,
    description: 'Loan not found.',
    type: NotFoundResponseDto
  })
  @ApiParam({
    name: 'id',
    description: 'Loan ID',
    format: 'uuid',
    example: '123e4567-e89b-12d3-a456-************'
  })
  @ApiBody({
    type: ProcessLoanPaymentDto,
    description: 'Loan payment data',
    examples: {
      'regular-payment': {
        summary: 'Regular Monthly Payment',
        description: 'Standard monthly loan payment',
        value: {
          loanId: '123e4567-e89b-12d3-a456-************',
          amount: '230.50',
          paymentMethod: 'WALLET',
          notes: 'Regular monthly payment'
        }
      },
      'extra-payment': {
        summary: 'Extra Payment',
        description: 'Additional payment towards principal',
        value: {
          loanId: '123e4567-e89b-12d3-a456-************',
          amount: '500.00',
          paymentMethod: 'BANK_TRANSFER',
          transactionReference: 'TXN_123456789',
          notes: 'Extra payment to reduce principal balance'
        }
      }
    }
  })
  makeLoanPayment(@Body() processLoanPaymentDto: ProcessLoanPaymentDto): Promise<LoanPaymentDto> {
    return this.creditService.processLoanPayment(
      processLoanPaymentDto.loanId,
      processLoanPaymentDto.amount,
      processLoanPaymentDto.paymentMethod
    );
  }
}
