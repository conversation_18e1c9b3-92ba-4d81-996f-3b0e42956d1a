import { Controller, Get, Post, Body, Patch, Param, Query, UseGuards } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth, ApiParam, ApiBody, ApiQuery } from '@nestjs/swagger';
import { EscrowService } from '../services/escrow.service';
import { CreateEscrowDto, ReleaseEscrowDto, RefundEscrowDto, DisputeEscrowDto } from '../dto/create-escrow.dto';
import { EscrowDto, EscrowStatus, EscrowEventDto, EscrowSummaryDto } from '../dto/escrow.dto';
import { JwtAuthGuard } from '@core/auth/guards/jwt-auth.guard';
import { RolesGuard } from '@core/auth/guards/roles.guard';
import { Roles } from '@core/auth/decorators/roles.decorator';
import { CurrentUser } from '@core/auth/decorators/current-user.decorator';
import { User } from '@apps/user/entities/user.entity';
import { ErrorResponseDto, UnauthorizedResponseDto, ForbiddenResponseDto, NotFoundResponseDto } from '@shared/dto/paginated-response.dto';

@ApiTags('escrow')
@ApiBearerAuth('JWT-auth')
@Controller('finance/escrow')
export class EscrowController {
  constructor(private readonly escrowService: EscrowService) {}

  @Post()
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles('admin', 'finance')
  @ApiOperation({
    summary: 'Create escrow transaction',
    description: 'Creates a new escrow transaction to hold customer payment until order completion.',
  })
  @ApiResponse({
    status: 201,
    description: 'Escrow transaction created successfully.',
    type: EscrowDto,
    example: {
      id: '123e4567-e89b-12d3-a456-************',
      orderId: '123e4567-e89b-12d3-a456-************',
      customerId: '123e4567-e89b-12d3-a456-************',
      tailorId: '123e4567-e89b-12d3-a456-426614174003',
      amount: '150.00',
      platformFee: '7.50',
      tailorAmount: '142.50',
      status: 'HELD',
      paymentMethod: 'credit_card',
      heldAt: '2023-01-15T08:30:00Z',
      expiresAt: '2023-02-15T08:30:00Z',
      createdAt: '2023-01-15T08:30:00Z',
      updatedAt: '2023-01-15T08:30:00Z'
    }
  })
  @ApiResponse({
    status: 400,
    description: 'Bad Request - Invalid input data or order already has escrow.',
    type: ErrorResponseDto
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - Invalid or missing authentication token.',
    type: UnauthorizedResponseDto
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Requires admin or finance role.',
    type: ForbiddenResponseDto
  })
  @ApiResponse({
    status: 404,
    description: 'Order not found.',
    type: NotFoundResponseDto
  })
  @ApiBody({
    type: CreateEscrowDto,
    description: 'Escrow transaction data',
    examples: {
      'standard-escrow': {
        summary: 'Standard Escrow Transaction',
        description: 'A typical escrow transaction for an order',
        value: {
          orderId: '123e4567-e89b-12d3-a456-************',
          customerId: '123e4567-e89b-12d3-a456-************',
          amount: '150.00',
          platformFeeRate: '0.05',
          paymentMethod: 'credit_card',
          paymentReference: 'PAY_REF_123456789',
          gatewayTransactionId: 'pi_1234567890abcdef',
          expiryDays: 30
        }
      },
      'custom-conditions': {
        summary: 'Escrow with Custom Conditions',
        description: 'Escrow with specific release conditions',
        value: {
          orderId: '123e4567-e89b-12d3-a456-************',
          customerId: '123e4567-e89b-12d3-a456-************',
          amount: '500.00',
          platformFeeRate: '0.03',
          holdReason: 'High-value custom order requiring quality verification',
          releaseConditions: ['ORDER_COMPLETED', 'CUSTOMER_CONFIRMATION', 'QUALITY_CHECK'],
          paymentMethod: 'bank_transfer',
          expiryDays: 45
        }
      }
    }
  })
  create(@Body() createEscrowDto: CreateEscrowDto): Promise<EscrowDto> {
    return this.escrowService.createEscrow(createEscrowDto);
  }

  @Get()
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles('admin', 'finance')
  @ApiOperation({
    summary: 'Get all escrow transactions',
    description: 'Retrieve all escrow transactions with optional status filtering.',
  })
  @ApiResponse({
    status: 200,
    description: 'Successfully retrieved escrow transactions.',
    type: [EscrowDto],
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - Invalid or missing authentication token.',
    type: UnauthorizedResponseDto
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Requires admin or finance role.',
    type: ForbiddenResponseDto
  })
  @ApiQuery({
    name: 'status',
    required: false,
    enum: EscrowStatus,
    description: 'Filter by escrow status',
    example: EscrowStatus.HELD
  })
  findAll(@Query('status') status?: EscrowStatus): Promise<EscrowDto[]> {
    return this.escrowService.findAllEscrows(status);
  }

  @Get('order/:orderId')
  @UseGuards(JwtAuthGuard)
  @ApiOperation({
    summary: 'Get escrow by order ID',
    description: 'Retrieve escrow transaction for a specific order.',
  })
  @ApiResponse({
    status: 200,
    description: 'Successfully retrieved escrow transaction.',
    type: EscrowDto,
  })
  @ApiResponse({
    status: 404,
    description: 'Escrow transaction not found for this order.',
    type: NotFoundResponseDto
  })
  @ApiParam({
    name: 'orderId',
    description: 'Order ID to find escrow for',
    format: 'uuid',
    example: '123e4567-e89b-12d3-a456-************'
  })
  findByOrderId(@Param('orderId') orderId: string): Promise<EscrowDto | null> {
    return this.escrowService.findEscrowByOrderId(orderId);
  }

  @Patch(':id/release')
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles('admin', 'finance', 'tailor')
  @ApiOperation({
    summary: 'Release escrow payment',
    description: 'Release escrow payment to tailor after order completion and validation.',
  })
  @ApiResponse({
    status: 200,
    description: 'Escrow payment released successfully.',
    type: EscrowDto,
  })
  @ApiResponse({
    status: 400,
    description: 'Bad Request - Cannot release escrow in current status or conditions not met.',
    type: ErrorResponseDto
  })
  @ApiResponse({
    status: 404,
    description: 'Escrow transaction not found.',
    type: NotFoundResponseDto
  })
  @ApiParam({
    name: 'id',
    description: 'Escrow transaction ID',
    format: 'uuid',
    example: '123e4567-e89b-12d3-a456-************'
  })
  @ApiBody({
    type: ReleaseEscrowDto,
    required: false,
    description: 'Optional release notes',
    examples: {
      'standard-release': {
        summary: 'Standard Release',
        value: {
          notes: 'Order completed successfully, customer confirmed receipt'
        }
      }
    }
  })
  release(
    @Param('id') id: string,
    @Body() releaseEscrowDto: ReleaseEscrowDto,
    @CurrentUser() user: User,
  ): Promise<EscrowDto> {
    return this.escrowService.releaseEscrow(id, user.id, releaseEscrowDto.notes);
  }

  @Patch(':id/refund')
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles('admin', 'finance')
  @ApiOperation({
    summary: 'Refund escrow payment',
    description: 'Refund escrow payment to customer in case of order cancellation or dispute resolution.',
  })
  @ApiResponse({
    status: 200,
    description: 'Escrow payment refunded successfully.',
    type: EscrowDto,
  })
  @ApiResponse({
    status: 400,
    description: 'Bad Request - Cannot refund escrow in current status.',
    type: ErrorResponseDto
  })
  @ApiResponse({
    status: 404,
    description: 'Escrow transaction not found.',
    type: NotFoundResponseDto
  })
  @ApiParam({
    name: 'id',
    description: 'Escrow transaction ID',
    format: 'uuid',
    example: '123e4567-e89b-12d3-a456-************'
  })
  @ApiBody({
    type: RefundEscrowDto,
    description: 'Refund details',
    examples: {
      'order-cancelled': {
        summary: 'Order Cancellation Refund',
        value: {
          reason: 'Order cancelled by customer before production started'
        }
      },
      'quality-issue': {
        summary: 'Quality Issue Refund',
        value: {
          reason: 'Product quality did not meet customer expectations'
        }
      }
    }
  })
  refund(
    @Param('id') id: string,
    @Body() refundEscrowDto: RefundEscrowDto,
    @CurrentUser() user: User,
  ): Promise<EscrowDto> {
    return this.escrowService.refundEscrow(id, refundEscrowDto.reason, user.id);
  }

  @Get('summary')
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles('admin', 'finance')
  @ApiOperation({
    summary: 'Get escrow summary statistics',
    description: 'Retrieve summary statistics for all escrow transactions.',
  })
  @ApiResponse({
    status: 200,
    description: 'Successfully retrieved escrow summary.',
    type: EscrowSummaryDto,
    example: {
      totalHeld: '15000.00',
      totalReleased: '45000.00',
      totalRefunded: '2500.00',
      totalPlatformFees: '2375.00',
      activeTransactions: 25,
      disputedTransactions: 3,
      expiredTransactions: 1
    }
  })
  getSummary(): Promise<EscrowSummaryDto> {
    // This would be implemented in the service
    throw new Error('Method not implemented yet');
  }

  @Post('check-expired')
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles('admin', 'finance')
  @ApiOperation({
    summary: 'Check and process expired escrows',
    description: 'Manually trigger check for expired escrow transactions.',
  })
  @ApiResponse({
    status: 200,
    description: 'Expired escrows checked and processed.',
    schema: {
      type: 'object',
      properties: {
        message: { type: 'string', example: 'Expired escrows processed successfully' },
        processedCount: { type: 'number', example: 3 }
      }
    }
  })
  checkExpired(): Promise<{ message: string; processedCount: number }> {
    // This would be implemented to return the count of processed escrows
    return this.escrowService.checkExpiredEscrows().then(() => ({
      message: 'Expired escrows processed successfully',
      processedCount: 0 // This should be returned from the service
    }));
  }
}
