import { Module } from '@nestjs/common';
import { TrainingService } from './services/training.service';
import { TrainingController } from './controllers/training.controller';
import { TrainingResolver } from './resolvers/training.resolver';
import { CoreModule } from '@core/core.module';
import { MongoDbModule } from '@infra/mongodb/mongodb.module';
import { WebsocketModule } from '@infra/websocket/websocket.module';
import { RabbitMQModule } from '@infra/rabbitmq/rabbitmq.module';

@Module({
  imports: [
    CoreModule,
    MongoDbModule,
    WebsocketModule,
    RabbitMQModule,
  ],
  providers: [TrainingService, TrainingResolver],
  controllers: [TrainingController],
  exports: [TrainingService],
})
export class TrainingModule {}
