import { Controller, Get, Post, Body, Patch, Param, Delete, UseGuards, Query, UseInterceptors } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiParam, ApiQuery, ApiBearerAuth, ApiBody } from '@nestjs/swagger';
import { TrainingService } from '../services/training.service';
import { CreateTrainingDto } from '../dto/create-training.dto';
import { UpdateTrainingDto } from '../dto/update-training.dto';
import { TrainingDto, TrainingEnrollmentDto } from '../dto/training.dto';
import { CreateTrainingEnrollmentDto } from '../dto/create-training-enrollment.dto';
import { UpdateTrainingEnrollmentDto } from '../dto/update-training-enrollment.dto';
import { JwtAuthGuard } from '@core/auth/guards/jwt-auth.guard';
import { RolesGuard } from '@core/auth/guards/roles.guard';
import { PermissionsGuard } from '@shared/guards/permissions.guard';
import { Roles } from '@core/auth/decorators/roles.decorator';
import { RequirePermissions, Resources, ResourceActions, createPermission } from '@shared/decorators/permissions.decorator';
import { CacheInterceptor } from '@nestjs/cache-manager';
import { parseQueryParams, StandardListParams } from '@shared/utils/query-params.util';

@ApiTags('trainings')
@ApiBearerAuth()
@Controller('api/v1/trainings')
export class TrainingController {
  constructor(private readonly trainingService: TrainingService) {}

  @Post()
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles('ADMIN', 'TAILOR')
  @ApiOperation({ summary: 'Create a new training' })
  @ApiResponse({ status: 201, description: 'The training has been successfully created.', type: TrainingDto })
  @ApiResponse({ status: 400, description: 'Invalid input data.' })
  @ApiResponse({ status: 401, description: 'Unauthorized.' })
  @ApiResponse({ status: 403, description: 'Forbidden. Requires admin or tailor role.' })
  @ApiResponse({ status: 404, description: 'Instructor not found.' })
  @ApiBody({ type: CreateTrainingDto })
  create(@Body() createTrainingDto: CreateTrainingDto): Promise<TrainingDto> {
    return this.trainingService.create(createTrainingDto);
  }

  @Get()
  @UseGuards(JwtAuthGuard)
  @UseInterceptors(CacheInterceptor)
  @ApiOperation({ summary: 'Get all trainings with pagination and filtering' })
  @ApiResponse({ status: 200, description: 'Return all trainings with pagination.', type: [TrainingDto] })
  @ApiResponse({ status: 400, description: 'Bad Request.' })
  @ApiResponse({ status: 401, description: 'Unauthorized.' })
  @ApiQuery({ name: 'page', required: false, description: 'Page number (starts from 1)' })
  @ApiQuery({ name: 'limit', required: false, description: 'Items per page (max 100)' })
  @ApiQuery({ name: 'where', required: false, description: 'JSON filtering conditions' })
  @ApiQuery({ name: 'search', required: false, description: 'Text search parameter' })
  @ApiQuery({ name: 'order', required: false, description: 'Sort order specification (field and direction)' })
  findAll(@Query() query: Record<string, any>): Promise<{ data: TrainingDto[]; total: number; page: number; limit: number }> {
    const params: StandardListParams = parseQueryParams(query);
    return this.trainingService.findAll(params);
  }

  @Get(':id')
  @UseGuards(JwtAuthGuard)
  @ApiOperation({ summary: 'Get a training by ID' })
  @ApiResponse({ status: 200, description: 'Return the training.', type: TrainingDto })
  @ApiResponse({ status: 401, description: 'Unauthorized.' })
  @ApiResponse({ status: 404, description: 'Training not found.' })
  @ApiParam({ name: 'id', type: 'string', description: 'Training ID' })
  findOne(@Param('id') id: string): Promise<TrainingDto | null> {
    return this.trainingService.findOne(id);
  }

  @Get('instructor/:instructorId')
  @UseGuards(JwtAuthGuard)
  @UseInterceptors(CacheInterceptor)
  @ApiOperation({ summary: 'Get trainings by instructor ID' })
  @ApiResponse({ status: 200, description: 'Return the trainings with pagination.', type: [TrainingDto] })
  @ApiResponse({ status: 401, description: 'Unauthorized.' })
  @ApiResponse({ status: 404, description: 'Instructor not found.' })
  @ApiParam({ name: 'instructorId', type: 'string', description: 'Instructor ID' })
  @ApiQuery({ name: 'page', required: false, description: 'Page number (starts from 1)' })
  @ApiQuery({ name: 'limit', required: false, description: 'Items per page (max 100)' })
  @ApiQuery({ name: 'where', required: false, description: 'JSON filtering conditions' })
  @ApiQuery({ name: 'search', required: false, description: 'Text search parameter' })
  @ApiQuery({ name: 'order', required: false, description: 'Sort order specification (field and direction)' })
  findByInstructorId(
    @Param('instructorId') instructorId: string,
    @Query() query: Record<string, any>,
  ): Promise<{ data: TrainingDto[]; pagination: any }> {
    const params: StandardListParams = parseQueryParams(query);
    return this.trainingService.findByInstructorId(instructorId, params);
  }

  @Patch(':id')
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles('ADMIN', 'TAILOR')
  @ApiOperation({ summary: 'Update a training' })
  @ApiResponse({ status: 200, description: 'The training has been successfully updated.', type: TrainingDto })
  @ApiResponse({ status: 400, description: 'Invalid input data.' })
  @ApiResponse({ status: 401, description: 'Unauthorized.' })
  @ApiResponse({ status: 403, description: 'Forbidden. Requires admin or tailor role.' })
  @ApiResponse({ status: 404, description: 'Training not found.' })
  @ApiParam({ name: 'id', type: 'string', description: 'Training ID' })
  @ApiBody({ type: UpdateTrainingDto })
  update(@Param('id') id: string, @Body() updateTrainingDto: UpdateTrainingDto): Promise<TrainingDto> {
    return this.trainingService.update(id, updateTrainingDto);
  }

  @Delete(':id')
  @UseGuards(JwtAuthGuard, RolesGuard, PermissionsGuard)
  @Roles('ADMIN')
  @RequirePermissions(createPermission(Resources.TRAININGS, ResourceActions.DELETE))
  @ApiOperation({ summary: 'Delete a training' })
  @ApiResponse({ status: 200, description: 'The training has been successfully deleted.' })
  @ApiResponse({ status: 401, description: 'Unauthorized.' })
  @ApiResponse({ status: 403, description: 'Forbidden. Requires admin role and delete permission.' })
  @ApiResponse({ status: 404, description: 'Training not found.' })
  @ApiResponse({ status: 409, description: 'Conflict. Training has enrollments.' })
  @ApiParam({ name: 'id', type: 'string', description: 'Training ID' })
  remove(@Param('id') id: string): Promise<{ success: boolean; message: string }> {
    return this.trainingService.remove(id);
  }

  // Training Enrollment Endpoints
  @Post('enrollments')
  @UseGuards(JwtAuthGuard)
  @ApiOperation({ summary: 'Create a new training enrollment' })
  @ApiResponse({ status: 201, description: 'The enrollment has been successfully created.', type: TrainingEnrollmentDto })
  @ApiResponse({ status: 400, description: 'Invalid input data.' })
  @ApiResponse({ status: 401, description: 'Unauthorized.' })
  @ApiResponse({ status: 404, description: 'Training or user not found.' })
  @ApiResponse({ status: 409, description: 'User is already enrolled in this training.' })
  @ApiBody({ type: CreateTrainingEnrollmentDto })
  @ApiResponse({ status: 201, description: 'The training enrollment has been successfully created.' })
  @ApiResponse({ status: 400, description: 'Invalid input data.' })
  @ApiResponse({ status: 401, description: 'Unauthorized.' })
  @ApiResponse({ status: 404, description: 'User or training not found.' })
  @ApiResponse({ status: 409, description: 'User is already enrolled in this training.' })
  createEnrollment(@Body() createTrainingEnrollmentDto: CreateTrainingEnrollmentDto) {
    return this.trainingService.createEnrollment(createTrainingEnrollmentDto);
  }

  @Get('enrollments')
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles('ADMIN')
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Get all training enrollments' })
  @ApiResponse({ status: 200, description: 'Return all training enrollments.' })
  @ApiResponse({ status: 401, description: 'Unauthorized.' })
  @ApiResponse({ status: 403, description: 'Forbidden.' })
  @ApiQuery({ name: 'page', required: false, type: Number, description: 'Page number' })
  @ApiQuery({ name: 'limit', required: false, type: Number, description: 'Items per page' })
  @ApiQuery({ name: 'order', required: false, type: Object, description: 'Sort order' })
  findAllEnrollments(@Query() params: StandardListParams) {
    return this.trainingService.findAllEnrollments(params);
  }

  @Get('enrollments/user/:userId')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Get training enrollments by user ID' })
  @ApiResponse({ status: 200, description: 'Return the training enrollments.' })
  @ApiResponse({ status: 401, description: 'Unauthorized.' })
  @ApiParam({ name: 'userId', type: 'string', description: 'User ID' })
  @ApiQuery({ name: 'page', required: false, type: Number, description: 'Page number' })
  @ApiQuery({ name: 'limit', required: false, type: Number, description: 'Items per page' })
  @ApiQuery({ name: 'order', required: false, type: Object, description: 'Sort order' })
  findEnrollmentsByUserId(
    @Param('userId') userId: string,
    @Query() params: StandardListParams,
  ) {
    return this.trainingService.findEnrollmentsByUserId(userId, params);
  }

  @Get('enrollments/training/:trainingId')
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles('ADMIN', 'TAILOR')
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Get training enrollments by training ID' })
  @ApiResponse({ status: 200, description: 'Return the training enrollments.' })
  @ApiResponse({ status: 401, description: 'Unauthorized.' })
  @ApiResponse({ status: 403, description: 'Forbidden.' })
  @ApiParam({ name: 'trainingId', type: 'string', description: 'Training ID' })
  @ApiQuery({ name: 'page', required: false, type: Number, description: 'Page number' })
  @ApiQuery({ name: 'limit', required: false, type: Number, description: 'Items per page' })
  @ApiQuery({ name: 'order', required: false, type: Object, description: 'Sort order' })
  findEnrollmentsByTrainingId(
    @Param('trainingId') trainingId: string,
    @Query() params: StandardListParams,
  ) {
    return this.trainingService.findEnrollmentsByTrainingId(trainingId, params);
  }

  @Get('enrollments/:id')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Get a training enrollment by ID' })
  @ApiResponse({ status: 200, description: 'Return the training enrollment.' })
  @ApiResponse({ status: 401, description: 'Unauthorized.' })
  @ApiResponse({ status: 404, description: 'Training enrollment not found.' })
  @ApiParam({ name: 'id', type: 'string', description: 'Training enrollment ID' })
  findOneEnrollment(@Param('id') id: string) {
    return this.trainingService.findOneEnrollment(id);
  }

  @Patch('enrollments/:id')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Update a training enrollment' })
  @ApiResponse({ status: 200, description: 'The training enrollment has been successfully updated.' })
  @ApiResponse({ status: 400, description: 'Invalid input data.' })
  @ApiResponse({ status: 401, description: 'Unauthorized.' })
  @ApiResponse({ status: 404, description: 'Training enrollment not found.' })
  @ApiParam({ name: 'id', type: 'string', description: 'Training enrollment ID' })
  updateEnrollment(
    @Param('id') id: string,
    @Body() updateTrainingEnrollmentDto: UpdateTrainingEnrollmentDto,
  ) {
    return this.trainingService.updateEnrollment(id, updateTrainingEnrollmentDto);
  }

  @Delete('enrollments/:id')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Delete a training enrollment' })
  @ApiResponse({ status: 200, description: 'The training enrollment has been successfully deleted.' })
  @ApiResponse({ status: 401, description: 'Unauthorized.' })
  @ApiResponse({ status: 404, description: 'Training enrollment not found.' })
  @ApiParam({ name: 'id', type: 'string', description: 'Training enrollment ID' })
  removeEnrollment(@Param('id') id: string) {
    return this.trainingService.removeEnrollment(id);
  }
}
