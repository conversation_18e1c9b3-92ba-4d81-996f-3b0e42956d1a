import { Resolver, Query, Mutation, Args, ID } from '@nestjs/graphql';
import { TrainingService } from '../services/training.service';
import { Training } from '../entities/training.entity';
import { TrainingEnrollment } from '../entities/training-enrollment.entity';
import { CreateTrainingDto } from '../dto/create-training.dto';
import { UpdateTrainingDto } from '../dto/update-training.dto';
import { CreateTrainingEnrollmentDto } from '../dto/create-training-enrollment.dto';
import { UpdateTrainingEnrollmentDto } from '../dto/update-training-enrollment.dto';
import { UseGuards } from '@nestjs/common';
import { JwtAuthGuard } from '@core/auth/guards/jwt-auth.guard';
import { RolesGuard } from '@core/auth/guards/roles.guard';
import { Roles } from '@core/auth/decorators/roles.decorator';
import { PaginatedResponse } from '@shared/interfaces/paginated-response.interface';
import { ListParamsArgs } from '@shared/dto/list-params.args';

// Create paginated response types for Training and TrainingEnrollment
const PaginatedTrainingResponse = PaginatedResponse(Training);
const PaginatedTrainingEnrollmentResponse = PaginatedResponse(TrainingEnrollment);

@Resolver(() => Training)
export class TrainingResolver {
  constructor(private readonly trainingService: TrainingService) {}

  @Mutation(() => Training)
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles('ADMIN', 'TAILOR')
  createTraining(
    @Args('createTrainingDto') createTrainingDto: CreateTrainingDto,
  ) {
    return this.trainingService.create(createTrainingDto);
  }

  @Query(() => PaginatedTrainingResponse, { name: 'trainings' })
  findAllTrainings(@Args() params: ListParamsArgs) {
    return this.trainingService.findAll(params);
  }

  @Query(() => Training, { name: 'training' })
  findOneTraining(@Args('id', { type: () => ID }) id: string) {
    return this.trainingService.findOne(id);
  }

  @Query(() => PaginatedTrainingResponse, { name: 'trainingsByInstructorId' })
  findByInstructorId(
    @Args('instructorId', { type: () => ID }) instructorId: string,
    @Args() params: ListParamsArgs,
  ) {
    return this.trainingService.findByInstructorId(instructorId, params);
  }

  @Mutation(() => Training)
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles('ADMIN', 'TAILOR')
  updateTraining(
    @Args('id', { type: () => ID }) id: string,
    @Args('updateTrainingDto') updateTrainingDto: UpdateTrainingDto,
  ) {
    return this.trainingService.update(id, updateTrainingDto);
  }

  @Mutation(() => Training)
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles('ADMIN', 'TAILOR')
  removeTraining(@Args('id', { type: () => ID }) id: string) {
    return this.trainingService.remove(id);
  }

  // Training Enrollment Resolvers
  @Mutation(() => TrainingEnrollment)
  @UseGuards(JwtAuthGuard)
  createTrainingEnrollment(
    @Args('createTrainingEnrollmentDto') createTrainingEnrollmentDto: CreateTrainingEnrollmentDto,
  ) {
    return this.trainingService.createEnrollment(createTrainingEnrollmentDto);
  }

  @Query(() => PaginatedTrainingEnrollmentResponse, { name: 'trainingEnrollments' })
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles('ADMIN')
  findAllTrainingEnrollments(@Args() params: ListParamsArgs) {
    return this.trainingService.findAllEnrollments(params);
  }

  @Query(() => PaginatedTrainingEnrollmentResponse, { name: 'trainingEnrollmentsByUserId' })
  @UseGuards(JwtAuthGuard)
  findEnrollmentsByUserId(
    @Args('userId', { type: () => ID }) userId: string,
    @Args() params: ListParamsArgs,
  ) {
    return this.trainingService.findEnrollmentsByUserId(userId, params);
  }

  @Query(() => PaginatedTrainingEnrollmentResponse, { name: 'trainingEnrollmentsByTrainingId' })
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles('ADMIN', 'TAILOR')
  findEnrollmentsByTrainingId(
    @Args('trainingId', { type: () => ID }) trainingId: string,
    @Args() params: ListParamsArgs,
  ) {
    return this.trainingService.findEnrollmentsByTrainingId(trainingId, params);
  }

  @Query(() => TrainingEnrollment, { name: 'trainingEnrollment' })
  @UseGuards(JwtAuthGuard)
  findOneTrainingEnrollment(@Args('id', { type: () => ID }) id: string) {
    return this.trainingService.findOneEnrollment(id);
  }

  @Mutation(() => TrainingEnrollment)
  @UseGuards(JwtAuthGuard)
  updateTrainingEnrollment(
    @Args('id', { type: () => ID }) id: string,
    @Args('updateTrainingEnrollmentDto') updateTrainingEnrollmentDto: UpdateTrainingEnrollmentDto,
  ) {
    return this.trainingService.updateEnrollment(id, updateTrainingEnrollmentDto);
  }

  @Mutation(() => TrainingEnrollment)
  @UseGuards(JwtAuthGuard)
  removeTrainingEnrollment(@Args('id', { type: () => ID }) id: string) {
    return this.trainingService.removeEnrollment(id);
  }
}
