import { ObjectType, Field, ID, Int } from '@nestjs/graphql';
import { ApiProperty } from '@nestjs/swagger';
import { User } from '@apps/user/entities/user.entity';

@ObjectType()
export class Training {
  @ApiProperty({ description: 'Unique identifier for the training' })
  @Field(() => ID)
  id: string;

  @ApiProperty({ description: 'Title of the training' })
  @Field()
  title: string;

  @ApiProperty({ description: 'Description of the training' })
  @Field()
  description: string;

  @ApiProperty({ description: 'Instructor user ID' })
  @Field(() => ID)
  instructorId: string;

  @ApiProperty({ description: 'Instructor user' })
  @Field(() => User, { nullable: true })
  instructor?: User;

  @ApiProperty({ description: 'Content type (video, pdf, etc.)' })
  @Field()
  contentType: string;

  @ApiProperty({ description: 'Content URL' })
  @Field()
  contentUrl: string;

  @ApiProperty({ description: 'Price of the training', nullable: true })
  @Field({ nullable: true })
  price?: string;

  @ApiProperty({ description: 'Access level (free, premium)' })
  @Field()
  accessLevel: string;

  @ApiProperty({ description: 'Duration in minutes', nullable: true })
  @Field(() => Int, { nullable: true })
  duration?: number;

  @ApiProperty({ description: 'Creation timestamp' })
  @Field(() => Date)
  createdAt: Date;

  @ApiProperty({ description: 'Last update timestamp' })
  @Field(() => Date)
  updatedAt: Date;
}
