import { ObjectType, Field, ID, Int } from '@nestjs/graphql';
import { ApiProperty } from '@nestjs/swagger';
import { User } from '@apps/user/entities/user.entity';
import { Training } from './training.entity';

@ObjectType()
export class TrainingEnrollment {
  @ApiProperty({ description: 'Unique identifier for the training enrollment' })
  @Field(() => ID)
  id: string;

  @ApiProperty({ description: 'User ID associated with this enrollment' })
  @Field(() => ID)
  userId: string;

  @ApiProperty({ description: 'User associated with this enrollment' })
  @Field(() => User, { nullable: true })
  user?: User;

  @ApiProperty({ description: 'Training ID associated with this enrollment' })
  @Field(() => ID)
  trainingId: string;

  @ApiProperty({ description: 'Training associated with this enrollment' })
  @Field(() => Training, { nullable: true })
  training?: Training;

  @ApiProperty({ description: 'Enrollment timestamp' })
  @Field(() => Date)
  enrolledAt: Date;

  @ApiProperty({ description: 'Completion timestamp', nullable: true })
  @Field(() => Date, { nullable: true })
  completedAt?: Date;

  @ApiProperty({ description: 'Progress percentage (0-100)' })
  @Field(() => Int)
  progress: number;
}
