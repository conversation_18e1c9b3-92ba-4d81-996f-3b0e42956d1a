import { Injectable, NotFoundException, ConflictException, BadRequestException } from '@nestjs/common';
import { PrismaService } from '@core/prisma/prisma.service';
import { MongoDbService } from '@infra/mongodb/mongodb.service';
import { WebsocketService } from '@infra/websocket/websocket.service';
import { RabbitMQService } from '@infra/rabbitmq/rabbitmq.service';
import { LoggerService } from '@core/logger/logger.service';
import { CreateTrainingDto } from '../dto/create-training.dto';
import { UpdateTrainingDto } from '../dto/update-training.dto';
import { CreateTrainingEnrollmentDto } from '../dto/create-training-enrollment.dto';
import { UpdateTrainingEnrollmentDto } from '../dto/update-training-enrollment.dto';
import { TrainingDto } from '../dto/training.dto';
import { StandardListParams, getPaginationMetadata, toMongoDbOptions } from '@shared/utils/query-params.util';

@Injectable()
export class TrainingService {
  constructor(
    private readonly prisma: PrismaService,
    private readonly mongoDbService: MongoDbService,
    private readonly websocketService: WebsocketService,
    private readonly rabbitMQService: RabbitMQService,
    private readonly loggerService: LoggerService,
  ) {}

  async create(createTrainingDto: CreateTrainingDto): Promise<TrainingDto> {
    try {
      this.loggerService.log(`Creating new training: ${createTrainingDto.title}`);
      
      // Check if instructor exists
      const instructor = await this.prisma.user.findUnique({
        where: { id: createTrainingDto.instructorId },
      });

      if (!instructor) {
        this.loggerService.warn(`Instructor with ID ${createTrainingDto.instructorId} not found`);
        throw new NotFoundException(`Instructor with ID ${createTrainingDto.instructorId} not found`);
      }

      // Create training in PostgreSQL (source of truth)
      const newTraining = await this.prisma.training.create({
        data: {
          title: createTrainingDto.title,
          description: createTrainingDto.description,
          instructorId: createTrainingDto.instructorId,
          contentType: createTrainingDto.contentType,
          contentUrl: createTrainingDto.contentUrl,
          price: createTrainingDto.price,
          accessLevel: createTrainingDto.accessLevel || 'free',
          duration: createTrainingDto.duration,
        },
      });

      // Sync to MongoDB for fast reads
      await this.syncTrainingToMongo(newTraining);

      // Notify about new training creation
      await this.notifyTrainingCreation(newTraining);
      
      this.loggerService.log(`Training created successfully with ID: ${newTraining.id}`);
      
      // Map to DTO before returning
      return this.mapToTrainingDto(newTraining);
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      this.loggerService.error(`Failed to create training: ${error.message}`, error.stack);
      throw new BadRequestException(`Failed to create training: ${error.message}`);
    }
  }

  async findAll(params: StandardListParams = {}) {
    try {
      this.loggerService.log(`Finding all trainings with params: ${JSON.stringify(params)}`);
      
      // Set default pagination parameters
      const page = params?.page || 1;
      const limit = Math.min(params?.limit || 10, 100); // Cap at 100 items per page
      
      // Build query filters
      let filter: Record<string, any> = {};
      
      if (params?.where) {
        if (typeof params.where === 'string') {
          try {
            filter = JSON.parse(params.where);
          } catch (e) {
            this.loggerService.error(`Invalid JSON in where parameter: ${params.where}`);
          }
        } else if (typeof params.where === 'object') {
          filter = params.where;
        }
      }
      
      // Apply search if provided
      if (params?.search) {
        filter.$or = [
          { title: { $regex: params.search, $options: 'i' } },
          { description: { $regex: params.search, $options: 'i' } },
          { contentType: { $regex: params.search, $options: 'i' } },
          { accessLevel: { $regex: params.search, $options: 'i' } }
        ];
      }
      
      // Convert standard params to MongoDB options
      const options = toMongoDbOptions(params);
      
      // Read from MongoDB for better performance
      const trainings = await this.mongoDbService.find('trainings', filter, options);
      const total = await this.mongoDbService.count('trainings', filter);
      
      // Map each training to DTO
      const mappedTrainings = trainings.map(training => this.mapToTrainingDto(training));
      
      // Get standardized pagination metadata
      const paginationMeta = getPaginationMetadata(params, total);
      
      // Return data with pagination metadata in standard format
      return {
        data: mappedTrainings,
        total,
        ...paginationMeta
      };
    } catch (error) {
      this.loggerService.error(`Error finding trainings: ${error.message}`, error.stack);
      throw new BadRequestException(`Failed to fetch trainings: ${error.message}`);
    }
  }

  async findOne(id: string): Promise<TrainingDto> {
    try {
      this.loggerService.log(`Finding training with ID: ${id}`);
      
      // Read from MongoDB for better performance
      const training = await this.mongoDbService.findOne('trainings', { id });

      if (!training) {
        this.loggerService.warn(`Training with ID ${id} not found`);
        throw new NotFoundException(`Training with ID ${id} not found`);
      }

      // Map to DTO before returning
      return this.mapToTrainingDto(training);
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      this.loggerService.error(`Error finding training ${id}: ${error.message}`, error.stack);
      throw new BadRequestException(`Failed to fetch training: ${error.message}`);
    }
  }

  async findByInstructorId(instructorId: string, params: StandardListParams = {}): Promise<{ data: TrainingDto[], pagination: any }> {
    // Read from MongoDB for better performance
    const filter = { instructorId, ...(params?.where || {}) };
    const options: Record<string, any> = {};
    
    // Apply pagination
    if (params?.page && params?.limit) {
      options.skip = (params.page - 1) * params.limit;
      options.limit = params.limit;
    } else if (params?.limit) {
      options.limit = params.limit;
    }
    
    // Apply sorting
    if (params?.order) {
      options.sort = {};
      for (const [field, direction] of Object.entries(params.order)) {
        options.sort[field] = direction === 'asc' ? 1 : -1;
      }
    }

    // Apply search if provided
    if (params?.search) {
      // Text search across multiple fields
      (filter as any)['$or'] = [
        { title: { $regex: params.search, $options: 'i' } },
        { description: { $regex: params.search, $options: 'i' } },
        { contentType: { $regex: params.search, $options: 'i' } },
        { accessLevel: { $regex: params.search, $options: 'i' } }
      ];
    }
    
    // Get total count for pagination metadata
    const totalCount = await this.mongoDbService.count('trainings', filter);
    
    // Get trainings with filters and options
    const trainings = await this.mongoDbService.find('trainings', filter, options);
    
    // Map each training to DTO
    const mappedTrainings = trainings.map(training => this.mapToTrainingDto(training));
    
    // Get standardized pagination metadata
    const paginationMeta = getPaginationMetadata(params, totalCount);
    
    // Return data with pagination metadata in standard format
    return {
      data: mappedTrainings,
      pagination: paginationMeta
    };
  }

  async update(id: string, updateTrainingDto: UpdateTrainingDto): Promise<TrainingDto> {
    // Check if training exists
    const training = await this.prisma.training.findUnique({
      where: { id },
    });
    
    if (!training) {
      throw new NotFoundException(`Training with ID ${id} not found`);
    }
    
    // Update training in PostgreSQL
    const updatedTraining = await this.prisma.training.update({
      where: { id },
      data: {
        title: updateTrainingDto.title !== undefined ? updateTrainingDto.title : training.title,
        description: updateTrainingDto.description !== undefined ? updateTrainingDto.description : training.description,
        contentType: updateTrainingDto.contentType !== undefined ? updateTrainingDto.contentType : training.contentType,
        contentUrl: updateTrainingDto.contentUrl !== undefined ? updateTrainingDto.contentUrl : training.contentUrl,
        price: updateTrainingDto.price !== undefined ? updateTrainingDto.price : training.price,
        accessLevel: updateTrainingDto.accessLevel !== undefined ? updateTrainingDto.accessLevel : training.accessLevel,
        duration: updateTrainingDto.duration !== undefined ? updateTrainingDto.duration : training.duration,
      },
    });
    
    // Sync to MongoDB for fast reads
    await this.syncTrainingToMongo(updatedTraining);
    
    // Notify about training update
    this.notifyTrainingUpdate(updatedTraining);
    
    // Map to DTO before returning
    return this.mapToTrainingDto(updatedTraining);
  }

  async remove(id: string): Promise<{ success: boolean; message: string }> {
    // Check if training exists
    const training = await this.prisma.training.findUnique({
      where: { id },
    });
    
    if (!training) {
      throw new NotFoundException(`Training with ID ${id} not found`);
    }
    
    // Check if there are enrollments for this training
    const enrollmentCount = await this.prisma.trainingEnrollment.count({
      where: { trainingId: id },
    });
    
    if (enrollmentCount > 0) {
      throw new ConflictException(`Cannot delete training with ID ${id} because it has ${enrollmentCount} enrollments`);
    }
    
    // Delete training from PostgreSQL
    await this.prisma.training.delete({
      where: { id },
    });
    
    // Delete from MongoDB
    await this.mongoDbService.deleteDocument('trainings', id);
    
    // Notify about training deletion
    this.notifyTrainingDeletion(id, training.instructorId);
    
    return { success: true, message: `Training with ID ${id} successfully deleted` };
  }

  // Enrollment methods
  async createEnrollment(createTrainingEnrollmentDto: CreateTrainingEnrollmentDto) {
    // Check if user exists
    const user = await this.prisma.user.findUnique({
      where: { id: createTrainingEnrollmentDto.userId },
    });

    if (!user) {
      throw new NotFoundException(`User with ID ${createTrainingEnrollmentDto.userId} not found`);
    }

    // Check if training exists
    const training = await this.prisma.training.findUnique({
      where: { id: createTrainingEnrollmentDto.trainingId },
    });

    if (!training) {
      throw new NotFoundException(`Training with ID ${createTrainingEnrollmentDto.trainingId} not found`);
    }

    // Check if enrollment already exists
    const existingEnrollment = await this.prisma.trainingEnrollment.findFirst({
      where: {
        userId: createTrainingEnrollmentDto.userId,
        trainingId: createTrainingEnrollmentDto.trainingId,
      },
    });

    if (existingEnrollment) {
      throw new ConflictException(`User ${createTrainingEnrollmentDto.userId} is already enrolled in training ${createTrainingEnrollmentDto.trainingId}`);
    }

    // Create enrollment in PostgreSQL
    const newEnrollment = await this.prisma.trainingEnrollment.create({
      data: {
        userId: createTrainingEnrollmentDto.userId,
        trainingId: createTrainingEnrollmentDto.trainingId,
        progress: createTrainingEnrollmentDto.progress || 0,
      },
    });

    // Sync to MongoDB for fast reads
    await this.syncEnrollmentToMongo(newEnrollment);

    // Notify about new enrollment
    this.notifyEnrollmentCreation(newEnrollment, training);

    return newEnrollment;
  }

  async findAllEnrollments(params: StandardListParams = {}): Promise<{ data: any[], pagination: any }> {
    // Read from MongoDB for better performance
    const filter = params?.where || {};
    const options: Record<string, any> = {};
    
    // Apply pagination
    if (params?.page && params?.limit) {
      options.skip = (params.page - 1) * params.limit;
      options.limit = params.limit;
    } else if (params?.limit) {
      options.limit = params.limit;
    }
    
    // Apply sorting
    if (params?.order) {
      options.sort = {};
      for (const [field, direction] of Object.entries(params.order)) {
        options.sort[field] = direction === 'asc' ? 1 : -1;
      }
    }
    
    // Get total count for pagination metadata
    const totalCount = await this.mongoDbService.count('trainingEnrollments', filter);
    
    // Get enrollments with filters and options
    const enrollments = await this.mongoDbService.find('trainingEnrollments', filter, options);
    
    // Get standardized pagination metadata
    const paginationMeta = getPaginationMetadata(params, totalCount);
    
    // Return data with pagination metadata in standard format
    return {
      data: enrollments,
      pagination: paginationMeta
    };
  }

  async findEnrollmentsByUserId(userId: string, params: StandardListParams = {}): Promise<{ data: any[], pagination: any }> {
    // Read from MongoDB for better performance
    const filter = { userId, ...(params?.where || {}) };
    const options: Record<string, any> = {};
    
    // Apply pagination
    if (params?.page && params?.limit) {
      options.skip = (params.page - 1) * params.limit;
      options.limit = params.limit;
    } else if (params?.limit) {
      options.limit = params.limit;
    }
    
    // Apply sorting
    if (params?.order) {
      options.sort = {};
      for (const [field, direction] of Object.entries(params.order)) {
        options.sort[field] = direction === 'asc' ? 1 : -1;
      }
    }
    
    // Get total count for pagination metadata
    const totalCount = await this.mongoDbService.count('trainingEnrollments', filter);
    
    // Get enrollments with filters and options
    const enrollments = await this.mongoDbService.find('trainingEnrollments', filter, options);
    
    // Get standardized pagination metadata
    const paginationMeta = getPaginationMetadata(params, totalCount);
    
    // Return data with pagination metadata in standard format
    return {
      data: enrollments,
      pagination: paginationMeta
    };
  }

  async findEnrollmentsByTrainingId(trainingId: string, params: StandardListParams = {}): Promise<{ data: any[], pagination: any }> {
    // Read from MongoDB for better performance
    const filter = { trainingId, ...(params?.where || {}) };
    const options: Record<string, any> = {};
    
    // Apply pagination
    if (params?.page && params?.limit) {
      options.skip = (params.page - 1) * params.limit;
      options.limit = params.limit;
    } else if (params?.limit) {
      options.limit = params.limit;
    }
    
    // Apply sorting
    if (params?.order) {
      options.sort = {};
      for (const [field, direction] of Object.entries(params.order)) {
        options.sort[field] = direction === 'asc' ? 1 : -1;
      }
    }
    
    // Get total count for pagination metadata
    const totalCount = await this.mongoDbService.count('trainingEnrollments', filter);
    
    // Get enrollments with filters and options
    const enrollments = await this.mongoDbService.find('trainingEnrollments', filter, options);
    
    // Get standardized pagination metadata
    const paginationMeta = getPaginationMetadata(params, totalCount);
    
    // Return data with pagination metadata in standard format
    return {
      data: enrollments,
      pagination: paginationMeta
    };
  }

  async findOneEnrollment(id: string) {
    // Read from MongoDB for better performance
    const enrollment = await this.mongoDbService.findOne('trainingEnrollments', { id });
    
    if (!enrollment) {
      throw new NotFoundException(`Training enrollment with ID ${id} not found`);
    }
    
    return enrollment;
  }

  async updateEnrollment(id: string, updateTrainingEnrollmentDto: UpdateTrainingEnrollmentDto) {
    // Check if enrollment exists
    const enrollment = await this.prisma.trainingEnrollment.findUnique({
      where: { id },
    });
    
    if (!enrollment) {
      throw new NotFoundException(`Training enrollment with ID ${id} not found`);
    }
    
    // Prepare update data
    const updateData: any = {};
    
    if (updateTrainingEnrollmentDto.progress !== undefined) {
      updateData.progress = updateTrainingEnrollmentDto.progress;
    }
    
    if (updateTrainingEnrollmentDto.completedAt !== undefined) {
      updateData.completedAt = updateTrainingEnrollmentDto.completedAt;
    }
    
    // If progress is 100 and completedAt is not set, set completedAt to current date
    if (updateData.progress === 100 && !updateData.completedAt && !enrollment.completedAt) {
      updateData.completedAt = new Date();
    }
    
    // Update enrollment in PostgreSQL
    const updatedEnrollment = await this.prisma.trainingEnrollment.update({
      where: { id },
      data: updateData,
    });
    
    // Sync to MongoDB for fast reads
    await this.syncEnrollmentToMongo(updatedEnrollment);
    
    // Notify about enrollment update
    this.notifyEnrollmentUpdate(updatedEnrollment);
    
    return updatedEnrollment;
  }

  async removeEnrollment(id: string) {
    // Check if enrollment exists
    const enrollment = await this.prisma.trainingEnrollment.findUnique({
      where: { id },
    });
    
    if (!enrollment) {
      throw new NotFoundException(`Training enrollment with ID ${id} not found`);
    }
    
    // Delete enrollment from PostgreSQL
    await this.prisma.trainingEnrollment.delete({
      where: { id },
    });
    
    // Delete from MongoDB
    await this.mongoDbService.deleteDocument('trainingEnrollments', id);
    
    // Notify about enrollment deletion
    this.notifyEnrollmentDeletion(id, enrollment.userId, enrollment.trainingId);
    
    return { id };
  }

  // Helper methods for MongoDB sync and notifications
  private async syncTrainingToMongo(training: any) {
    try {
      await this.mongoDbService.syncDocument('trainings', training);
      this.loggerService.log(`Synced training ${training.id} to MongoDB`);
    } catch (error) {
      this.loggerService.error(`Failed to sync training to MongoDB: ${error.message}`, error.stack);
      throw new Error(`Failed to sync training to MongoDB: ${error.message}`);
    }
  }

  private async syncEnrollmentToMongo(enrollment: any) {
    await this.mongoDbService.syncDocument('trainingEnrollments', enrollment);
  }

  /**
   * Maps raw training data to a TrainingDto
   * @param training Raw training data from MongoDB or PostgreSQL
   * @returns Mapped TrainingDto
   */
  private mapToTrainingDto(training: any): TrainingDto {
    if (!training) {
      throw new NotFoundException('Training not found');
    }
    
    const trainingDto = new TrainingDto();
    trainingDto.id = training.id;
    trainingDto.title = training.title;
    trainingDto.description = training.description;
    trainingDto.instructorId = training.instructorId;
    trainingDto.contentType = training.contentType;
    trainingDto.contentUrl = training.contentUrl;
    trainingDto.price = training.price !== null ? training.price : '0';
    trainingDto.accessLevel = training.accessLevel;
    trainingDto.duration = training.duration || 0;
    trainingDto.createdAt = training.createdAt;
    trainingDto.updatedAt = training.updatedAt;
    
    // Map enrollments if they exist
    if (training.enrollments && Array.isArray(training.enrollments)) {
      trainingDto.enrollments = training.enrollments.map(enrollment => ({
        id: enrollment.id,
        userId: enrollment.userId,
        trainingId: enrollment.trainingId,
        enrollmentDate: enrollment.enrollmentDate,
        completionDate: enrollment.completionDate,
        status: enrollment.status,
        progress: enrollment.progress,
        createdAt: enrollment.createdAt,
        updatedAt: enrollment.updatedAt
      }));
    } else {
      trainingDto.enrollments = [];
    }
    
    return trainingDto;
  }
  
  private async notifyTrainingCreation(training: any) {
    try {
      // Send message to RabbitMQ for event processing
      await this.rabbitMQService.publish({
        type: 'training_created',
        data: training,
      });

      // Notify admin users via WebSocket
      this.websocketService.sendToRole('admin', 'training_created', {
        message: `New training created: ${training.title}`,
        training,
      });
      
      this.loggerService.log(`Notifications sent for new training: ${training.id}`);
    } catch (error) {
      this.loggerService.error(`Failed to send training notifications: ${error.message}`, error.stack);
      // Don't throw here to prevent blocking the main flow if notifications fail
    }
  }

  private async notifyTrainingUpdate(training: any) {
    // Send message to RabbitMQ for event processing
    await this.rabbitMQService.publish({
      type: 'training_updated',
      data: training,
    });

    // Notify instructor via WebSocket
    this.websocketService.sendToUser(training.instructorId, 'training_updated', {
      message: 'Your training has been updated',
      training,
    });

    // Find all enrolled users and notify them
    const enrollments = await this.prisma.trainingEnrollment.findMany({
      where: { trainingId: training.id },
    });

    for (const enrollment of enrollments) {
      this.websocketService.sendToUser(enrollment.userId, 'training_updated', {
        message: 'A training you are enrolled in has been updated',
        training,
      });
    }
  }

  private async notifyTrainingDeletion(trainingId: string, instructorId: string) {
    // Send message to RabbitMQ for event processing
    await this.rabbitMQService.publish({
      type: 'training_deleted',
      data: { id: trainingId, instructorId },
    });

    // Notify instructor via WebSocket
    this.websocketService.sendToUser(instructorId, 'training_deleted', {
      message: `Training with ID ${trainingId} has been deleted`,
      trainingId,
    });

    // Notify admin users via WebSocket
    this.websocketService.sendToRole('ADMIN', 'training_deleted', {
      message: `Training with ID ${trainingId} has been deleted by instructor ${instructorId}`,
      trainingId,
      instructorId,
    });
  }

  private async notifyEnrollmentCreation(enrollment: any, training: any) {
    // Send message to RabbitMQ for event processing
    await this.rabbitMQService.publish({
      type: 'training_enrollment_created',
      data: enrollment,
    });

    // Notify user via WebSocket
    if (training) {
      this.websocketService.sendToUser(enrollment.userId, 'training_enrollment_created', {
        message: `You have been enrolled in training: ${training.title}`,
        enrollment,
        training,
      });

      // Notify instructor via WebSocket
      this.websocketService.sendToUser(training.instructorId, 'training_enrollment_created', {
        message: `A new user has enrolled in your training: ${training.title}`,
        enrollment,
        training,
      });
    }

    // Create notification in database for user
    if (training) {
      await this.prisma.notification.create({
        data: {
          userId: enrollment.userId,
          title: 'Training Enrollment',
          body: `You have been enrolled in training: ${training.title}`,
          isRead: false,
          type: 'training_enrollment',
          data: { trainingId: training.id, enrollmentId: enrollment.id },
        },
      });
    }
  }

  private async notifyEnrollmentUpdate(enrollment: any) {
    // Send message to RabbitMQ for event processing
    await this.rabbitMQService.publish({
      type: 'training_enrollment_updated',
      data: enrollment,
    });

    // Get training details
    const training = await this.prisma.training.findUnique({
      where: { id: enrollment.trainingId },
    });

    // Notify user via WebSocket
    this.websocketService.sendToUser(enrollment.userId, 'training_enrollment_updated', {
      message: `Your training progress has been updated`,
      enrollment,
      training,
    });

    // Notify instructor via WebSocket
    if (training) {
      this.websocketService.sendToUser(training.instructorId, 'training_enrollment_updated', {
        message: `A user's progress in your training has been updated`,
        enrollment,
        training,
      });
    }

    // If enrollment is completed, create a notification
    if (enrollment.progress === 100 && enrollment.completedAt && training) {
      await this.prisma.notification.create({
        data: {
          userId: enrollment.userId,
          title: 'Training Completed',
          body: `Congratulations! You have completed the training: ${training.title}`,
          isRead: false,
          type: 'training_completed',
          data: { trainingId: training.id, enrollmentId: enrollment.id },
        },
      });
    }
  }

  private async notifyEnrollmentDeletion(enrollmentId: string, userId: string, trainingId: string) {
    // Send message to RabbitMQ for event processing
    await this.rabbitMQService.publish({
      type: 'training_enrollment_deleted',
      data: { id: enrollmentId, userId, trainingId },
    });

    // Get training details
    const training = await this.prisma.training.findUnique({
      where: { id: trainingId },
    });

    // Notify user via WebSocket
    if (training) {
      this.websocketService.sendToUser(userId, 'training_enrollment_deleted', {
        message: `Your enrollment in training: ${training.title} has been removed`,
        enrollmentId,
        trainingId,
      });

      // Notify instructor via WebSocket
      this.websocketService.sendToUser(training.instructorId, 'training_enrollment_deleted', {
        message: `A user's enrollment in your training has been removed`,
        enrollmentId,
        userId,
        trainingId,
      });
    }
  }
}
