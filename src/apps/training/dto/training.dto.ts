import { ApiProperty } from '@nestjs/swagger';
import { IsDate, IsEnum, IsNumber, IsOptional, IsString, IsUUID } from 'class-validator';

export enum TrainingContentType {
  VIDEO = 'video',
  DOCUMENT = 'document',
  INTERACTIVE = 'interactive',
  LIVE = 'live',
}

export enum TrainingAccessLevel {
  FREE = 'free',
  PREMIUM = 'premium',
  EXCLUSIVE = 'exclusive',
}

export class TrainingEnrollmentDto {
  @ApiProperty({
    description: 'Unique identifier of the enrollment',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @IsUUID()
  id: string;

  @ApiProperty({
    description: 'User ID who enrolled in the training',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @IsUUID()
  userId: string;

  @ApiProperty({
    description: 'Training ID the user enrolled in',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @IsUUID()
  trainingId: string;

  @ApiProperty({
    description: 'Date when the user enrolled',
    type: Date,
  })
  @IsDate()
  enrollmentDate: Date;

  @ApiProperty({
    description: 'Date when the user completed the training',
    type: Date,
    nullable: true,
  })
  @IsOptional()
  @IsDate()
  completionDate?: Date;

  @ApiProperty({
    description: 'Current status of the enrollment',
    example: 'in_progress',
    enum: ['not_started', 'in_progress', 'completed', 'cancelled'],
  })
  @IsString()
  status: string;

  @ApiProperty({
    description: 'Progress percentage (0-100)',
    example: 75,
  })
  @IsNumber()
  progress: number;

  @ApiProperty({
    description: 'Creation timestamp',
    type: Date,
  })
  createdAt: Date;

  @ApiProperty({
    description: 'Last update timestamp',
    type: Date,
  })
  updatedAt: Date;
}

export class TrainingDto {
  @ApiProperty({
    description: 'Unique identifier of the training',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @IsUUID()
  id: string;

  @ApiProperty({
    description: 'Title of the training',
    example: 'Advanced Tailoring Techniques',
  })
  @IsString()
  title: string;

  @ApiProperty({
    description: 'Detailed description of the training',
    example: 'Learn advanced techniques for tailoring formal wear and special occasion outfits.',
  })
  @IsString()
  description: string;

  @ApiProperty({
    description: 'ID of the instructor providing the training',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @IsUUID()
  instructorId: string;

  @ApiProperty({
    description: 'Type of content provided in the training',
    enum: TrainingContentType,
    example: TrainingContentType.VIDEO,
  })
  @IsEnum(TrainingContentType)
  contentType: string;

  @ApiProperty({
    description: 'URL to access the training content',
    example: 'https://example.com/trainings/advanced-tailoring',
  })
  @IsString()
  contentUrl: string;

  @ApiProperty({
    description: 'Price of the training in the system currency',
    example: '99.99',
  })
  @IsString()
  price: string;

  @ApiProperty({
    description: 'Access level required for the training',
    enum: TrainingAccessLevel,
    example: TrainingAccessLevel.PREMIUM,
  })
  @IsEnum(TrainingAccessLevel)
  accessLevel: string;

  @ApiProperty({
    description: 'Duration of the training in minutes',
    example: 120,
  })
  @IsNumber()
  duration: number;

  @ApiProperty({
    description: 'List of enrollments for this training',
    type: [TrainingEnrollmentDto],
    isArray: true,
  })
  @IsOptional()
  enrollments?: TrainingEnrollmentDto[];

  @ApiProperty({
    description: 'Creation timestamp',
    type: Date,
  })
  createdAt: Date;

  @ApiProperty({
    description: 'Last update timestamp',
    type: Date,
  })
  updatedAt: Date;
}
