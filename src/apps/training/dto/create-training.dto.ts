import { InputType, Field, Int } from '@nestjs/graphql';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsNotEmpty, IsString, IsOptional, IsNumber, Min, IsUUID, IsEnum, IsUrl } from 'class-validator';

enum ContentType {
  VIDEO = 'video',
  PDF = 'pdf',
  DOCUMENT = 'document',
  AUDIO = 'audio',
  OTHER = 'other'
}

enum AccessLevel {
  FREE = 'free',
  PREMIUM = 'premium'
}

@InputType()
export class CreateTrainingDto {
  @ApiProperty({
    description: 'Title of the training',
    example: 'Advanced Tailoring Techniques'
  })
  @Field()
  @IsString()
  @IsNotEmpty({ message: 'Title is required' })
  title: string;

  @ApiProperty({
    description: 'Description of the training',
    example: 'Learn advanced tailoring techniques for formal wear.'
  })
  @Field()
  @IsString()
  @IsNotEmpty({ message: 'Description is required' })
  description: string;

  @ApiProperty({
    description: 'Instructor user ID',
    example: '123e4567-e89b-12d3-a456-************'
  })
  @Field()
  @IsUUID()
  @IsNotEmpty({ message: 'Instructor ID is required' })
  instructorId: string;

  @ApiProperty({
    description: 'Content type (video, pdf, document, audio, other)',
    example: 'video',
    enum: ContentType
  })
  @Field()
  @IsEnum(ContentType, { message: 'Content type must be one of: video, pdf, document, audio, other' })
  @IsNotEmpty({ message: 'Content type is required' })
  contentType: string;

  @ApiProperty({
    description: 'Content URL',
    example: 'https://example.com/training/video.mp4'
  })
  @Field()
  @IsUrl({}, { message: 'Content URL must be a valid URL' })
  @IsNotEmpty({ message: 'Content URL is required' })
  contentUrl: string;

  @ApiPropertyOptional({
    description: 'Price of the training',
    example: '29.99'
  })
  @Field({ nullable: true })
  @IsString()
  @IsOptional()
  price?: string;

  @ApiPropertyOptional({
    description: 'Access level (free, premium)',
    example: 'premium',
    enum: AccessLevel,
    default: 'free'
  })
  @Field({ nullable: true, defaultValue: 'free' })
  @IsEnum(AccessLevel, { message: 'Access level must be one of: free, premium' })
  @IsOptional()
  accessLevel?: string;

  @ApiPropertyOptional({
    description: 'Duration in minutes',
    example: 60,
    minimum: 1
  })
  @Field(() => Int, { nullable: true })
  @IsNumber()
  @Min(1, { message: 'Duration must be at least 1 minute' })
  @IsOptional()
  duration?: number;
}
