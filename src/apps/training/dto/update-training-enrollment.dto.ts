import { InputType, Field, Int, PartialType } from '@nestjs/graphql';
import { ApiPropertyOptional } from '@nestjs/swagger';
import { IsOptional, IsInt, Min, Max, IsDate } from 'class-validator';
import { Type } from 'class-transformer';
import { CreateTrainingEnrollmentDto } from './create-training-enrollment.dto';

@InputType()
export class UpdateTrainingEnrollmentDto extends PartialType(CreateTrainingEnrollmentDto) {
  @ApiPropertyOptional({
    description: 'Progress percentage (0-100)',
    example: 50,
    minimum: 0,
    maximum: 100
  })
  @Field(() => Int, { nullable: true })
  @IsInt()
  @Min(0, { message: 'Progress must be at least 0' })
  @Max(100, { message: 'Progress cannot exceed 100' })
  @IsOptional()
  progress?: number;

  @ApiPropertyOptional({
    description: 'Completion timestamp',
    example: '2025-05-22T12:00:00Z'
  })
  @Field(() => Date, { nullable: true })
  @IsDate()
  @Type(() => Date)
  @IsOptional()
  completedAt?: Date;
}
