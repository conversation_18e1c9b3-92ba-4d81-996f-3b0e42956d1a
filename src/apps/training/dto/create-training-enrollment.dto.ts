import { InputType, Field, Int } from '@nestjs/graphql';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsNotEmpty, IsUUID, IsOptional, IsInt, Min, Max } from 'class-validator';

@InputType()
export class CreateTrainingEnrollmentDto {
  @ApiProperty({
    description: 'User ID associated with this enrollment',
    example: '123e4567-e89b-12d3-a456-************'
  })
  @Field()
  @IsUUID()
  @IsNotEmpty({ message: 'User ID is required' })
  userId: string;

  @ApiProperty({
    description: 'Training ID associated with this enrollment',
    example: '123e4567-e89b-12d3-a456-************'
  })
  @Field()
  @IsUUID()
  @IsNotEmpty({ message: 'Training ID is required' })
  trainingId: string;

  @ApiPropertyOptional({
    description: 'Progress percentage (0-100)',
    example: 0,
    minimum: 0,
    maximum: 100,
    default: 0
  })
  @Field(() => Int, { nullable: true, defaultValue: 0 })
  @IsInt()
  @Min(0, { message: 'Progress must be at least 0' })
  @Max(100, { message: 'Progress cannot exceed 100' })
  @IsOptional()
  progress?: number;
}
