import { InputType, Field, Int, PartialType } from '@nestjs/graphql';
import { ApiPropertyOptional } from '@nestjs/swagger';
import { IsString, IsOptional, IsNumber, Min, IsEnum, IsUrl } from 'class-validator';
import { CreateTrainingDto } from './create-training.dto';

enum ContentType {
  VIDEO = 'video',
  PDF = 'pdf',
  DOCUMENT = 'document',
  AUDIO = 'audio',
  OTHER = 'other'
}

enum AccessLevel {
  FREE = 'free',
  PREMIUM = 'premium'
}

@InputType()
export class UpdateTrainingDto extends PartialType(CreateTrainingDto) {
  @ApiPropertyOptional({
    description: 'Title of the training',
    example: 'Advanced Tailoring Techniques'
  })
  @Field({ nullable: true })
  @IsString()
  @IsOptional()
  title?: string;

  @ApiPropertyOptional({
    description: 'Description of the training',
    example: 'Learn advanced tailoring techniques for formal wear.'
  })
  @Field({ nullable: true })
  @IsString()
  @IsOptional()
  description?: string;

  @ApiPropertyOptional({
    description: 'Content type (video, pdf, document, audio, other)',
    example: 'video',
    enum: ContentType
  })
  @Field({ nullable: true })
  @IsEnum(ContentType, { message: 'Content type must be one of: video, pdf, document, audio, other' })
  @IsOptional()
  contentType?: string;

  @ApiPropertyOptional({
    description: 'Content URL',
    example: 'https://example.com/training/video.mp4'
  })
  @Field({ nullable: true })
  @IsUrl({}, { message: 'Content URL must be a valid URL' })
  @IsOptional()
  contentUrl?: string;

  @ApiPropertyOptional({
    description: 'Price of the training',
    example: '29.99'
  })
  @Field({ nullable: true })
  @IsString()
  @IsOptional()
  price?: string;

  @ApiPropertyOptional({
    description: 'Access level (free, premium)',
    example: 'premium',
    enum: AccessLevel
  })
  @Field({ nullable: true })
  @IsEnum(AccessLevel, { message: 'Access level must be one of: free, premium' })
  @IsOptional()
  accessLevel?: string;

  @ApiPropertyOptional({
    description: 'Duration in minutes',
    example: 60,
    minimum: 1
  })
  @Field(() => Int, { nullable: true })
  @IsNumber()
  @Min(1, { message: 'Duration must be at least 1 minute' })
  @IsOptional()
  duration?: number;
}
