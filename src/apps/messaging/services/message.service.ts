import { Injectable, NotFoundException } from '@nestjs/common';
import { PrismaService } from '@core/prisma/prisma.service';
import { MongoDbService } from '@infra/mongodb/mongodb.service';
import { WebsocketService } from '@infra/websocket/websocket.service';
import { RabbitMQService } from '@infra/rabbitmq/rabbitmq.service';
import { CreateMessageDto } from '../dto/create-message.dto';
import { UpdateMessageDto } from '../dto/update-message.dto';
import { StandardListParams } from '@shared/utils/query-params.util';

@Injectable()
export class MessageService {
  constructor(
    private readonly prisma: PrismaService,
    private readonly mongoDbService: MongoDbService,
    private readonly websocketService: WebsocketService,
    private readonly rabbitMQService: RabbitMQService,
  ) {}

  async create(createMessageDto: CreateMessageDto) {
    // Check if sender exists
    const sender = await this.prisma.user.findUnique({
      where: { id: createMessageDto.senderId },
    });

    if (!sender) {
      throw new NotFoundException(`Sender with ID ${createMessageDto.senderId} not found`);
    }

    // Check if receiver exists
    const receiver = await this.prisma.user.findUnique({
      where: { id: createMessageDto.receiverId },
    });

    if (!receiver) {
      throw new NotFoundException(`Receiver with ID ${createMessageDto.receiverId} not found`);
    }

    // Check if order exists if orderId is provided
    if (createMessageDto.orderId) {
      const order = await this.prisma.order.findUnique({
        where: { id: createMessageDto.orderId },
      });

      if (!order) {
        throw new NotFoundException(`Order with ID ${createMessageDto.orderId} not found`);
      }
    }

    // Create message in PostgreSQL
    const newMessage = await this.prisma.message.create({
      data: {
        orderId: createMessageDto.orderId,
        senderId: createMessageDto.senderId,
        receiverId: createMessageDto.receiverId,
        content: createMessageDto.content,
        isRead: false,
      },
    });

    // Sync to MongoDB for fast reads
    await this.syncMessageToMongo(newMessage);

    // Notify about new message
    this.notifyNewMessage(newMessage);

    return newMessage;
  }

  async findAll(params?: StandardListParams) {
    // Read from MongoDB for better performance
    const filter = params?.where || {};
    const options: Record<string, any> = {};
    
    // Apply pagination
    if (params?.page && params?.limit) {
      options.skip = (params.page - 1) * params.limit;
      options.limit = params.limit;
    } else if (params?.limit) {
      options.limit = params.limit;
    }
    
    // Apply sorting (default to createdAt desc for messages)
    if (params?.order) {
      options.sort = {};
      for (const [field, direction] of Object.entries(params.order)) {
        options.sort[field] = direction === 'asc' ? 1 : -1;
      }
    } else {
      options.sort = { createdAt: -1 };
    }

    // Apply search if provided
    if (params?.search) {
      // Text search in content
      filter.content = { $regex: params.search, $options: 'i' };
    }
    
    // Get total count for pagination metadata
    const totalCount = await this.mongoDbService.count('messages', filter);
    
    // Get messages with filters and options
    const messages = await this.mongoDbService.find('messages', filter, options);
    
    // Generate pagination metadata
    const page = params?.page || 1;
    const limit = params?.limit || messages.length;
    const totalPages = Math.ceil(totalCount / limit);
    
    return {
      data: messages,
      pagination: {
        page,
        limit,
        totalPages,
        totalCount,
        hasNextPage: page < totalPages,
        hasPreviousPage: page > 1,
      }
    };
  }

  async findOne(id: string) {
    // Read from MongoDB for better performance
    const message = await this.mongoDbService.findOne('messages', { id });
    
    if (!message) {
      throw new NotFoundException(`Message with ID ${id} not found`);
    }
    
    return message;
  }

  async findByOrderId(orderId: string, params?: StandardListParams) {
    // Read from MongoDB for better performance
    const filter = { orderId, ...(params?.where || {}) };
    const options: Record<string, any> = {};
    
    // Apply pagination
    if (params?.page && params?.limit) {
      options.skip = (params.page - 1) * params.limit;
      options.limit = params.limit;
    } else if (params?.limit) {
      options.limit = params.limit;
    }
    
    // Apply sorting (default to createdAt asc for conversation flow)
    if (params?.order) {
      options.sort = {};
      for (const [field, direction] of Object.entries(params.order)) {
        options.sort[field] = direction === 'asc' ? 1 : -1;
      }
    } else {
      options.sort = { createdAt: 1 };
    }
    
    // Get total count for pagination metadata
    const totalCount = await this.mongoDbService.count('messages', filter);
    
    // Get messages with filters and options
    const messages = await this.mongoDbService.find('messages', filter, options);
    
    // Generate pagination metadata
    const page = params?.page || 1;
    const limit = params?.limit || messages.length;
    const totalPages = Math.ceil(totalCount / limit);
    
    return {
      data: messages,
      pagination: {
        page,
        limit,
        totalPages,
        totalCount,
        hasNextPage: page < totalPages,
        hasPreviousPage: page > 1,
      }
    };
  }

  async findConversation(userId1: string, userId2: string, params?: StandardListParams) {
    // Read from MongoDB for better performance
    const filter = {
      $or: [
        { senderId: userId1, receiverId: userId2 },
        { senderId: userId2, receiverId: userId1 }
      ],
      ...(params?.where || {})
    };
    const options: Record<string, any> = {};
    
    // Apply pagination
    if (params?.page && params?.limit) {
      options.skip = (params.page - 1) * params.limit;
      options.limit = params.limit;
    } else if (params?.limit) {
      options.limit = params.limit;
    }
    
    // Apply sorting (default to createdAt asc for conversation flow)
    if (params?.order) {
      options.sort = {};
      for (const [field, direction] of Object.entries(params.order)) {
        options.sort[field] = direction === 'asc' ? 1 : -1;
      }
    } else {
      options.sort = { createdAt: 1 };
    }
    
    // Get total count for pagination metadata
    const totalCount = await this.mongoDbService.count('messages', filter);
    
    // Get messages with filters and options
    const messages = await this.mongoDbService.find('messages', filter, options);
    
    // Generate pagination metadata
    const page = params?.page || 1;
    const limit = params?.limit || messages.length;
    const totalPages = Math.ceil(totalCount / limit);
    
    return {
      data: messages,
      pagination: {
        page,
        limit,
        totalPages,
        totalCount,
        hasNextPage: page < totalPages,
        hasPreviousPage: page > 1,
      }
    };
  }

  async findUserMessages(userId: string, params?: StandardListParams) {
    // Read from MongoDB for better performance
    const filter = {
      $or: [
        { senderId: userId },
        { receiverId: userId }
      ],
      ...(params?.where || {})
    };
    const options: Record<string, any> = {};
    
    // Apply pagination
    if (params?.page && params?.limit) {
      options.skip = (params.page - 1) * params.limit;
      options.limit = params.limit;
    } else if (params?.limit) {
      options.limit = params.limit;
    }
    
    // Apply sorting (default to createdAt desc for messages)
    if (params?.order) {
      options.sort = {};
      for (const [field, direction] of Object.entries(params.order)) {
        options.sort[field] = direction === 'asc' ? 1 : -1;
      }
    } else {
      options.sort = { createdAt: -1 };
    }
    
    // Get total count for pagination metadata
    const totalCount = await this.mongoDbService.count('messages', filter);
    
    // Get messages with filters and options
    const messages = await this.mongoDbService.find('messages', filter, options);
    
    // Generate pagination metadata
    const page = params?.page || 1;
    const limit = params?.limit || messages.length;
    const totalPages = Math.ceil(totalCount / limit);
    
    return {
      data: messages,
      pagination: {
        page,
        limit,
        totalPages,
        totalCount,
        hasNextPage: page < totalPages,
        hasPreviousPage: page > 1,
      }
    };
  }

  async update(id: string, updateMessageDto: UpdateMessageDto) {
    // Check if message exists
    const message = await this.prisma.message.findUnique({
      where: { id },
    });
    
    if (!message) {
      throw new NotFoundException(`Message with ID ${id} not found`);
    }
    
    // Update message in PostgreSQL
    const updatedMessage = await this.prisma.message.update({
      where: { id },
      data: {
        content: updateMessageDto.content !== undefined ? updateMessageDto.content : message.content,
        isRead: updateMessageDto.isRead !== undefined ? updateMessageDto.isRead : message.isRead,
      },
    });
    
    // Sync to MongoDB for fast reads
    await this.syncMessageToMongo(updatedMessage);
    
    // Notify about message update
    this.notifyMessageUpdate(updatedMessage);
    
    return updatedMessage;
  }

  async markAsRead(id: string) {
    // Check if message exists
    const message = await this.prisma.message.findUnique({
      where: { id },
    });
    
    if (!message) {
      throw new NotFoundException(`Message with ID ${id} not found`);
    }
    
    // If already read, return the message
    if (message.isRead) {
      return message;
    }
    
    // Update message in PostgreSQL
    const updatedMessage = await this.prisma.message.update({
      where: { id },
      data: {
        isRead: true,
      },
    });
    
    // Sync to MongoDB for fast reads
    await this.syncMessageToMongo(updatedMessage);
    
    // Notify about message read status
    this.notifyMessageRead(updatedMessage);
    
    return updatedMessage;
  }

  async remove(id: string) {
    // Check if message exists
    const message = await this.prisma.message.findUnique({
      where: { id },
    });
    
    if (!message) {
      throw new NotFoundException(`Message with ID ${id} not found`);
    }
    
    // Delete message from PostgreSQL
    await this.prisma.message.delete({
      where: { id },
    });
    
    // Delete from MongoDB
    await this.mongoDbService.deleteDocument('messages', id);
    
    // Notify about message deletion
    this.notifyMessageDeletion(id, message.senderId, message.receiverId);
    
    return { id };
  }

  // Helper methods for MongoDB sync and notifications
  private async syncMessageToMongo(message: any) {
    await this.mongoDbService.syncDocument('messages', message);
  }

  private async notifyNewMessage(message: any) {
    // Send message to RabbitMQ for event processing
    await this.rabbitMQService.publish({
      type: 'message_created',
      data: message,
    });

    // Notify receiver via WebSocket
    this.websocketService.sendToUser(message.receiverId, 'new_message', {
      message: 'You have received a new message',
      data: message,
    });

    // Create notification in database
    await this.prisma.notification.create({
      data: {
        userId: message.receiverId,
        title: 'New Message',
        body: `You have a new message${message.orderId ? ' related to an order' : ''}`,
        isRead: false,
        type: 'message',
        data: { messageId: message.id, senderId: message.senderId },
      },
    });
  }

  private async notifyMessageUpdate(message: any) {
    // Send message to RabbitMQ for event processing
    await this.rabbitMQService.publish({
      type: 'message_updated',
      data: message,
    });

    // Notify users via WebSocket
    this.websocketService.sendToUser(message.senderId, 'message_updated', {
      message: 'Message has been updated',
      data: message,
    });
    
    this.websocketService.sendToUser(message.receiverId, 'message_updated', {
      message: 'Message has been updated',
      data: message,
    });
  }

  private async notifyMessageRead(message: any) {
    // Send message to RabbitMQ for event processing
    await this.rabbitMQService.publish({
      type: 'message_read',
      data: message,
    });

    // Notify sender that their message has been read
    this.websocketService.sendToUser(message.senderId, 'message_read', {
      message: 'Your message has been read',
      data: message,
    });
  }

  private async notifyMessageDeletion(messageId: string, senderId: string, receiverId: string) {
    // Send message to RabbitMQ for event processing
    await this.rabbitMQService.publish({
      type: 'message_deleted',
      data: { id: messageId, senderId, receiverId },
    });

    // Notify users via WebSocket
    this.websocketService.sendToUser(senderId, 'message_deleted', {
      message: `Message with ID ${messageId} has been deleted`,
      messageId,
    });
    
    this.websocketService.sendToUser(receiverId, 'message_deleted', {
      message: `Message with ID ${messageId} has been deleted`,
      messageId,
    });
  }
}
