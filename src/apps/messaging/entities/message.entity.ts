import { ObjectType, Field, ID } from '@nestjs/graphql';
import { ApiProperty } from '@nestjs/swagger';
import { User } from '@apps/user/entities/user.entity';
import { Order } from '@apps/order/entities/order.entity';

@ObjectType()
export class Message {
  @ApiProperty({ description: 'Unique identifier for the message' })
  @Field(() => ID)
  id: string;

  @ApiProperty({ description: 'Order ID associated with this message', nullable: true })
  @Field(() => ID, { nullable: true })
  orderId?: string;

  @ApiProperty({ description: 'Order associated with this message', nullable: true })
  @Field(() => Order, { nullable: true })
  order?: Order;

  @ApiProperty({ description: 'Sender user ID' })
  @Field(() => ID)
  senderId: string;

  @ApiProperty({ description: 'Sender user' })
  @Field(() => User)
  sender: User;

  @ApiProperty({ description: 'Receiver user ID' })
  @Field(() => ID)
  receiverId: string;

  @ApiProperty({ description: 'Receiver user' })
  @Field(() => User)
  receiver: User;

  @ApiProperty({ description: 'Message content' })
  @Field()
  content: string;

  @ApiProperty({ description: 'Read status' })
  @Field(() => Boolean)
  isRead: boolean;

  @ApiProperty({ description: 'Creation timestamp' })
  @Field(() => Date)
  createdAt: Date;

  @ApiProperty({ description: 'Last update timestamp' })
  @Field(() => Date)
  updatedAt: Date;
}
