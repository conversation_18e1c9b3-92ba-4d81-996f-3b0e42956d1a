import { InputType, Field, PartialType } from '@nestjs/graphql';
import { ApiPropertyOptional } from '@nestjs/swagger';
import { IsString, IsOptional, IsBoolean } from 'class-validator';
import { CreateMessageDto } from './create-message.dto';

@InputType()
export class UpdateMessageDto extends PartialType(CreateMessageDto) {
  @ApiPropertyOptional({
    description: 'Message content',
    example: 'Updated message content'
  })
  @Field({ nullable: true })
  @IsString()
  @IsOptional()
  content?: string;

  @ApiPropertyOptional({
    description: 'Read status',
    example: true
  })
  @Field(() => Boolean, { nullable: true })
  @IsBoolean()
  @IsOptional()
  isRead?: boolean;
}
