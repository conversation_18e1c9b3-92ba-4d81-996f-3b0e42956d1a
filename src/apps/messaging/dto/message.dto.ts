import { ApiProperty } from '@nestjs/swagger';
import { IsDate, IsEnum, IsOptional, IsString, IsUUID } from 'class-validator';

enum MessageStatus {
  SENT = 'SENT',
  DELIVERED = 'DELIVERED',
  READ = 'READ',
}

enum MessageType {
  TEXT = 'TEXT',
  IMAGE = 'IMAGE',
  FILE = 'FILE',
  SYSTEM = 'SYSTEM',
}

export class MessageDto {
  @ApiProperty({
    description: 'Unique identifier of the message',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @IsUUID()
  id: string;

  @ApiProperty({
    description: 'User ID of the sender',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @IsUUID()
  senderId: string;

  @ApiProperty({
    description: 'User ID of the recipient',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @IsUUID()
  recipientId: string;

  @ApiProperty({
    description: 'Content of the message',
    example: 'Hello, how are you doing today?',
  })
  @IsString()
  content: string;

  @ApiProperty({
    description: 'Type of the message',
    enum: MessageType,
    example: MessageType.TEXT,
  })
  @IsEnum(MessageType)
  type: MessageType;

  @ApiProperty({
    description: 'Status of the message',
    enum: MessageStatus,
    example: MessageStatus.SENT,
  })
  @IsEnum(MessageStatus)
  status: MessageStatus;

  @ApiProperty({
    description: 'Date when the message was sent',
    example: '2023-01-15T08:30:00Z',
  })
  @IsDate()
  sentAt: Date;

  @ApiProperty({
    description: 'Date when the message was delivered',
    example: '2023-01-15T08:30:05Z',
    required: false,
  })
  @IsDate()
  @IsOptional()
  deliveredAt?: Date;

  @ApiProperty({
    description: 'Date when the message was read',
    example: '2023-01-15T08:31:00Z',
    required: false,
  })
  @IsDate()
  @IsOptional()
  readAt?: Date;

  @ApiProperty({
    description: 'Optional URL to attached file or image',
    example: 'https://example.com/files/image.jpg',
    required: false,
  })
  @IsString()
  @IsOptional()
  attachmentUrl?: string;

  @ApiProperty({
    description: 'Date when the message was created',
    example: '2023-01-15T08:30:00Z',
  })
  @IsDate()
  createdAt: Date;

  @ApiProperty({
    description: 'Date when the message was last updated',
    example: '2023-01-15T08:31:00Z',
  })
  @IsDate()
  updatedAt: Date;
}
