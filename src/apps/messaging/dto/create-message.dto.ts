import { InputType, Field, ID } from '@nestjs/graphql';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsNotEmpty, IsString, IsOptional, IsUUID } from 'class-validator';

@InputType()
export class CreateMessageDto {
  @ApiPropertyOptional({
    description: 'Order ID associated with this message',
    example: '123e4567-e89b-12d3-a456-************'
  })
  @Field(() => ID, { nullable: true })
  @IsUUID()
  @IsOptional()
  orderId?: string;

  @ApiProperty({
    description: 'Sender user ID',
    example: '123e4567-e89b-12d3-a456-************'
  })
  @Field(() => ID)
  @IsUUID()
  @IsNotEmpty({ message: 'Sender ID is required' })
  senderId: string;

  @ApiProperty({
    description: 'Receiver user ID',
    example: '123e4567-e89b-12d3-a456-************'
  })
  @Field(() => ID)
  @IsUUID()
  @IsNotEmpty({ message: 'Receiver ID is required' })
  receiverId: string;

  @ApiProperty({
    description: 'Message content',
    example: 'Hello, I would like to inquire about your tailoring services.'
  })
  @Field()
  @IsString()
  @IsNotEmpty({ message: 'Message content is required' })
  content: string;
}
