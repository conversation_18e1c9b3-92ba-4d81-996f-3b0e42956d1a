import { Resolver, Query, Mutation, Args, ID } from '@nestjs/graphql';
import { MessageService } from '../services/message.service';
import { Message } from '../entities/message.entity';
import { CreateMessageDto } from '../dto/create-message.dto';
import { UpdateMessageDto } from '../dto/update-message.dto';
import { UseGuards } from '@nestjs/common';
import { JwtAuthGuard } from '@core/auth/guards/jwt-auth.guard';
import { PaginatedResponse } from '@shared/interfaces/paginated-response.interface';
import { ListParamsArgs } from '@shared/dto/list-params.args';

// Create a paginated response type for Message
const PaginatedMessageResponse = PaginatedResponse(Message);

@Resolver(() => Message)
export class MessageResolver {
  constructor(private readonly messageService: MessageService) {}

  @Mutation(() => Message)
  @UseGuards(JwtAuthGuard)
  createMessage(
    @Args('createMessageDto') createMessageDto: CreateMessageDto,
  ) {
    return this.messageService.create(createMessageDto);
  }

  @Query(() => PaginatedMessageResponse, { name: 'messages' })
  @UseGuards(JwtAuthGuard)
  findAll(@Args() params: ListParamsArgs) {
    return this.messageService.findAll(params);
  }

  @Query(() => Message, { name: 'message' })
  @UseGuards(JwtAuthGuard)
  findOne(@Args('id', { type: () => ID }) id: string) {
    return this.messageService.findOne(id);
  }

  @Query(() => PaginatedMessageResponse, { name: 'messagesByOrderId' })
  @UseGuards(JwtAuthGuard)
  findByOrderId(
    @Args('orderId', { type: () => ID }) orderId: string,
    @Args() params: ListParamsArgs,
  ) {
    return this.messageService.findByOrderId(orderId, params);
  }

  @Query(() => PaginatedMessageResponse, { name: 'conversationMessages' })
  @UseGuards(JwtAuthGuard)
  findConversation(
    @Args('userId1', { type: () => ID }) userId1: string,
    @Args('userId2', { type: () => ID }) userId2: string,
    @Args() params: ListParamsArgs,
  ) {
    return this.messageService.findConversation(userId1, userId2, params);
  }

  @Query(() => PaginatedMessageResponse, { name: 'userMessages' })
  @UseGuards(JwtAuthGuard)
  findUserMessages(
    @Args('userId', { type: () => ID }) userId: string,
    @Args() params: ListParamsArgs,
  ) {
    return this.messageService.findUserMessages(userId, params);
  }

  @Mutation(() => Message)
  @UseGuards(JwtAuthGuard)
  updateMessage(
    @Args('id', { type: () => ID }) id: string,
    @Args('updateMessageDto') updateMessageDto: UpdateMessageDto,
  ) {
    return this.messageService.update(id, updateMessageDto);
  }

  @Mutation(() => Message)
  @UseGuards(JwtAuthGuard)
  markMessageAsRead(@Args('id', { type: () => ID }) id: string) {
    return this.messageService.markAsRead(id);
  }

  @Mutation(() => Message)
  @UseGuards(JwtAuthGuard)
  removeMessage(@Args('id', { type: () => ID }) id: string) {
    return this.messageService.remove(id);
  }
}
