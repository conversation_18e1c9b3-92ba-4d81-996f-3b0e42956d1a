import { Modu<PERSON> } from '@nestjs/common';
import { MessageService } from './services/message.service';
import { MessageController } from './controllers/message.controller';
import { MessageResolver } from './resolvers/message.resolver';
import { CoreModule } from '@core/core.module';
import { MongoDbModule } from '@infra/mongodb/mongodb.module';
import { WebsocketModule } from '@infra/websocket/websocket.module';
import { RabbitMQModule } from '@infra/rabbitmq/rabbitmq.module';

@Module({
  imports: [
    CoreModule,
    MongoDbModule,
    WebsocketModule,
    RabbitMQModule,
  ],
  providers: [MessageService, MessageResolver],
  controllers: [MessageController],
  exports: [MessageService],
})
export class MessagingModule {}
