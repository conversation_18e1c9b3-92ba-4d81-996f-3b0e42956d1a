import { Controller, Get, Post, Body, Patch, Param, Delete, UseGuards, Query } from '@nestjs/common';
import { TailorService } from '../services/tailor.service';
import { CreateTailorProfileDto } from '../dto/create-tailor-profile.dto';
import { UpdateTailorProfileDto } from '../dto/update-tailor-profile.dto';
import { JwtAuthGuard } from '@core/auth/guards/jwt-auth.guard';
import { RolesGuard } from '@core/auth/guards/roles.guard';
import { Roles } from '@core/auth/decorators/roles.decorator';
import { ApiTags, ApiOperation, ApiResponse, ApiParam, ApiQuery, ApiBearerAuth } from '@nestjs/swagger';
import { StandardListParams } from '@shared/utils/query-params.util';

@ApiTags('tailor-profiles')
@Controller('tailor-profiles')
export class TailorController {
  constructor(private readonly tailorService: TailorService) {}

  @Post()
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles('ADMIN', 'TAILOR')
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Create a new tailor profile' })
  @ApiResponse({ status: 201, description: 'The tailor profile has been successfully created.' })
  @ApiResponse({ status: 400, description: 'Invalid input data.' })
  @ApiResponse({ status: 401, description: 'Unauthorized.' })
  @ApiResponse({ status: 403, description: 'Forbidden.' })
  @ApiResponse({ status: 404, description: 'User not found.' })
  @ApiResponse({ status: 409, description: 'Tailor profile already exists for this user.' })
  create(@Body() createTailorProfileDto: CreateTailorProfileDto) {
    return this.tailorService.createProfile(createTailorProfileDto);
  }

  @Get()
  @ApiOperation({ summary: 'Get all tailor profiles' })
  @ApiResponse({ status: 200, description: 'Return all tailor profiles.' })
  @ApiQuery({ name: 'page', required: false, type: Number, description: 'Page number' })
  @ApiQuery({ name: 'limit', required: false, type: Number, description: 'Items per page' })
  @ApiQuery({ name: 'search', required: false, type: String, description: 'Search term' })
  @ApiQuery({ name: 'order', required: false, type: Object, description: 'Sort order' })
  findAll(@Query() params: StandardListParams) {
    return this.tailorService.findAllProfiles(params);
  }

  @Get(':id')
  @ApiOperation({ summary: 'Get a tailor profile by ID' })
  @ApiResponse({ status: 200, description: 'Return the tailor profile.' })
  @ApiResponse({ status: 404, description: 'Tailor profile not found.' })
  @ApiParam({ name: 'id', type: 'string', description: 'Tailor profile ID' })
  findOne(@Param('id') id: string) {
    return this.tailorService.findProfileById(id);
  }

  @Get('user/:userId')
  @ApiOperation({ summary: 'Get a tailor profile by user ID' })
  @ApiResponse({ status: 200, description: 'Return the tailor profile.' })
  @ApiResponse({ status: 404, description: 'Tailor profile not found.' })
  @ApiParam({ name: 'userId', type: 'string', description: 'User ID' })
  findByUserId(@Param('userId') userId: string) {
    return this.tailorService.findProfileByUserId(userId);
  }

  @Patch(':id')
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles('ADMIN', 'TAILOR')
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Update a tailor profile' })
  @ApiResponse({ status: 200, description: 'The tailor profile has been successfully updated.' })
  @ApiResponse({ status: 400, description: 'Invalid input data.' })
  @ApiResponse({ status: 401, description: 'Unauthorized.' })
  @ApiResponse({ status: 403, description: 'Forbidden.' })
  @ApiResponse({ status: 404, description: 'Tailor profile not found.' })
  @ApiParam({ name: 'id', type: 'string', description: 'Tailor profile ID' })
  update(@Param('id') id: string, @Body() updateTailorProfileDto: UpdateTailorProfileDto) {
    return this.tailorService.updateProfile(id, updateTailorProfileDto);
  }

  @Delete(':id')
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles('ADMIN', 'TAILOR')
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Delete a tailor profile' })
  @ApiResponse({ status: 200, description: 'The tailor profile has been successfully deleted.' })
  @ApiResponse({ status: 401, description: 'Unauthorized.' })
  @ApiResponse({ status: 403, description: 'Forbidden.' })
  @ApiResponse({ status: 404, description: 'Tailor profile not found.' })
  @ApiParam({ name: 'id', type: 'string', description: 'Tailor profile ID' })
  remove(@Param('id') id: string) {
    return this.tailorService.removeProfile(id);
  }
}
