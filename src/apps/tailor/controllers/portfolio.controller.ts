import { Controller, Get, Post, Body, Patch, Param, Delete, UseGuards, Query } from '@nestjs/common';
import { PortfolioService } from '../services/portfolio.service';
import { CreatePortfolioItemDto } from '../dto/create-portfolio-item.dto';
import { UpdatePortfolioItemDto } from '../dto/update-portfolio-item.dto';
import { JwtAuthGuard } from '@core/auth/guards/jwt-auth.guard';
import { RolesGuard } from '@core/auth/guards/roles.guard';
import { Roles } from '@core/auth/decorators/roles.decorator';
import { ApiTags, ApiOperation, ApiResponse, ApiParam, ApiQuery, ApiBearerAuth } from '@nestjs/swagger';
import { StandardListParams } from '@shared/utils/query-params.util';

@ApiTags('portfolio')
@Controller('portfolio')
export class PortfolioController {
  constructor(private readonly portfolioService: PortfolioService) {}

  @Post()
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles('ADMIN', 'TAILOR')
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Create a new portfolio item' })
  @ApiResponse({ status: 201, description: 'The portfolio item has been successfully created.' })
  @ApiResponse({ status: 400, description: 'Invalid input data.' })
  @ApiResponse({ status: 401, description: 'Unauthorized.' })
  @ApiResponse({ status: 403, description: 'Forbidden.' })
  @ApiResponse({ status: 404, description: 'User not found.' })
  create(@Body() createPortfolioItemDto: CreatePortfolioItemDto) {
    return this.portfolioService.create(createPortfolioItemDto);
  }

  @Get()
  @ApiOperation({ summary: 'Get all portfolio items' })
  @ApiResponse({ status: 200, description: 'Return all portfolio items.' })
  @ApiQuery({ name: 'page', required: false, type: Number, description: 'Page number' })
  @ApiQuery({ name: 'limit', required: false, type: Number, description: 'Items per page' })
  @ApiQuery({ name: 'search', required: false, type: String, description: 'Search term' })
  @ApiQuery({ name: 'order', required: false, type: Object, description: 'Sort order' })
  findAll(@Query() params: StandardListParams) {
    return this.portfolioService.findAll(params);
  }

  @Get(':id')
  @ApiOperation({ summary: 'Get a portfolio item by ID' })
  @ApiResponse({ status: 200, description: 'Return the portfolio item.' })
  @ApiResponse({ status: 404, description: 'Portfolio item not found.' })
  @ApiParam({ name: 'id', type: 'string', description: 'Portfolio item ID' })
  findOne(@Param('id') id: string) {
    return this.portfolioService.findOne(id);
  }

  @Get('user/:userId')
  @ApiOperation({ summary: 'Get portfolio items by user ID' })
  @ApiResponse({ status: 200, description: 'Return the portfolio items.' })
  @ApiParam({ name: 'userId', type: 'string', description: 'User ID' })
  @ApiQuery({ name: 'page', required: false, type: Number, description: 'Page number' })
  @ApiQuery({ name: 'limit', required: false, type: Number, description: 'Items per page' })
  @ApiQuery({ name: 'search', required: false, type: String, description: 'Search term' })
  @ApiQuery({ name: 'order', required: false, type: Object, description: 'Sort order' })
  findByUserId(
    @Param('userId') userId: string,
    @Query() params: StandardListParams,
  ) {
    return this.portfolioService.findByUserId(userId, params);
  }

  @Patch(':id')
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles('ADMIN', 'TAILOR')
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Update a portfolio item' })
  @ApiResponse({ status: 200, description: 'The portfolio item has been successfully updated.' })
  @ApiResponse({ status: 400, description: 'Invalid input data.' })
  @ApiResponse({ status: 401, description: 'Unauthorized.' })
  @ApiResponse({ status: 403, description: 'Forbidden.' })
  @ApiResponse({ status: 404, description: 'Portfolio item not found.' })
  @ApiParam({ name: 'id', type: 'string', description: 'Portfolio item ID' })
  update(@Param('id') id: string, @Body() updatePortfolioItemDto: UpdatePortfolioItemDto) {
    return this.portfolioService.update(id, updatePortfolioItemDto);
  }

  @Delete(':id')
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles('ADMIN', 'TAILOR')
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Delete a portfolio item' })
  @ApiResponse({ status: 200, description: 'The portfolio item has been successfully deleted.' })
  @ApiResponse({ status: 401, description: 'Unauthorized.' })
  @ApiResponse({ status: 403, description: 'Forbidden.' })
  @ApiResponse({ status: 404, description: 'Portfolio item not found.' })
  @ApiParam({ name: 'id', type: 'string', description: 'Portfolio item ID' })
  remove(@Param('id') id: string) {
    return this.portfolioService.remove(id);
  }
}
