import { Module } from '@nestjs/common';
import { TailorService } from './services/tailor.service';
import { TailorController } from './controllers/tailor.controller';
import { TailorResolver } from './resolvers/tailor.resolver';
import { CoreModule } from '@core/core.module';
import { MongoDbModule } from '@infra/mongodb/mongodb.module';
import { WebsocketModule } from '@infra/websocket/websocket.module';
import { RabbitMQModule } from '@infra/rabbitmq/rabbitmq.module';
import { MeasurementService } from './services/measurement.service';
import { PortfolioService } from './services/portfolio.service';
import { MeasurementResolver } from './resolvers/measurement.resolver';
import { PortfolioResolver } from './resolvers/portfolio.resolver';
import { MeasurementController } from './controllers/measurement.controller';
import { PortfolioController } from './controllers/portfolio.controller';

@Module({
  imports: [
    CoreModule,
    MongoDbModule,
    WebsocketModule,
    RabbitMQModule,
  ],
  providers: [
    TailorService, 
    TailorResolver, 
    MeasurementService, 
    PortfolioService,
    MeasurementResolver,
    PortfolioResolver
  ],
  controllers: [
    TailorController, 
    MeasurementController, 
    PortfolioController
  ],
  exports: [
    TailorService, 
    MeasurementService, 
    PortfolioService
  ],
})
export class TailorModule {}
