import { Injectable, NotFoundException, ConflictException } from '@nestjs/common';
import { PrismaService } from '@core/prisma/prisma.service';
import { MongoDbService } from '@infra/mongodb/mongodb.service';
import { WebsocketService } from '@infra/websocket/websocket.service';
import { RabbitMQService } from '@infra/rabbitmq/rabbitmq.service';
import { CreateTailorProfileDto } from '../dto/create-tailor-profile.dto';
import { UpdateTailorProfileDto } from '../dto/update-tailor-profile.dto';
import { StandardListParams } from '@shared/utils/query-params.util';

@Injectable()
export class TailorService {
  constructor(
    private readonly prisma: PrismaService,
    private readonly mongoDbService: MongoDbService,
    private readonly websocketService: WebsocketService,
    private readonly rabbitMQService: RabbitMQService,
  ) {}

  async createProfile(createTailorProfileDto: CreateTailorProfileDto) {
    // Check if user exists
    const user = await this.prisma.user.findUnique({
      where: { id: createTailorProfileDto.userId },
    });

    if (!user) {
      throw new NotFoundException(`User with ID ${createTailorProfileDto.userId} not found`);
    }

    // Check if profile already exists
    const existingProfile = await this.prisma.tailorProfile.findUnique({
      where: { userId: createTailorProfileDto.userId },
    });

    if (existingProfile) {
      throw new ConflictException(`Tailor profile for user ${createTailorProfileDto.userId} already exists`);
    }

    // Create tailor profile in PostgreSQL
    const newProfile = await this.prisma.tailorProfile.create({
      data: {
        userId: createTailorProfileDto.userId,
        bio: createTailorProfileDto.bio,
        certificates: createTailorProfileDto.certificates || [],
        experienceYears: createTailorProfileDto.experienceYears,
        specialties: createTailorProfileDto.specialties || [],
        isVerified: createTailorProfileDto.isVerified || false,
      },
    });

    // Update user roles to include TAILOR role if not already present
    if (!user.roles.includes('TAILOR')) {
      await this.prisma.user.update({
        where: { id: user.id },
        data: {
          roles: [...user.roles, 'TAILOR'],
        },
      });
    }

    // Sync to MongoDB for fast reads
    await this.syncProfileToMongo(newProfile);

    // Notify about new tailor profile creation
    this.notifyProfileCreation(newProfile);

    return newProfile;
  }

  async findAllProfiles(params?: StandardListParams) {
    // Read from MongoDB for better performance
    const filter = params?.where || {};
    const options: Record<string, any> = {};
    
    // Apply pagination
    if (params?.page && params?.limit) {
      options.skip = (params.page - 1) * params.limit;
      options.limit = params.limit;
    } else if (params?.limit) {
      options.limit = params.limit;
    }
    
    // Apply sorting
    if (params?.order) {
      options.sort = {};
      for (const [field, direction] of Object.entries(params.order)) {
        options.sort[field] = direction === 'asc' ? 1 : -1;
      }
    }

    // Apply search if provided
    if (params?.search) {
      // Text search across multiple fields
      filter.$or = [
        { bio: { $regex: params.search, $options: 'i' } },
        { specialties: { $in: [new RegExp(params.search, 'i')] } }
      ];
    }
    
    // Get total count for pagination metadata
    const totalCount = await this.mongoDbService.count('tailorProfiles', filter);
    
    // Get profiles with filters and options
    const profiles = await this.mongoDbService.find('tailorProfiles', filter, options);
    
    // Generate pagination metadata
    const page = params?.page || 1;
    const limit = params?.limit || profiles.length;
    const totalPages = Math.ceil(totalCount / limit);
    
    return {
      data: profiles,
      pagination: {
        page,
        limit,
        totalPages,
        totalCount,
        hasNextPage: page < totalPages,
        hasPreviousPage: page > 1,
      }
    };
  }

  async findProfileById(id: string) {
    // Read from MongoDB for better performance
    const profile = await this.mongoDbService.findOne('tailorProfiles', { id });
    
    if (!profile) {
      throw new NotFoundException(`Tailor profile with ID ${id} not found`);
    }
    
    return profile;
  }

  async findProfileByUserId(userId: string) {
    // Read from MongoDB for better performance
    const profile = await this.mongoDbService.findOne('tailorProfiles', { userId });
    
    if (!profile) {
      throw new NotFoundException(`Tailor profile for user with ID ${userId} not found`);
    }
    
    return profile;
  }

  async updateProfile(id: string, updateTailorProfileDto: UpdateTailorProfileDto) {
    // Check if profile exists
    const profile = await this.prisma.tailorProfile.findUnique({
      where: { id },
    });
    
    if (!profile) {
      throw new NotFoundException(`Tailor profile with ID ${id} not found`);
    }
    
    // Update profile in PostgreSQL
    const updatedProfile = await this.prisma.tailorProfile.update({
      where: { id },
      data: {
        bio: updateTailorProfileDto.bio !== undefined ? updateTailorProfileDto.bio : profile.bio,
        certificates: updateTailorProfileDto.certificates !== undefined ? updateTailorProfileDto.certificates : profile.certificates,
        experienceYears: updateTailorProfileDto.experienceYears !== undefined ? updateTailorProfileDto.experienceYears : profile.experienceYears,
        specialties: updateTailorProfileDto.specialties !== undefined ? updateTailorProfileDto.specialties : profile.specialties,
        isVerified: updateTailorProfileDto.isVerified !== undefined ? updateTailorProfileDto.isVerified : profile.isVerified,
      },
    });
    
    // Sync to MongoDB for fast reads
    await this.syncProfileToMongo(updatedProfile);
    
    // Notify about profile update
    this.notifyProfileUpdate(updatedProfile);
    
    return updatedProfile;
  }

  async removeProfile(id: string) {
    // Check if profile exists
    const profile = await this.prisma.tailorProfile.findUnique({
      where: { id },
    });
    
    if (!profile) {
      throw new NotFoundException(`Tailor profile with ID ${id} not found`);
    }
    
    // Delete profile from PostgreSQL
    await this.prisma.tailorProfile.delete({
      where: { id },
    });
    
    // Delete from MongoDB
    await this.mongoDbService.deleteDocument('tailorProfiles', id);
    
    // Notify about profile deletion
    this.notifyProfileDeletion(id, profile.userId);
    
    return { id };
  }

  // Helper methods for MongoDB sync and notifications
  private async syncProfileToMongo(profile: any) {
    await this.mongoDbService.syncDocument('tailorProfiles', profile);
  }

  private async notifyProfileCreation(profile: any) {
    // Send message to RabbitMQ for event processing
    await this.rabbitMQService.publish({
      type: 'tailor_profile_created',
      data: profile,
    });

    // Notify admin users via WebSocket
    this.websocketService.sendToRole('ADMIN', 'tailor_profile_created', {
      message: `New tailor profile created for user ${profile.userId}`,
      profile,
    });
  }

  private async notifyProfileUpdate(profile: any) {
    // Send message to RabbitMQ for event processing
    await this.rabbitMQService.publish({
      type: 'tailor_profile_updated',
      data: profile,
    });

    // Notify the user about their profile update
    this.websocketService.sendToUser(profile.userId, 'profile_updated', {
      message: 'Your tailor profile has been updated',
      profile,
    });
  }

  private async notifyProfileDeletion(profileId: string, userId: string) {
    // Send message to RabbitMQ for event processing
    await this.rabbitMQService.publish({
      type: 'tailor_profile_deleted',
      data: { id: profileId, userId },
    });

    // Notify admin users via WebSocket
    this.websocketService.sendToRole('ADMIN', 'tailor_profile_deleted', {
      message: `Tailor profile with ID ${profileId} has been deleted`,
      profileId,
      userId,
    });
  }
}
