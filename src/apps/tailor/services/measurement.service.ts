import { Injectable, NotFoundException } from '@nestjs/common';
import { PrismaService } from '@core/prisma/prisma.service';
import { MongoDbService } from '@infra/mongodb/mongodb.service';
import { WebsocketService } from '@infra/websocket/websocket.service';
import { RabbitMQService } from '@infra/rabbitmq/rabbitmq.service';
import { CreateMeasurementDto } from '../dto/create-measurement.dto';
import { UpdateMeasurementDto } from '../dto/update-measurement.dto';
import { StandardListParams } from '@shared/utils/query-params.util';

@Injectable()
export class MeasurementService {
  constructor(
    private readonly prisma: PrismaService,
    private readonly mongoDbService: MongoDbService,
    private readonly websocketService: WebsocketService,
    private readonly rabbitMQService: RabbitMQService,
  ) {}

  async create(createMeasurementDto: CreateMeasurementDto) {
    // Check if user exists
    const user = await this.prisma.user.findUnique({
      where: { id: createMeasurementDto.userId },
    });

    if (!user) {
      throw new NotFoundException(`User with ID ${createMeasurementDto.userId} not found`);
    }

    // Create measurement in PostgreSQL
    const newMeasurement = await this.prisma.measurement.create({
      data: {
        userId: createMeasurementDto.userId,
        height: createMeasurementDto.height,
        weight: createMeasurementDto.weight,
        chest: createMeasurementDto.chest,
        waist: createMeasurementDto.waist,
        hips: createMeasurementDto.hips,
        shoulderWidth: createMeasurementDto.shoulderWidth,
        neck: createMeasurementDto.neck,
        armLength: createMeasurementDto.armLength,
        legLength: createMeasurementDto.legLength,
        thigh: createMeasurementDto.thigh,
        calf: createMeasurementDto.calf,
        footSize: createMeasurementDto.footSize,
        measurementUnit: createMeasurementDto.measurementUnit || 'cm',
        notes: createMeasurementDto.notes,
      },
    });

    // Sync to MongoDB for fast reads
    await this.syncMeasurementToMongo(newMeasurement);

    // Notify about new measurement creation
    this.notifyMeasurementCreation(newMeasurement);

    return newMeasurement;
  }

  async findAll(params?: StandardListParams) {
    // Read from MongoDB for better performance
    const filter = params?.where || {};
    const options: Record<string, any> = {};
    
    // Apply pagination
    if (params?.page && params?.limit) {
      options.skip = (params.page - 1) * params.limit;
      options.limit = params.limit;
    } else if (params?.limit) {
      options.limit = params.limit;
    }
    
    // Apply sorting
    if (params?.order) {
      options.sort = {};
      for (const [field, direction] of Object.entries(params.order)) {
        options.sort[field] = direction === 'asc' ? 1 : -1;
      }
    }
    
    // Get total count for pagination metadata
    const totalCount = await this.mongoDbService.count('measurements', filter);
    
    // Get measurements with filters and options
    const measurements = await this.mongoDbService.find('measurements', filter, options);
    
    // Generate pagination metadata
    const page = params?.page || 1;
    const limit = params?.limit || measurements.length;
    const totalPages = Math.ceil(totalCount / limit);
    
    return {
      data: measurements,
      pagination: {
        page,
        limit,
        totalPages,
        totalCount,
        hasNextPage: page < totalPages,
        hasPreviousPage: page > 1,
      }
    };
  }

  async findOne(id: string) {
    // Read from MongoDB for better performance
    const measurement = await this.mongoDbService.findOne('measurements', { id });
    
    if (!measurement) {
      throw new NotFoundException(`Measurement with ID ${id} not found`);
    }
    
    return measurement;
  }

  async findByUserId(userId: string) {
    // Read from MongoDB for better performance
    const measurements = await this.mongoDbService.find('measurements', { userId });
    
    return measurements;
  }

  async update(id: string, updateMeasurementDto: UpdateMeasurementDto) {
    // Check if measurement exists
    const measurement = await this.prisma.measurement.findUnique({
      where: { id },
    });
    
    if (!measurement) {
      throw new NotFoundException(`Measurement with ID ${id} not found`);
    }
    
    // Update measurement in PostgreSQL
    const updatedMeasurement = await this.prisma.measurement.update({
      where: { id },
      data: {
        height: updateMeasurementDto.height !== undefined ? updateMeasurementDto.height : measurement.height,
        weight: updateMeasurementDto.weight !== undefined ? updateMeasurementDto.weight : measurement.weight,
        chest: updateMeasurementDto.chest !== undefined ? updateMeasurementDto.chest : measurement.chest,
        waist: updateMeasurementDto.waist !== undefined ? updateMeasurementDto.waist : measurement.waist,
        hips: updateMeasurementDto.hips !== undefined ? updateMeasurementDto.hips : measurement.hips,
        shoulderWidth: updateMeasurementDto.shoulderWidth !== undefined ? updateMeasurementDto.shoulderWidth : measurement.shoulderWidth,
        neck: updateMeasurementDto.neck !== undefined ? updateMeasurementDto.neck : measurement.neck,
        armLength: updateMeasurementDto.armLength !== undefined ? updateMeasurementDto.armLength : measurement.armLength,
        legLength: updateMeasurementDto.legLength !== undefined ? updateMeasurementDto.legLength : measurement.legLength,
        thigh: updateMeasurementDto.thigh !== undefined ? updateMeasurementDto.thigh : measurement.thigh,
        calf: updateMeasurementDto.calf !== undefined ? updateMeasurementDto.calf : measurement.calf,
        footSize: updateMeasurementDto.footSize !== undefined ? updateMeasurementDto.footSize : measurement.footSize,
        measurementUnit: updateMeasurementDto.measurementUnit !== undefined ? updateMeasurementDto.measurementUnit : measurement.measurementUnit,
        notes: updateMeasurementDto.notes !== undefined ? updateMeasurementDto.notes : measurement.notes,
      },
    });
    
    // Sync to MongoDB for fast reads
    await this.syncMeasurementToMongo(updatedMeasurement);
    
    // Notify about measurement update
    this.notifyMeasurementUpdate(updatedMeasurement);
    
    return updatedMeasurement;
  }

  async remove(id: string) {
    // Check if measurement exists
    const measurement = await this.prisma.measurement.findUnique({
      where: { id },
    });
    
    if (!measurement) {
      throw new NotFoundException(`Measurement with ID ${id} not found`);
    }
    
    // Delete measurement from PostgreSQL
    await this.prisma.measurement.delete({
      where: { id },
    });
    
    // Delete from MongoDB
    await this.mongoDbService.deleteDocument('measurements', id);
    
    // Notify about measurement deletion
    this.notifyMeasurementDeletion(id, measurement.userId);
    
    return { id };
  }

  // Helper methods for MongoDB sync and notifications
  private async syncMeasurementToMongo(measurement: any) {
    await this.mongoDbService.syncDocument('measurements', measurement);
  }

  private async notifyMeasurementCreation(measurement: any) {
    // Send message to RabbitMQ for event processing
    await this.rabbitMQService.publish({
      type: 'measurement_created',
      data: measurement,
    });

    // Notify user via WebSocket
    this.websocketService.sendToUser(measurement.userId, 'measurement_created', {
      message: 'New measurement has been created',
      measurement,
    });
  }

  private async notifyMeasurementUpdate(measurement: any) {
    // Send message to RabbitMQ for event processing
    await this.rabbitMQService.publish({
      type: 'measurement_updated',
      data: measurement,
    });

    // Notify the user about their measurement update
    this.websocketService.sendToUser(measurement.userId, 'measurement_updated', {
      message: 'Your measurement has been updated',
      measurement,
    });
  }

  private async notifyMeasurementDeletion(measurementId: string, userId: string) {
    // Send message to RabbitMQ for event processing
    await this.rabbitMQService.publish({
      type: 'measurement_deleted',
      data: { id: measurementId, userId },
    });

    // Notify user via WebSocket
    this.websocketService.sendToUser(userId, 'measurement_deleted', {
      message: `Measurement with ID ${measurementId} has been deleted`,
      measurementId,
    });
  }
}
