import { Injectable, NotFoundException } from '@nestjs/common';
import { PrismaService } from '@core/prisma/prisma.service';
import { MongoDbService } from '@infra/mongodb/mongodb.service';
import { WebsocketService } from '@infra/websocket/websocket.service';
import { RabbitMQService } from '@infra/rabbitmq/rabbitmq.service';
import { CreatePortfolioItemDto } from '../dto/create-portfolio-item.dto';
import { UpdatePortfolioItemDto } from '../dto/update-portfolio-item.dto';
import { StandardListParams } from '@shared/utils/query-params.util';

@Injectable()
export class PortfolioService {
  constructor(
    private readonly prisma: PrismaService,
    private readonly mongoDbService: MongoDbService,
    private readonly websocketService: WebsocketService,
    private readonly rabbitMQService: RabbitMQService,
  ) {}

  async create(createPortfolioItemDto: CreatePortfolioItemDto) {
    // Check if user exists
    const user = await this.prisma.user.findUnique({
      where: { id: createPortfolioItemDto.userId },
    });

    if (!user) {
      throw new NotFoundException(`User with ID ${createPortfolioItemDto.userId} not found`);
    }

    // Create portfolio item in PostgreSQL
    const newPortfolioItem = await this.prisma.portfolioItem.create({
      data: {
        userId: createPortfolioItemDto.userId,
        title: createPortfolioItemDto.title,
        description: createPortfolioItemDto.description,
        imageUrl: createPortfolioItemDto.imageUrl,
        category: createPortfolioItemDto.category,
        tags: createPortfolioItemDto.tags || [],
      },
    });

    // Sync to MongoDB for fast reads
    await this.syncPortfolioItemToMongo(newPortfolioItem);

    // Notify about new portfolio item creation
    this.notifyPortfolioItemCreation(newPortfolioItem);

    return newPortfolioItem;
  }

  async findAll(params?: StandardListParams) {
    // Read from MongoDB for better performance
    const filter = params?.where || {};
    const options: Record<string, any> = {};
    
    // Apply pagination
    if (params?.page && params?.limit) {
      options.skip = (params.page - 1) * params.limit;
      options.limit = params.limit;
    } else if (params?.limit) {
      options.limit = params.limit;
    }
    
    // Apply sorting
    if (params?.order) {
      options.sort = {};
      for (const [field, direction] of Object.entries(params.order)) {
        options.sort[field] = direction === 'asc' ? 1 : -1;
      }
    }

    // Apply search if provided
    if (params?.search) {
      // Text search across multiple fields
      filter.$or = [
        { title: { $regex: params.search, $options: 'i' } },
        { description: { $regex: params.search, $options: 'i' } },
        { category: { $regex: params.search, $options: 'i' } },
        { tags: { $in: [new RegExp(params.search, 'i')] } }
      ];
    }
    
    // Get total count for pagination metadata
    const totalCount = await this.mongoDbService.count('portfolioItems', filter);
    
    // Get portfolio items with filters and options
    const portfolioItems = await this.mongoDbService.find('portfolioItems', filter, options);
    
    // Generate pagination metadata
    const page = params?.page || 1;
    const limit = params?.limit || portfolioItems.length;
    const totalPages = Math.ceil(totalCount / limit);
    
    return {
      data: portfolioItems,
      pagination: {
        page,
        limit,
        totalPages,
        totalCount,
        hasNextPage: page < totalPages,
        hasPreviousPage: page > 1,
      }
    };
  }

  async findOne(id: string) {
    // Read from MongoDB for better performance
    const portfolioItem = await this.mongoDbService.findOne('portfolioItems', { id });
    
    if (!portfolioItem) {
      throw new NotFoundException(`Portfolio item with ID ${id} not found`);
    }
    
    return portfolioItem;
  }

  async findByUserId(userId: string, params?: StandardListParams) {
    // Read from MongoDB for better performance
    const filter: Record<string, any> = { userId, ...(params?.where || {}) };
    const options: Record<string, any> = {};
    
    // Apply pagination
    if (params?.page && params?.limit) {
      options.skip = (params.page - 1) * params.limit;
      options.limit = params.limit;
    } else if (params?.limit) {
      options.limit = params.limit;
    }
    
    // Apply sorting
    if (params?.order) {
      options.sort = {};
      for (const [field, direction] of Object.entries(params.order)) {
        options.sort[field] = direction === 'asc' ? 1 : -1;
      }
    }

    // Apply search if provided
    if (params?.search) {
      // Text search across multiple fields
      filter.$or = [
        { title: { $regex: params.search, $options: 'i' } },
        { description: { $regex: params.search, $options: 'i' } },
        { category: { $regex: params.search, $options: 'i' } },
        { tags: { $in: [new RegExp(params.search, 'i')] } }
      ];
    }
    
    // Get total count for pagination metadata
    const totalCount = await this.mongoDbService.count('portfolioItems', filter);
    
    // Get portfolio items with filters and options
    const portfolioItems = await this.mongoDbService.find('portfolioItems', filter, options);
    
    // Generate pagination metadata
    const page = params?.page || 1;
    const limit = params?.limit || portfolioItems.length;
    const totalPages = Math.ceil(totalCount / limit);
    
    return {
      data: portfolioItems,
      pagination: {
        page,
        limit,
        totalPages,
        totalCount,
        hasNextPage: page < totalPages,
        hasPreviousPage: page > 1,
      }
    };
  }

  async update(id: string, updatePortfolioItemDto: UpdatePortfolioItemDto) {
    // Check if portfolio item exists
    const portfolioItem = await this.prisma.portfolioItem.findUnique({
      where: { id },
    });
    
    if (!portfolioItem) {
      throw new NotFoundException(`Portfolio item with ID ${id} not found`);
    }
    
    // Update portfolio item in PostgreSQL
    const updatedPortfolioItem = await this.prisma.portfolioItem.update({
      where: { id },
      data: {
        title: updatePortfolioItemDto.title !== undefined ? updatePortfolioItemDto.title : portfolioItem.title,
        description: updatePortfolioItemDto.description !== undefined ? updatePortfolioItemDto.description : portfolioItem.description,
        imageUrl: updatePortfolioItemDto.imageUrl !== undefined ? updatePortfolioItemDto.imageUrl : portfolioItem.imageUrl,
        category: updatePortfolioItemDto.category !== undefined ? updatePortfolioItemDto.category : portfolioItem.category,
        tags: updatePortfolioItemDto.tags !== undefined ? updatePortfolioItemDto.tags : portfolioItem.tags,
      },
    });
    
    // Sync to MongoDB for fast reads
    await this.syncPortfolioItemToMongo(updatedPortfolioItem);
    
    // Notify about portfolio item update
    this.notifyPortfolioItemUpdate(updatedPortfolioItem);
    
    return updatedPortfolioItem;
  }

  async remove(id: string) {
    // Check if portfolio item exists
    const portfolioItem = await this.prisma.portfolioItem.findUnique({
      where: { id },
    });
    
    if (!portfolioItem) {
      throw new NotFoundException(`Portfolio item with ID ${id} not found`);
    }
    
    // Delete portfolio item from PostgreSQL
    await this.prisma.portfolioItem.delete({
      where: { id },
    });
    
    // Delete from MongoDB
    await this.mongoDbService.deleteDocument('portfolioItems', id);
    
    // Notify about portfolio item deletion
    this.notifyPortfolioItemDeletion(id, portfolioItem.userId);
    
    return { id };
  }

  // Helper methods for MongoDB sync and notifications
  private async syncPortfolioItemToMongo(portfolioItem: any) {
    await this.mongoDbService.syncDocument('portfolioItems', portfolioItem);
  }

  private async notifyPortfolioItemCreation(portfolioItem: any) {
    // Send message to RabbitMQ for event processing
    await this.rabbitMQService.publish({
      type: 'portfolio_item_created',
      data: portfolioItem,
    });

    // Notify user via WebSocket
    this.websocketService.sendToUser(portfolioItem.userId, 'portfolio_item_created', {
      message: 'New portfolio item has been created',
      portfolioItem,
    });
  }

  private async notifyPortfolioItemUpdate(portfolioItem: any) {
    // Send message to RabbitMQ for event processing
    await this.rabbitMQService.publish({
      type: 'portfolio_item_updated',
      data: portfolioItem,
    });

    // Notify the user about their portfolio item update
    this.websocketService.sendToUser(portfolioItem.userId, 'portfolio_item_updated', {
      message: 'Your portfolio item has been updated',
      portfolioItem,
    });
  }

  private async notifyPortfolioItemDeletion(portfolioItemId: string, userId: string) {
    // Send message to RabbitMQ for event processing
    await this.rabbitMQService.publish({
      type: 'portfolio_item_deleted',
      data: { id: portfolioItemId, userId },
    });

    // Notify user via WebSocket
    this.websocketService.sendToUser(userId, 'portfolio_item_deleted', {
      message: `Portfolio item with ID ${portfolioItemId} has been deleted`,
      portfolioItemId,
    });
  }
}
