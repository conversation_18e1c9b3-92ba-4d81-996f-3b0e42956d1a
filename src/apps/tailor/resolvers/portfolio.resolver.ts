import { Resolver, Query, Mutation, Args, ID } from '@nestjs/graphql';
import { PortfolioService } from '../services/portfolio.service';
import { PortfolioItem } from '../entities/portfolio-item.entity';
import { CreatePortfolioItemDto } from '../dto/create-portfolio-item.dto';
import { UpdatePortfolioItemDto } from '../dto/update-portfolio-item.dto';
import { UseGuards } from '@nestjs/common';
import { JwtAuthGuard } from '@core/auth/guards/jwt-auth.guard';
import { RolesGuard } from '@core/auth/guards/roles.guard';
import { Roles } from '@core/auth/decorators/roles.decorator';
import { PaginatedResponse } from '@shared/interfaces/paginated-response.interface';
import { ListParamsArgs } from '@shared/dto/list-params.args';

// Create a paginated response type for PortfolioItem
const PaginatedPortfolioResponse = PaginatedResponse(PortfolioItem);

@Resolver(() => PortfolioItem)
export class PortfolioResolver {
  constructor(private readonly portfolioService: PortfolioService) {}

  @Mutation(() => PortfolioItem)
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles('ADMIN', 'TAILOR')
  createPortfolioItem(
    @Args('createPortfolioItemDto') createPortfolioItemDto: CreatePortfolioItemDto,
  ) {
    return this.portfolioService.create(createPortfolioItemDto);
  }

  @Query(() => PaginatedPortfolioResponse, { name: 'portfolioItems' })
  findAll(@Args() params: ListParamsArgs) {
    return this.portfolioService.findAll(params);
  }

  @Query(() => PortfolioItem, { name: 'portfolioItem' })
  findOne(@Args('id', { type: () => ID }) id: string) {
    return this.portfolioService.findOne(id);
  }

  @Query(() => PaginatedPortfolioResponse, { name: 'portfolioItemsByUserId' })
  findByUserId(
    @Args('userId', { type: () => ID }) userId: string,
    @Args() params: ListParamsArgs,
  ) {
    return this.portfolioService.findByUserId(userId, params);
  }

  @Mutation(() => PortfolioItem)
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles('ADMIN', 'TAILOR')
  updatePortfolioItem(
    @Args('id', { type: () => ID }) id: string,
    @Args('updatePortfolioItemDto') updatePortfolioItemDto: UpdatePortfolioItemDto,
  ) {
    return this.portfolioService.update(id, updatePortfolioItemDto);
  }

  @Mutation(() => PortfolioItem)
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles('ADMIN', 'TAILOR')
  removePortfolioItem(@Args('id', { type: () => ID }) id: string) {
    return this.portfolioService.remove(id);
  }
}
