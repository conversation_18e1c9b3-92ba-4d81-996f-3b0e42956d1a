import { Resolver, Query, Mutation, Args, ID, ObjectType } from '@nestjs/graphql';
import { TailorService } from '../services/tailor.service';
import { TailorProfile } from '../entities/tailor-profile.entity';
import { CreateTailorProfileDto } from '../dto/create-tailor-profile.dto';
import { UpdateTailorProfileDto } from '../dto/update-tailor-profile.dto';
import { UseGuards } from '@nestjs/common';
import { JwtAuthGuard } from '@core/auth/guards/jwt-auth.guard';
import { RolesGuard } from '@core/auth/guards/roles.guard';
import { Roles } from '@core/auth/decorators/roles.decorator';
import { PaginatedResponse, PaginationMetaDto } from '@shared/interfaces/paginated-response.interface';
import { ListParamsArgs } from '@shared/dto/list-params.args';
import { Field } from '@nestjs/graphql';

@ObjectType()
class PaginatedTailorProfileResponse {
  @Field(() => [TailorProfile])
  data: TailorProfile[];

  @Field(() => PaginationMetaDto)
  pagination: PaginationMetaDto;
}

@Resolver(() => TailorProfile)
export class TailorResolver {
  constructor(private readonly tailorService: TailorService) {}

  @Mutation(() => TailorProfile)
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles('ADMIN', 'TAILOR')
  createTailorProfile(
    @Args('createTailorProfileDto') createTailorProfileDto: CreateTailorProfileDto,
  ) {
    return this.tailorService.createProfile(createTailorProfileDto);
  }

  @Query(() => PaginatedTailorProfileResponse, { name: 'tailorProfiles' })
  findAllProfiles(@Args() params: ListParamsArgs) {
    return this.tailorService.findAllProfiles(params);
  }

  @Query(() => TailorProfile, { name: 'tailorProfile' })
  findOne(@Args('id', { type: () => ID }) id: string) {
    return this.tailorService.findProfileById(id);
  }

  @Query(() => TailorProfile, { name: 'tailorProfileByUserId' })
  findByUserId(@Args('userId', { type: () => ID }) userId: string) {
    return this.tailorService.findProfileByUserId(userId);
  }

  @Mutation(() => TailorProfile)
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles('ADMIN', 'TAILOR')
  updateTailorProfile(
    @Args('id', { type: () => ID }) id: string,
    @Args('updateTailorProfileDto') updateTailorProfileDto: UpdateTailorProfileDto,
  ) {
    return this.tailorService.updateProfile(id, updateTailorProfileDto);
  }

  @Mutation(() => TailorProfile)
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles('ADMIN', 'TAILOR')
  removeTailorProfile(@Args('id', { type: () => ID }) id: string) {
    return this.tailorService.removeProfile(id);
  }
}
