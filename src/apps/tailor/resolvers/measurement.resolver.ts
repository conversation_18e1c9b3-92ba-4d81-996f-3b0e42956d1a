import { Resolver, Query, Mutation, Args, ID } from '@nestjs/graphql';
import { MeasurementService } from '../services/measurement.service';
import { Measurement } from '../entities/measurement.entity';
import { CreateMeasurementDto } from '../dto/create-measurement.dto';
import { UpdateMeasurementDto } from '../dto/update-measurement.dto';
import { UseGuards } from '@nestjs/common';
import { JwtAuthGuard } from '@core/auth/guards/jwt-auth.guard';
import { RolesGuard } from '@core/auth/guards/roles.guard';
import { Roles } from '@core/auth/decorators/roles.decorator';
import { PaginatedResponse } from '@shared/interfaces/paginated-response.interface';
import { ListParamsArgs } from '@shared/dto/list-params.args';

// Create a paginated response type for Measurement
const PaginatedMeasurementResponse = PaginatedResponse(Measurement);

@Resolver(() => Measurement)
export class MeasurementResolver {
  constructor(private readonly measurementService: MeasurementService) {}

  @Mutation(() => Measurement)
  @UseGuards(JwtAuthGuard)
  createMeasurement(
    @Args('createMeasurementDto') createMeasurementDto: CreateMeasurementDto,
  ) {
    return this.measurementService.create(createMeasurementDto);
  }

  @Query(() => PaginatedMeasurementResponse, { name: 'measurements' })
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles('ADMIN', 'TAILOR')
  findAll(@Args() params: ListParamsArgs) {
    return this.measurementService.findAll(params);
  }

  @Query(() => Measurement, { name: 'measurement' })
  @UseGuards(JwtAuthGuard)
  findOne(@Args('id', { type: () => ID }) id: string) {
    return this.measurementService.findOne(id);
  }

  @Query(() => [Measurement], { name: 'measurementsByUserId' })
  @UseGuards(JwtAuthGuard)
  findByUserId(@Args('userId', { type: () => ID }) userId: string) {
    return this.measurementService.findByUserId(userId);
  }

  @Mutation(() => Measurement)
  @UseGuards(JwtAuthGuard)
  updateMeasurement(
    @Args('id', { type: () => ID }) id: string,
    @Args('updateMeasurementDto') updateMeasurementDto: UpdateMeasurementDto,
  ) {
    return this.measurementService.update(id, updateMeasurementDto);
  }

  @Mutation(() => Measurement)
  @UseGuards(JwtAuthGuard)
  removeMeasurement(@Args('id', { type: () => ID }) id: string) {
    return this.measurementService.remove(id);
  }
}
