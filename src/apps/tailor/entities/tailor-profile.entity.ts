import { ObjectType, Field, ID, Float, Int } from '@nestjs/graphql';
import { ApiProperty } from '@nestjs/swagger';
import { User } from '@apps/user/entities/user.entity';

@ObjectType()
export class TailorProfile {
  @ApiProperty({ description: 'Unique identifier for the tailor profile' })
  @Field(() => ID)
  id: string;

  @ApiProperty({ description: 'User ID associated with this tailor profile' })
  @Field(() => ID)
  userId: string;

  @ApiProperty({ description: 'User associated with this tailor profile' })
  @Field(() => User, { nullable: true })
  user?: User;

  @ApiProperty({ description: 'Tailor biography', nullable: true })
  @Field({ nullable: true })
  bio?: string;

  @ApiProperty({ description: 'Array of certificate URLs', type: [String] })
  @Field(() => [String], { nullable: true })
  certificates?: string[];

  @ApiProperty({ description: 'Years of experience', nullable: true })
  @Field(() => Int, { nullable: true })
  experienceYears?: number;

  @ApiProperty({ description: 'Array of specialties', type: [String] })
  @Field(() => [String], { nullable: true })
  specialties?: string[];

  @ApiProperty({ description: 'Average rating', nullable: true })
  @Field(() => Float, { nullable: true, defaultValue: 0 })
  rating?: number;

  @ApiProperty({ description: 'Number of reviews', nullable: true })
  @Field(() => Int, { nullable: true, defaultValue: 0 })
  reviewCount?: number;

  @ApiProperty({ description: 'Verification status' })
  @Field(() => Boolean, { defaultValue: false })
  isVerified: boolean;

  @ApiProperty({ description: 'Creation timestamp' })
  @Field(() => Date)
  createdAt: Date;

  @ApiProperty({ description: 'Last update timestamp' })
  @Field(() => Date)
  updatedAt: Date;
}
