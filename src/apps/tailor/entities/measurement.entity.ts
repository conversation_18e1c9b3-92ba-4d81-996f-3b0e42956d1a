import { ObjectType, Field, ID, Float } from '@nestjs/graphql';
import { ApiProperty } from '@nestjs/swagger';
import { User } from '@apps/user/entities/user.entity';

@ObjectType()
export class Measurement {
  @ApiProperty({ description: 'Unique identifier for the measurement' })
  @Field(() => ID)
  id: string;

  @ApiProperty({ description: 'User ID associated with this measurement' })
  @Field(() => ID)
  userId: string;

  @ApiProperty({ description: 'User associated with this measurement' })
  @Field(() => User, { nullable: true })
  user?: User;

  @ApiProperty({ description: 'Height in cm/inch', nullable: true })
  @Field(() => Float, { nullable: true })
  height?: number;

  @ApiProperty({ description: 'Weight in kg/lbs', nullable: true })
  @Field(() => Float, { nullable: true })
  weight?: number;

  @ApiProperty({ description: 'Chest measurement in cm/inch', nullable: true })
  @Field(() => Float, { nullable: true })
  chest?: number;

  @ApiProperty({ description: 'Waist measurement in cm/inch', nullable: true })
  @Field(() => Float, { nullable: true })
  waist?: number;

  @ApiProperty({ description: 'Hips measurement in cm/inch', nullable: true })
  @Field(() => Float, { nullable: true })
  hips?: number;

  @ApiProperty({ description: 'Shoulder width in cm/inch', nullable: true })
  @Field(() => Float, { nullable: true })
  shoulderWidth?: number;

  @ApiProperty({ description: 'Neck measurement in cm/inch', nullable: true })
  @Field(() => Float, { nullable: true })
  neck?: number;

  @ApiProperty({ description: 'Arm length in cm/inch', nullable: true })
  @Field(() => Float, { nullable: true })
  armLength?: number;

  @ApiProperty({ description: 'Leg length in cm/inch', nullable: true })
  @Field(() => Float, { nullable: true })
  legLength?: number;

  @ApiProperty({ description: 'Thigh measurement in cm/inch', nullable: true })
  @Field(() => Float, { nullable: true })
  thigh?: number;

  @ApiProperty({ description: 'Calf measurement in cm/inch', nullable: true })
  @Field(() => Float, { nullable: true })
  calf?: number;

  @ApiProperty({ description: 'Foot size in cm/inch', nullable: true })
  @Field(() => Float, { nullable: true })
  footSize?: number;

  @ApiProperty({ description: 'Measurement unit (cm/inch)', default: 'cm' })
  @Field()
  measurementUnit: string;

  @ApiProperty({ description: 'Additional notes', nullable: true })
  @Field({ nullable: true })
  notes?: string;

  @ApiProperty({ description: 'Creation timestamp' })
  @Field(() => Date)
  createdAt: Date;

  @ApiProperty({ description: 'Last update timestamp' })
  @Field(() => Date)
  updatedAt: Date;
}
