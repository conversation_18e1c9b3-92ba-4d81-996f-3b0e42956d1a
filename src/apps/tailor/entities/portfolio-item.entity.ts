import { ObjectType, Field, ID } from '@nestjs/graphql';
import { ApiProperty } from '@nestjs/swagger';
import { User } from '@apps/user/entities/user.entity';

@ObjectType()
export class PortfolioItem {
  @ApiProperty({ description: 'Unique identifier for the portfolio item' })
  @Field(() => ID)
  id: string;

  @ApiProperty({ description: 'User ID associated with this portfolio item' })
  @Field(() => ID)
  userId: string;

  @ApiProperty({ description: 'User associated with this portfolio item' })
  @Field(() => User, { nullable: true })
  user?: User;

  @ApiProperty({ description: 'Title of the portfolio item' })
  @Field()
  title: string;

  @ApiProperty({ description: 'Description of the portfolio item', nullable: true })
  @Field({ nullable: true })
  description?: string;

  @ApiProperty({ description: 'Image URL for the portfolio item' })
  @Field()
  imageUrl: string;

  @ApiProperty({ description: 'Category of the portfolio item', nullable: true })
  @Field({ nullable: true })
  category?: string;

  @ApiProperty({ description: 'Tags for the portfolio item', type: [String] })
  @Field(() => [String], { nullable: true })
  tags?: string[];

  @ApiProperty({ description: 'Creation timestamp' })
  @Field(() => Date)
  createdAt: Date;

  @ApiProperty({ description: 'Last update timestamp' })
  @Field(() => Date)
  updatedAt: Date;
}
