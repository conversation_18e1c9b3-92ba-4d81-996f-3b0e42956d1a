import { InputType, Field, Float, PartialType } from '@nestjs/graphql';
import { ApiPropertyOptional } from '@nestjs/swagger';
import { IsString, IsOptional, IsNumber, Min, IsEnum } from 'class-validator';
import { CreateMeasurementDto } from './create-measurement.dto';

enum MeasurementUnit {
  CM = 'cm',
  INCH = 'inch'
}

@InputType()
export class UpdateMeasurementDto extends PartialType(CreateMeasurementDto) {
  @ApiPropertyOptional({
    description: 'Height in cm/inch',
    example: 175.5,
    minimum: 0
  })
  @Field(() => Float, { nullable: true })
  @IsNumber()
  @Min(0)
  @IsOptional()
  height?: number;

  @ApiPropertyOptional({
    description: 'Weight in kg/lbs',
    example: 70.5,
    minimum: 0
  })
  @Field(() => Float, { nullable: true })
  @IsNumber()
  @Min(0)
  @IsOptional()
  weight?: number;

  @ApiPropertyOptional({
    description: 'Chest measurement in cm/inch',
    example: 95.5,
    minimum: 0
  })
  @Field(() => Float, { nullable: true })
  @IsNumber()
  @Min(0)
  @IsOptional()
  chest?: number;

  @ApiPropertyOptional({
    description: 'Waist measurement in cm/inch',
    example: 80.5,
    minimum: 0
  })
  @Field(() => Float, { nullable: true })
  @IsNumber()
  @Min(0)
  @IsOptional()
  waist?: number;

  @ApiPropertyOptional({
    description: 'Hips measurement in cm/inch',
    example: 95.5,
    minimum: 0
  })
  @Field(() => Float, { nullable: true })
  @IsNumber()
  @Min(0)
  @IsOptional()
  hips?: number;

  @ApiPropertyOptional({
    description: 'Shoulder width in cm/inch',
    example: 45.5,
    minimum: 0
  })
  @Field(() => Float, { nullable: true })
  @IsNumber()
  @Min(0)
  @IsOptional()
  shoulderWidth?: number;

  @ApiPropertyOptional({
    description: 'Neck measurement in cm/inch',
    example: 38.5,
    minimum: 0
  })
  @Field(() => Float, { nullable: true })
  @IsNumber()
  @Min(0)
  @IsOptional()
  neck?: number;

  @ApiPropertyOptional({
    description: 'Arm length in cm/inch',
    example: 65.5,
    minimum: 0
  })
  @Field(() => Float, { nullable: true })
  @IsNumber()
  @Min(0)
  @IsOptional()
  armLength?: number;

  @ApiPropertyOptional({
    description: 'Leg length in cm/inch',
    example: 85.5,
    minimum: 0
  })
  @Field(() => Float, { nullable: true })
  @IsNumber()
  @Min(0)
  @IsOptional()
  legLength?: number;

  @ApiPropertyOptional({
    description: 'Thigh measurement in cm/inch',
    example: 55.5,
    minimum: 0
  })
  @Field(() => Float, { nullable: true })
  @IsNumber()
  @Min(0)
  @IsOptional()
  thigh?: number;

  @ApiPropertyOptional({
    description: 'Calf measurement in cm/inch',
    example: 35.5,
    minimum: 0
  })
  @Field(() => Float, { nullable: true })
  @IsNumber()
  @Min(0)
  @IsOptional()
  calf?: number;

  @ApiPropertyOptional({
    description: 'Foot size in cm/inch',
    example: 26.5,
    minimum: 0
  })
  @Field(() => Float, { nullable: true })
  @IsNumber()
  @Min(0)
  @IsOptional()
  footSize?: number;

  @ApiPropertyOptional({
    description: 'Measurement unit (cm/inch)',
    example: 'cm',
    enum: MeasurementUnit
  })
  @Field({ nullable: true })
  @IsEnum(MeasurementUnit)
  @IsOptional()
  measurementUnit?: string;

  @ApiPropertyOptional({
    description: 'Additional notes',
    example: 'Customer prefers loose fitting around the waist.'
  })
  @Field({ nullable: true })
  @IsString()
  @IsOptional()
  notes?: string;
}
