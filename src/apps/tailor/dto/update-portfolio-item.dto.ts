import { InputType, Field, PartialType } from '@nestjs/graphql';
import { ApiPropertyOptional } from '@nestjs/swagger';
import { IsString, IsOptional, IsArray, IsUrl } from 'class-validator';
import { CreatePortfolioItemDto } from './create-portfolio-item.dto';

@InputType()
export class UpdatePortfolioItemDto extends PartialType(CreatePortfolioItemDto) {
  @ApiPropertyOptional({
    description: 'Title of the portfolio item',
    example: 'Wedding Dress Collection 2025'
  })
  @Field({ nullable: true })
  @IsString()
  @IsOptional()
  title?: string;

  @ApiPropertyOptional({
    description: 'Description of the portfolio item',
    example: 'Custom-designed wedding dress with hand-sewn embroidery and pearl details.'
  })
  @Field({ nullable: true })
  @IsString()
  @IsOptional()
  description?: string;

  @ApiPropertyOptional({
    description: 'Image URL for the portfolio item',
    example: 'https://example.com/portfolio/wedding-dress.jpg'
  })
  @Field({ nullable: true })
  @IsString()
  @IsUrl({}, { message: 'Image URL must be a valid URL' })
  @IsOptional()
  imageUrl?: string;

  @ApiPropertyOptional({
    description: 'Category of the portfolio item',
    example: 'Wedding'
  })
  @Field({ nullable: true })
  @IsString()
  @IsOptional()
  category?: string;

  @ApiPropertyOptional({
    description: 'Tags for the portfolio item',
    example: ['Wedding', 'Dress', 'Formal', 'Custom'],
    isArray: true
  })
  @Field(() => [String], { nullable: true })
  @IsArray()
  @IsOptional()
  tags?: string[];
}
