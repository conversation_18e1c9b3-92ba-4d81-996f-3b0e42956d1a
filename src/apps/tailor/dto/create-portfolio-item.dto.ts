import { InputType, Field } from '@nestjs/graphql';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsNotEmpty, IsString, IsOptional, IsArray, IsUrl } from 'class-validator';

@InputType()
export class CreatePortfolioItemDto {
  @ApiProperty({
    description: 'User ID associated with this portfolio item',
    example: '123e4567-e89b-12d3-a456-426614174000'
  })
  @Field()
  @IsString()
  @IsNotEmpty({ message: 'User ID is required' })
  userId: string;

  @ApiProperty({
    description: 'Title of the portfolio item',
    example: 'Wedding Dress Collection 2025'
  })
  @Field()
  @IsString()
  @IsNotEmpty({ message: 'Title is required' })
  title: string;

  @ApiPropertyOptional({
    description: 'Description of the portfolio item',
    example: 'Custom-designed wedding dress with hand-sewn embroidery and pearl details.'
  })
  @Field({ nullable: true })
  @IsString()
  @IsOptional()
  description?: string;

  @ApiProperty({
    description: 'Image URL for the portfolio item',
    example: 'https://example.com/portfolio/wedding-dress.jpg'
  })
  @Field()
  @IsString()
  @IsUrl({}, { message: 'Image URL must be a valid URL' })
  @IsNotEmpty({ message: 'Image URL is required' })
  imageUrl: string;

  @ApiPropertyOptional({
    description: 'Category of the portfolio item',
    example: 'Wedding'
  })
  @Field({ nullable: true })
  @IsString()
  @IsOptional()
  category?: string;

  @ApiPropertyOptional({
    description: 'Tags for the portfolio item',
    example: ['Wedding', 'Dress', 'Formal', 'Custom'],
    isArray: true
  })
  @Field(() => [String], { nullable: true })
  @IsArray()
  @IsOptional()
  tags?: string[];
}
