import { ApiProperty } from '@nestjs/swagger';
import { IsDate, IsN<PERSON>ber, IsOptional, IsString, IsUUID } from 'class-validator';

export class MeasurementDto {
  @ApiProperty({
    description: 'Unique identifier of the measurement',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @IsUUID()
  id: string;

  @ApiProperty({
    description: 'User ID associated with this measurement',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @IsUUID()
  userId: string;

  @ApiProperty({
    description: '<PERSON>lor ID who took the measurement',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @IsUUID()
  tailorId: string;

  @ApiProperty({
    description: 'Chest measurement in centimeters',
    example: 96.5,
  })
  @IsNumber()
  chest: number;

  @ApiProperty({
    description: 'Waist measurement in centimeters',
    example: 82.3,
  })
  @IsNumber()
  waist: number;

  @ApiProperty({
    description: 'Hip measurement in centimeters',
    example: 98.7,
  })
  @IsNumber()
  hip: number;

  @ApiProperty({
    description: 'Shoulder width measurement in centimeters',
    example: 45.2,
  })
  @IsNumber()
  shoulderWidth: number;

  @ApiProperty({
    description: 'Sleeve length measurement in centimeters',
    example: 64.8,
  })
  @IsNumber()
  sleeveLength: number;

  @ApiProperty({
    description: 'Neck measurement in centimeters',
    example: 38.1,
  })
  @IsNumber()
  neck: number;

  @ApiProperty({
    description: 'Inseam measurement in centimeters',
    example: 78.5,
  })
  @IsNumber()
  inseam: number;

  @ApiProperty({
    description: 'Outseam measurement in centimeters',
    example: 102.3,
  })
  @IsNumber()
  outseam: number;

  @ApiProperty({
    description: 'Thigh measurement in centimeters',
    example: 56.7,
  })
  @IsNumber()
  thigh: number;

  @ApiProperty({
    description: 'Arm length measurement in centimeters',
    example: 62.4,
  })
  @IsNumber()
  armLength: number;

  @ApiProperty({
    description: 'Wrist measurement in centimeters',
    example: 16.8,
  })
  @IsNumber()
  wrist: number;

  @ApiProperty({
    description: 'Height measurement in centimeters',
    example: 178.5,
  })
  @IsNumber()
  height: number;

  @ApiProperty({
    description: 'Weight measurement in kilograms',
    example: 75.2,
  })
  @IsNumber()
  weight: number;

  @ApiProperty({
    description: 'Additional notes about the measurement',
    example: 'Customer prefers slightly looser fit around the waist',
    required: false,
  })
  @IsString()
  @IsOptional()
  notes?: string;

  @ApiProperty({
    description: 'Date when the measurement was taken',
    example: '2023-01-15T08:30:00Z',
  })
  @IsDate()
  measurementDate: Date;

  @ApiProperty({
    description: 'Date when the measurement record was created',
    example: '2023-01-15T08:30:00Z',
  })
  @IsDate()
  createdAt: Date;

  @ApiProperty({
    description: 'Date when the measurement record was last updated',
    example: '2023-02-20T10:15:00Z',
  })
  @IsDate()
  updatedAt: Date;
}
