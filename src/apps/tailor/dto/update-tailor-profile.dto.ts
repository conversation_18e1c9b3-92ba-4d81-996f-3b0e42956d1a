import { InputType, Field, Int, PartialType } from '@nestjs/graphql';
import { ApiPropertyOptional } from '@nestjs/swagger';
import { IsString, IsOptional, IsArray, IsInt, Min, IsBoolean } from 'class-validator';
import { CreateTailorProfileDto } from './create-tailor-profile.dto';

@InputType()
export class UpdateTailorProfileDto extends PartialType(CreateTailorProfileDto) {
  @ApiPropertyOptional({
    description: 'Tailor biography',
    example: 'Professional tailor with 10 years of experience in custom garments.'
  })
  @Field({ nullable: true })
  @IsString()
  @IsOptional()
  bio?: string;

  @ApiPropertyOptional({
    description: 'Array of certificate URLs',
    example: ['https://example.com/certificate1.pdf', 'https://example.com/certificate2.pdf'],
    isArray: true
  })
  @Field(() => [String], { nullable: true })
  @IsArray()
  @IsOptional()
  certificates?: string[];

  @ApiPropertyOptional({
    description: 'Years of experience',
    example: 5,
    minimum: 0
  })
  @Field(() => Int, { nullable: true })
  @IsInt()
  @Min(0)
  @IsOptional()
  experienceYears?: number;

  @ApiPropertyOptional({
    description: 'Array of specialties',
    example: ['Wedding dresses', 'Formal suits', 'Alterations'],
    isArray: true
  })
  @Field(() => [String], { nullable: true })
  @IsArray()
  @IsOptional()
  specialties?: string[];

  @ApiPropertyOptional({
    description: 'Verification status',
    example: false
  })
  @Field(() => Boolean, { nullable: true })
  @IsBoolean()
  @IsOptional()
  isVerified?: boolean;
}
