import { ApiProperty } from '@nestjs/swagger';
import { IsArra<PERSON>, IsDate, IsOptional, IsString, IsUUID } from 'class-validator';

export class PortfolioItemDto {
  @ApiProperty({
    description: 'Unique identifier of the portfolio item',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @IsUUID()
  id: string;

  @ApiProperty({
    description: 'Tailor ID associated with this portfolio item',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @IsUUID()
  tailorId: string;

  @ApiProperty({
    description: 'Title of the portfolio item',
    example: 'Custom Wedding Suit',
  })
  @IsString()
  title: string;

  @ApiProperty({
    description: 'Description of the portfolio item',
    example: 'Hand-crafted three-piece wedding suit made with premium Italian wool',
  })
  @IsString()
  description: string;

  @ApiProperty({
    description: 'Type of garment',
    example: 'Suit',
  })
  @IsString()
  garmentType: string;

  @ApiProperty({
    description: 'Materials used in the garment',
    example: 'Italian wool, silk lining, mother of pearl buttons',
  })
  @IsString()
  materials: string;

  @ApiProperty({
    description: 'Array of image URLs showcasing the portfolio item',
    example: [
      'https://example.com/images/portfolio/suit1_front.jpg',
      'https://example.com/images/portfolio/suit1_back.jpg',
      'https://example.com/images/portfolio/suit1_detail.jpg',
    ],
  })
  @IsArray()
  @IsString({ each: true })
  imageUrls: string[];

  @ApiProperty({
    description: 'Featured image URL (main display image)',
    example: 'https://example.com/images/portfolio/suit1_front.jpg',
  })
  @IsString()
  featuredImageUrl: string;

  @ApiProperty({
    description: 'Completion date of the garment',
    example: '2023-01-15',
  })
  @IsDate()
  completionDate: Date;

  @ApiProperty({
    description: 'Tags associated with the portfolio item',
    example: ['wedding', 'suit', 'formal', 'custom'],
    required: false,
  })
  @IsArray()
  @IsString({ each: true })
  @IsOptional()
  tags?: string[];

  @ApiProperty({
    description: 'Client testimonial about the garment',
    example: 'The suit was perfect for my wedding day. The fit was impeccable and I received many compliments.',
    required: false,
  })
  @IsString()
  @IsOptional()
  clientTestimonial?: string;

  @ApiProperty({
    description: 'Date when the portfolio item was created',
    example: '2023-01-15T08:30:00Z',
  })
  @IsDate()
  createdAt: Date;

  @ApiProperty({
    description: 'Date when the portfolio item was last updated',
    example: '2023-02-20T10:15:00Z',
  })
  @IsDate()
  updatedAt: Date;
}
