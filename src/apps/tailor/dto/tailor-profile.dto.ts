import { ApiProperty } from '@nestjs/swagger';
import { <PERSON><PERSON><PERSON><PERSON>, IsDate, IsEnum, IsNumber, IsOptional, IsString, IsUUID } from 'class-validator';

enum ExperienceLevel {
  APPRENTICE = 'APPRENTICE',
  JUNIOR = 'JUNIOR',
  INTERMEDIATE = 'INTERMEDIATE',
  SENIOR = 'SENIOR',
  MASTER = 'MASTER',
}

export class TailorProfileDto {
  @ApiProperty({
    description: 'Unique identifier of the tailor profile',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @IsUUID()
  id: string;

  @ApiProperty({
    description: 'User ID associated with this tailor profile',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @IsUUID()
  userId: string;

  @ApiProperty({
    description: 'Professional title of the tailor',
    example: 'Master Bespoke Tailor',
  })
  @IsString()
  title: string;

  @ApiProperty({
    description: 'Professional biography of the tailor',
    example: 'With over 20 years of experience in bespoke tailoring, specializing in formal wear and wedding attire.',
  })
  @IsString()
  biography: string;

  @ApiProperty({
    description: 'Years of experience in tailoring',
    example: 20,
  })
  @IsNumber()
  yearsOfExperience: number;

  @ApiProperty({
    description: 'Experience level of the tailor',
    enum: ExperienceLevel,
    example: ExperienceLevel.MASTER,
  })
  @IsEnum(ExperienceLevel)
  experienceLevel: ExperienceLevel;

  @ApiProperty({
    description: 'Specializations of the tailor',
    example: ['Formal wear', 'Wedding attire', 'Suits', 'Traditional garments'],
  })
  @IsArray()
  @IsString({ each: true })
  specializations: string[];

  @ApiProperty({
    description: 'Certifications held by the tailor',
    example: ['Certified Master Tailor - London School of Fashion', 'Advanced Pattern Making - Paris Institute'],
    required: false,
  })
  @IsArray()
  @IsString({ each: true })
  @IsOptional()
  certifications?: string[];

  @ApiProperty({
    description: 'Languages spoken by the tailor',
    example: ['English', 'French', 'Italian'],
    required: false,
  })
  @IsArray()
  @IsString({ each: true })
  @IsOptional()
  languages?: string[];

  @ApiProperty({
    description: 'Location of the tailor (city, country)',
    example: 'London, United Kingdom',
  })
  @IsString()
  location: string;

  @ApiProperty({
    description: 'Profile image URL of the tailor',
    example: 'https://example.com/images/tailors/john_smith.jpg',
  })
  @IsString()
  profileImageUrl: string;

  @ApiProperty({
    description: 'Contact email of the tailor',
    example: '<EMAIL>',
  })
  @IsString()
  contactEmail: string;

  @ApiProperty({
    description: 'Contact phone number of the tailor',
    example: '+44 20 1234 5678',
    required: false,
  })
  @IsString()
  @IsOptional()
  contactPhone?: string;

  @ApiProperty({
    description: 'Social media profiles of the tailor',
    example: {
      instagram: '@mastersmith',
      linkedin: 'john-smith-tailor',
      website: 'https://johnsmithtailor.com',
    },
    required: false,
  })
  @IsOptional()
  socialMedia?: Record<string, string>;

  @ApiProperty({
    description: 'Average rating of the tailor (1-5)',
    example: 4.8,
  })
  @IsNumber()
  averageRating: number;

  @ApiProperty({
    description: 'Number of reviews received',
    example: 124,
  })
  @IsNumber()
  reviewCount: number;

  @ApiProperty({
    description: 'Date when the tailor profile was created',
    example: '2023-01-15T08:30:00Z',
  })
  @IsDate()
  createdAt: Date;

  @ApiProperty({
    description: 'Date when the tailor profile was last updated',
    example: '2023-02-20T10:15:00Z',
  })
  @IsDate()
  updatedAt: Date;
}
