import { InputType, Field, Int } from '@nestjs/graphql';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsNotEmpty, IsString, IsOptional, IsArray, IsInt, Min, IsBoolean } from 'class-validator';

@InputType()
export class CreateTailorProfileDto {
  @ApiProperty({
    description: 'User ID associated with this tailor profile',
    example: '123e4567-e89b-12d3-a456-426614174000'
  })
  @Field()
  @IsString()
  @IsNotEmpty({ message: 'User ID is required' })
  userId: string;

  @ApiPropertyOptional({
    description: 'Tailor biography',
    example: 'Professional tailor with 10 years of experience in custom garments.'
  })
  @Field({ nullable: true })
  @IsString()
  @IsOptional()
  bio?: string;

  @ApiPropertyOptional({
    description: 'Array of certificate URLs',
    example: ['https://example.com/certificate1.pdf', 'https://example.com/certificate2.pdf'],
    isArray: true
  })
  @Field(() => [String], { nullable: true })
  @IsArray()
  @IsOptional()
  certificates?: string[];

  @ApiPropertyOptional({
    description: 'Years of experience',
    example: 5,
    minimum: 0
  })
  @Field(() => Int, { nullable: true })
  @IsInt()
  @Min(0)
  @IsOptional()
  experienceYears?: number;

  @ApiPropertyOptional({
    description: 'Array of specialties',
    example: ['Wedding dresses', 'Formal suits', 'Alterations'],
    isArray: true
  })
  @Field(() => [String], { nullable: true })
  @IsArray()
  @IsOptional()
  specialties?: string[];

  @ApiPropertyOptional({
    description: 'Verification status',
    example: false,
    default: false
  })
  @Field(() => Boolean, { nullable: true, defaultValue: false })
  @IsBoolean()
  @IsOptional()
  isVerified?: boolean;
}
