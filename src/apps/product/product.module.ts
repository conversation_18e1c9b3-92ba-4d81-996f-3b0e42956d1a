import { Module } from '@nestjs/common';
import { ProductService } from './services/product.service';
import { ProductController } from './controllers/product.controller';
import { ProductResolver } from './resolvers/product.resolver';
import { CoreModule } from '@core/core.module';
import { SharedModule } from '@shared/shared.module';
import { MongoDbModule } from '@infra/mongodb/mongodb.module';
import { WebsocketModule } from '@infra/websocket/websocket.module';
import { RabbitMQModule } from '@infra/rabbitmq/rabbitmq.module';

@Module({
  imports: [
    CoreModule, 
    SharedModule,
    MongoDbModule,
    WebsocketModule,
    RabbitMQModule,
  ],
  providers: [ProductService, ProductResolver],
  controllers: [ProductController],
  exports: [ProductService],
})
export class ProductModule {}
