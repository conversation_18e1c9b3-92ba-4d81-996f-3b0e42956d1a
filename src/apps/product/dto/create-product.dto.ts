import { InputType, Field } from '@nestjs/graphql';
import { IsNotEmpty, IsString, IsNumber, Min, IsOptional, IsArray, IsBoolean } from 'class-validator';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

@InputType()
export class CreateProductDto {
  @ApiProperty({
    description: 'Product name',
    example: 'Premium Cotton T-Shirt',
    minLength: 1,
    maxLength: 255,
  })
  @Field()
  @IsString()
  @IsNotEmpty({ message: 'Product name is required' })
  name: string;

  @ApiProperty({
    description: 'Detailed product description',
    example: 'High-quality cotton t-shirt with comfortable fit and durable fabric',
    minLength: 1,
    maxLength: 1000,
  })
  @Field()
  @IsString()
  @IsNotEmpty({ message: 'Product description is required' })
  description: string;

  @ApiProperty({
    description: 'Stock Keeping Unit - unique product identifier',
    example: 'TSH-COT-001',
    minLength: 1,
    maxLength: 100,
  })
  @Field()
  @IsString()
  @IsNotEmpty({ message: 'SKU is required' })
  sku: string;

  @ApiProperty({
    description: 'Product price in decimal format',
    example: '29.99',
    pattern: '^\\d+(\\.\\d{1,2})?$',
  })
  @Field(() => String)
  @IsNotEmpty({ message: 'Price is required' })
  price: string;

  @ApiPropertyOptional({
    description: 'Discounted price if applicable',
    example: '24.99',
    pattern: '^\\d+(\\.\\d{1,2})?$',
  })
  @Field(() => String, { nullable: true })
  @IsOptional()
  discountedPrice?: string;

  @ApiProperty({
    description: 'Available quantity in stock',
    example: 100,
    minimum: 0,
    type: 'integer',
  })
  @Field(() => Number)
  @IsNumber()
  @Min(0, { message: 'Quantity cannot be negative' })
  @IsNotEmpty({ message: 'Quantity is required' })
  quantity: number;

  @ApiPropertyOptional({
    description: 'Product category',
    example: 'Clothing',
    maxLength: 100,
  })
  @Field({ nullable: true })
  @IsString()
  @IsOptional()
  category?: string;

  @ApiPropertyOptional({
    description: 'Product tags for categorization and search',
    example: ['cotton', 'casual', 'summer'],
    type: [String],
  })
  @Field(() => [String], { nullable: true })
  @IsArray()
  @IsOptional()
  tags?: string[];

  @ApiPropertyOptional({
    description: 'Product image URLs',
    example: ['https://example.com/image1.jpg', 'https://example.com/image2.jpg'],
    type: [String],
  })
  @Field(() => [String], { nullable: true })
  @IsArray()
  @IsOptional()
  images?: string[];

  @ApiPropertyOptional({
    description: 'Whether the product is active and available for sale',
    example: true,
    default: true,
  })
  @Field(() => Boolean, { defaultValue: true })
  @IsBoolean()
  @IsOptional()
  isActive?: boolean;
}
