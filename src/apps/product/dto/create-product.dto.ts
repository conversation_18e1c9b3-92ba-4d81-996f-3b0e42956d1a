import { InputType, Field } from '@nestjs/graphql';
import { IsNotEmpty, IsString, IsNumber, Min, IsOptional, IsArray, IsBoolean } from 'class-validator';

@InputType()
export class CreateProductDto {
  @Field()
  @IsString()
  @IsNotEmpty({ message: 'Product name is required' })
  name: string;

  @Field()
  @IsString()
  @IsNotEmpty({ message: 'Product description is required' })
  description: string;

  @Field()
  @IsString()
  @IsNotEmpty({ message: 'SKU is required' })
  sku: string;

  @Field(() => String)
  @IsNotEmpty({ message: 'Price is required' })
  price: string;

  @Field(() => String, { nullable: true })
  @IsOptional()
  discountedPrice?: string;

  @Field(() => Number)
  @IsNumber()
  @Min(0, { message: 'Quantity cannot be negative' })
  @IsNotEmpty({ message: 'Quantity is required' })
  quantity: number;

  @Field({ nullable: true })
  @IsString()
  @IsOptional()
  category?: string;

  @Field(() => [String], { nullable: true })
  @IsArray()
  @IsOptional()
  tags?: string[];

  @Field(() => [String], { nullable: true })
  @IsArray()
  @IsOptional()
  images?: string[];

  @Field(() => Boolean, { defaultValue: true })
  @IsBoolean()
  @IsOptional()
  isActive?: boolean;
}
