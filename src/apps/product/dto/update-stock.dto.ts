import { ApiProperty } from '@nestjs/swagger';
import { IsN<PERSON><PERSON>, Min, IsOptional, IsString } from 'class-validator';

export class UpdateStockDto {
  @ApiProperty({
    description: 'New stock quantity',
    example: 50,
    type: 'integer',
    minimum: 0,
  })
  @IsNumber({}, { message: 'Quantity must be a number' })
  @Min(0, { message: 'Quantity cannot be negative' })
  quantity: number;

  @ApiProperty({
    description: 'Reason for stock update',
    example: 'Inventory restock',
    required: false,
    maxLength: 255,
  })
  @IsString()
  @IsOptional()
  reason?: string;
}
