import { ApiProperty } from '@nestjs/swagger';
import { IsDate, IsNumber, IsOptional, IsString, IsUUID } from 'class-validator';

export class ProductDto {
  @ApiProperty({
    description: 'Unique identifier of the product',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @IsUUID()
  id: string;

  @ApiProperty({
    description: 'Name of the product',
    example: 'Premium Cotton Shirt',
  })
  @IsString()
  name: string;

  @ApiProperty({
    description: 'Detailed description of the product',
    example: 'High-quality cotton shirt with premium stitching',
  })
  @IsString()
  description: string;

  @ApiProperty({
    description: 'Price of the product',
    example: 59.99,
  })
  @IsNumber()
  price: number;

  @ApiProperty({
    description: 'Current stock quantity',
    example: 100,
  })
  @IsNumber()
  quantity: number;
  
  @ApiProperty({
    description: 'Current stock quantity (alias for quantity)',
    example: 100,
  })
  @IsNumber()
  get stockQuantity(): number {
    return this.quantity;
  }
  
  set stockQuantity(value: number) {
    this.quantity = value;
  }

  @ApiProperty({
    description: 'SKU (Stock Keeping Unit) code',
    example: 'SHIRT-PREM-001',
  })
  @IsString()
  sku: string;

  @ApiProperty({
    description: 'Category of the product',
    example: 'Clothing',
  })
  @IsString()
  category: string;

  @ApiProperty({
    description: 'Date when the product was created',
    example: '2023-01-15T08:30:00Z',
  })
  @IsDate()
  createdAt: Date;

  @ApiProperty({
    description: 'Date when the product was last updated',
    example: '2023-02-20T10:15:00Z',
  })
  @IsDate()
  updatedAt: Date;

  @ApiProperty({
    description: 'Optional image URL of the product',
    example: 'https://example.com/images/shirt.jpg',
    required: false,
  })
  @IsString()
  @IsOptional()
  imageUrl?: string;
}
