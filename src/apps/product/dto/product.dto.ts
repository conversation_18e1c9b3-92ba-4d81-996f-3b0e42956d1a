import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsDate, IsNumber, IsOptional, IsString, IsUUID, IsBoolean, IsArray } from 'class-validator';

export class ProductDto {
  @ApiProperty({
    description: 'Unique identifier of the product',
    example: '123e4567-e89b-12d3-a456-************',
    format: 'uuid',
  })
  @IsUUID()
  id: string;

  @ApiProperty({
    description: 'Name of the product',
    example: 'Premium Cotton T-Shirt',
    maxLength: 255,
  })
  @IsString()
  name: string;

  @ApiProperty({
    description: 'Detailed description of the product',
    example: 'High-quality cotton t-shirt with comfortable fit and durable fabric',
    maxLength: 1000,
  })
  @IsString()
  description: string;

  @ApiProperty({
    description: 'SKU (Stock Keeping Unit) - unique product identifier',
    example: 'TSH-COT-001',
    maxLength: 100,
  })
  @IsString()
  sku: string;

  @ApiProperty({
    description: 'Product price in decimal format',
    example: '29.99',
    type: 'string',
    pattern: '^\\d+(\\.\\d{1,2})?$',
  })
  @IsString()
  price: string;

  @ApiPropertyOptional({
    description: 'Discounted price if applicable',
    example: '24.99',
    type: 'string',
    pattern: '^\\d+(\\.\\d{1,2})?$',
  })
  @IsString()
  @IsOptional()
  discountedPrice?: string;

  @ApiProperty({
    description: 'Available quantity in stock',
    example: 100,
    minimum: 0,
    type: 'integer',
  })
  @IsNumber()
  quantity: number;

  @ApiProperty({
    description: 'Current stock quantity (alias for quantity)',
    example: 100,
    minimum: 0,
    type: 'integer',
  })
  @IsNumber()
  get stockQuantity(): number {
    return this.quantity;
  }

  set stockQuantity(value: number) {
    this.quantity = value;
  }

  @ApiPropertyOptional({
    description: 'Product category',
    example: 'Clothing',
    maxLength: 100,
  })
  @IsString()
  @IsOptional()
  category?: string;

  @ApiPropertyOptional({
    description: 'Product tags for categorization and search',
    example: ['cotton', 'casual', 'summer'],
    type: [String],
  })
  @IsArray()
  @IsOptional()
  tags?: string[];

  @ApiPropertyOptional({
    description: 'Product image URLs',
    example: ['https://example.com/image1.jpg', 'https://example.com/image2.jpg'],
    type: [String],
  })
  @IsArray()
  @IsOptional()
  images?: string[];

  @ApiProperty({
    description: 'Whether the product is active and available for sale',
    example: true,
    default: true,
  })
  @IsBoolean()
  isActive: boolean;

  @ApiProperty({
    description: 'Date when the product was created',
    example: '2023-01-15T08:30:00Z',
    format: 'date-time',
  })
  @IsDate()
  createdAt: Date;

  @ApiProperty({
    description: 'Date when the product was last updated',
    example: '2023-02-20T10:15:00Z',
    format: 'date-time',
  })
  @IsDate()
  updatedAt: Date;

  @ApiPropertyOptional({
    description: 'Optional image URL of the product (deprecated - use images array)',
    example: 'https://example.com/images/shirt.jpg',
    deprecated: true,
  })
  @IsString()
  @IsOptional()
  imageUrl?: string;
}
