import { Resolver, Query, Mutation, Args, ID } from '@nestjs/graphql';
import { UseGuards } from '@nestjs/common';
import { ProductService } from '../services/product.service';
import { Product, ProductCategory } from '../entities/product.entity';
import { CreateProductDto } from '../dto/create-product.dto';
import { UpdateProductDto } from '../dto/update-product.dto';
import { JwtAuthGuard } from '../../../core/auth/guards/jwt-auth.guard';
import { RolesGuard } from '../../../core/auth/guards/roles.guard';
import { Roles } from '../../../core/auth/decorators/roles.decorator';

@Resolver(() => Product)
export class ProductResolver {
  constructor(private readonly productService: ProductService) {}

  @Mutation(() => Product)
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles('admin')
  createProduct(@Args('createProductInput') createProductDto: CreateProductDto) {
    return this.productService.create(createProductDto);
  }

  @Query(() => [Product], { name: 'products' })
  findAllProducts() {
    return this.productService.findAll();
  }

  @Query(() => Product, { name: 'product' })
  findOneProduct(@Args('id', { type: () => ID }) id: string) {
    return this.productService.findOne(id);
  }

  @Query(() => [Product], { name: 'productsByCategory' })
  findProductsByCategory(@Args('category') category: string) {
    return this.productService.findByCategory(category);
  }

  @Mutation(() => Product)
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles('admin')
  updateProduct(
    @Args('id', { type: () => ID }) id: string,
    @Args('updateProductInput') updateProductDto: UpdateProductDto,
  ) {
    return this.productService.update(id, updateProductDto);
  }

  @Mutation(() => Product)
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles('admin')
  removeProduct(@Args('id', { type: () => ID }) id: string) {
    return this.productService.remove(id);
  }

  @Mutation(() => Product)
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles('admin', 'inventory')
  updateProductStock(
    @Args('id', { type: () => ID }) id: string,
    @Args('quantity') quantity: number,
  ) {
    return this.productService.updateStock(id, quantity);
  }
}
