import { Injectable, NotFoundException, ConflictException } from '@nestjs/common';
import { PrismaService } from '@core/prisma/prisma.service';
import { CreateProductDto } from '../dto/create-product.dto';
import { UpdateProductDto } from '../dto/update-product.dto';
import { ProductDto } from '../dto/product.dto';
import { MongoDbService } from '@infra/mongodb/mongodb.service';
import { RabbitMQService } from '@infra/rabbitmq/rabbitmq.service';
import { WebsocketService } from '@infra/websocket/websocket.service';
import { DecimalService } from '@shared/decimal/decimal.service';
import { StandardListParams, toMongoDbOptions, getPaginationMetadata } from '@shared/utils/query-params.util';
import { Decimal } from 'decimal.js';
import { plainToClass } from 'class-transformer';

@Injectable()
export class ProductService {
  constructor(
    private readonly prisma: PrismaService,
    private readonly mongoDbService: MongoDbService,
    private readonly rabbitMQService: RabbitMQService,
    private readonly websocketService: WebsocketService,
    private readonly decimalService: DecimalService,
  ) {}

  async create(createProductDto: CreateProductDto): Promise<ProductDto> {
    // Check if product with the same SKU already exists
    const existingProduct = await this.prisma.product.findUnique({
      where: { sku: createProductDto.sku },
    });

    if (existingProduct) {
      throw new ConflictException('Product with this SKU already exists');
    }

    // Convert Decimal strings to Decimal objects if necessary
    const price = this.decimalService.create(createProductDto.price.toString());
    const discountedPrice = createProductDto.discountedPrice 
      ? this.decimalService.create(createProductDto.discountedPrice.toString()) 
      : null;

    // Create product in PostgreSQL (source of truth)
    const newProduct = await this.prisma.product.create({
      data: {
        name: createProductDto.name,
        description: createProductDto.description,
        sku: createProductDto.sku,
        price: price.toString(),
        discountedPrice: discountedPrice?.toString(),
        quantity: createProductDto.quantity,
        category: createProductDto.category,
        tags: createProductDto.tags,
        images: createProductDto.images,
        isActive: createProductDto.isActive ?? true,
      },
    });

    // Sync to MongoDB for fast reads
    await this.syncProductToMongo(newProduct);

    // Notify about new product creation
    await this.notifyProductCreation(newProduct);

    // Map to ProductDto
    return this.mapToProductDto(newProduct);
  }

  async findAll(params: StandardListParams = {}): Promise<{ data: ProductDto[]; total: number; page: number; limit: number }> {
    // Create query based on filters
    const query: Record<string, any> = {};
    
    // Apply where conditions if provided
    if (params.where) {
      Object.assign(query, params.where);
    }
    
    // Apply text search if provided
    if (params.search) {
      query.$or = [
        { name: { $regex: params.search, $options: 'i' } },
        { description: { $regex: params.search, $options: 'i' } },
        { sku: { $regex: params.search, $options: 'i' } },
        { category: { $regex: params.search, $options: 'i' } },
      ];
    }
    
    // Get total count of matching documents
    const totalCount = await this.mongoDbService.count('products', query);
    
    // Apply sorting, pagination and get products
    const options = toMongoDbOptions(params);
    const products = await this.mongoDbService.find('products', query, options);
    
    // Map products to DTOs
    const productDtos = products.map(product => this.mapToProductDto(product));
    
    const pagination = getPaginationMetadata(params, totalCount);
    
    // Return data with pagination metadata
    return {
      data: productDtos,
      total: pagination.totalCount,
      page: pagination.page,
      limit: pagination.limit
    };
  }

  async findOne(id: string): Promise<ProductDto> {
    // Read from MongoDB for better performance
    const product = await this.mongoDbService.findOne('products', { id });
    
    if (!product) {
      throw new NotFoundException(`Product with ID ${id} not found`);
    }
    
    // Map to ProductDto
    return this.mapToProductDto(product);
  }

  async findByCategory(category: string): Promise<ProductDto[]> {
    // Read from MongoDB for better performance
    const products = await this.mongoDbService.find('products', { category });
    // Map to ProductDto array
    return products.map(product => this.mapToProductDto(product));
  }

  async update(id: string, updateProductDto: UpdateProductDto): Promise<ProductDto> {
    // Check if product exists
    const product = await this.prisma.product.findUnique({
      where: { id },
    });
    
    if (!product) {
      throw new NotFoundException(`Product with ID ${id} not found`);
    }
    
    // Prepare update data
    const updateData: any = {};
    
    // Only update fields that are provided
    if (updateProductDto.name !== undefined) updateData.name = updateProductDto.name;
    if (updateProductDto.description !== undefined) updateData.description = updateProductDto.description;
    if (updateProductDto.category !== undefined) updateData.category = updateProductDto.category;
    if (updateProductDto.tags !== undefined) updateData.tags = updateProductDto.tags;
    if (updateProductDto.images !== undefined) updateData.images = updateProductDto.images;
    if (updateProductDto.isActive !== undefined) updateData.isActive = updateProductDto.isActive;
    
    // Handle decimal fields with proper conversion
    if (updateProductDto.price !== undefined) {
      const decimalPrice = this.decimalService.create(updateProductDto.price.toString()) as Decimal;
      updateData.price = decimalPrice.toString();
    }
    
    if (updateProductDto.discountedPrice !== undefined) {
      const decimalDiscountedPrice = this.decimalService.create(updateProductDto.discountedPrice.toString()) as Decimal;
      updateData.discountedPrice = decimalDiscountedPrice.toString();
    }
    
    // Update product in PostgreSQL
    const updatedProduct = await this.prisma.product.update({
      where: { id },
      data: updateData,
    });
    
    // Sync to MongoDB for fast reads
    await this.syncProductToMongo(updatedProduct);
    
    // Notify about product update
    await this.notifyProductUpdate(updatedProduct);
    
    // Map to ProductDto
    return this.mapToProductDto(updatedProduct);
  }

  async remove(id: string) {
    // Check if product exists
    const product = await this.prisma.product.findUnique({
      where: { id },
    });
    
    if (!product) {
      throw new NotFoundException(`Product with ID ${id} not found`);
    }
    
    // Delete product from PostgreSQL
    await this.prisma.product.delete({
      where: { id },
    });
    
    // Delete from MongoDB
    await this.mongoDbService.deleteDocument('products', id);
    
    // Notify about product deletion
    await this.notifyProductDeletion(id, product.name);
    
    return { id };
  }

  async updateStock(id: string, quantity: number): Promise<ProductDto> {
    // Check if product exists
    const product = await this.prisma.product.findUnique({
      where: { id },
    });
    
    if (!product) {
      throw new NotFoundException(`Product with ID ${id} not found`);
    }
    
    // Update stock in PostgreSQL
    const updatedProduct = await this.prisma.product.update({
      where: { id },
      data: {
        quantity: {
          increment: quantity,
        },
      },
    });
    
    // Sync to MongoDB for fast reads
    await this.syncProductToMongo(updatedProduct);
    
    // Notify about stock update
    await this.notifyStockUpdate(updatedProduct);
    
    // Map to ProductDto
    return this.mapToProductDto(updatedProduct);
  }

  // Private helper methods
  private async syncProductToMongo(product: any) {
    await this.mongoDbService.syncDocument('products', product);
  }

  private async notifyProductCreation(product: any) {
    // Send message to RabbitMQ for event processing
    await this.rabbitMQService.publish({
      type: 'product_created',
      data: product,
    });

    // Notify admin users via WebSocket
    this.websocketService.sendToRole('admin', 'product_created', {
      message: `New product created: ${product.name}`,
      product,
    });

    // Update dashboard
    this.websocketService.sendDashboardUpdate({
      type: 'product_count',
      data: await this.mongoDbService.count('products', {}),
    });
  }

  private async notifyProductUpdate(product: any) {
    // Send message to RabbitMQ for event processing
    await this.rabbitMQService.publish({
      type: 'product_updated',
      data: product,
    });

    // Notify admin users via WebSocket
    this.websocketService.sendToRole('admin', 'product_updated', {
      message: `Product updated: ${product.name}`,
      product,
    });
  }

  private async notifyProductDeletion(id: string, name: string) {
    // Send message to RabbitMQ for event processing
    await this.rabbitMQService.publish({
      type: 'product_deleted',
      data: { id, name },
    });

    // Notify admin users via WebSocket
    this.websocketService.sendToRole('admin', 'product_deleted', {
      message: `Product deleted: ${name}`,
      productId: id,
      productName: name,
    });

    // Update dashboard
    this.websocketService.sendDashboardUpdate({
      type: 'product_count',
      data: await this.mongoDbService.count('products', {}),
    });
  }

  private async notifyStockUpdate(product: any) {
    // Send message to RabbitMQ for event processing
    await this.rabbitMQService.publish({
      type: 'stock_updated',
      data: {
        productId: product.id,
        productName: product.name,
        quantity: product.quantity,
      },
    });

    // Notify admin users via WebSocket
    this.websocketService.sendToRole('admin', 'stock_updated', {
      message: `Stock updated for: ${product.name}`,
      product,
    });
  }
  
  /**
   * Maps a database product object to a ProductDto
   */
  private mapToProductDto(product: any): ProductDto {
    const productDto = new ProductDto();
    productDto.id = product.id;
    productDto.name = product.name;
    productDto.description = product.description;
    productDto.price = Number(product.price);
    productDto.quantity = product.quantity;
    productDto.sku = product.sku;
    productDto.category = product.category || '';
    productDto.createdAt = new Date(product.createdAt);
    productDto.updatedAt = new Date(product.updatedAt);
    productDto.imageUrl = product.images && product.images.length > 0 ? product.images[0] : undefined;
    
    return productDto;
  }
}
