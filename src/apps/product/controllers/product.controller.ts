import { Controller, Get, Post, Body, Patch, Param, Delete, UseGuards, Query, UseInterceptors } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth, ApiParam, ApiBody, ApiQuery, ApiExtraModels, getSchemaPath } from '@nestjs/swagger';
import { ProductService } from '../services/product.service';
import { CreateProductDto } from '../dto/create-product.dto';
import { UpdateProductDto } from '../dto/update-product.dto';
import { UpdateStockDto } from '../dto/update-stock.dto';
import { ProductDto } from '../dto/product.dto';
import { JwtAuthGuard } from '@core/auth/guards/jwt-auth.guard';
import { RolesGuard } from '@core/auth/guards/roles.guard';
import { Roles } from '@core/auth/decorators/roles.decorator';
import { RequirePermissions, Resources, ResourceActions, createPermission } from '@shared/decorators/permissions.decorator';
import { PermissionsGuard } from '@shared/guards/permissions.guard';
import { CacheInterceptor } from '@nestjs/cache-manager';
import { StandardListParams, parseQueryParams } from '@shared/utils/query-params.util';
import { ErrorResponseDto, UnauthorizedResponseDto, ForbiddenResponseDto, NotFoundResponseDto } from '@shared/dto/paginated-response.dto';

@ApiTags('products')
@ApiBearerAuth('JWT-auth')
@ApiExtraModels(ProductDto, ErrorResponseDto, UnauthorizedResponseDto, ForbiddenResponseDto, NotFoundResponseDto)
@Controller('products')
export class ProductController {
  constructor(private readonly productService: ProductService) {}

  @Post()
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles('admin')
  @ApiOperation({
    summary: 'Create a new product',
    description: 'Creates a new product in the system. Requires admin role.',
  })
  @ApiResponse({
    status: 201,
    description: 'Product successfully created.',
    type: ProductDto,
    example: {
      id: '123e4567-e89b-12d3-a456-************',
      name: 'Premium Cotton T-Shirt',
      description: 'High-quality cotton t-shirt with comfortable fit',
      sku: 'TSH-COT-001',
      price: '29.99',
      quantity: 100,
      category: 'Clothing',
      isActive: true,
      createdAt: '2023-01-15T08:30:00Z',
      updatedAt: '2023-01-15T08:30:00Z'
    }
  })
  @ApiResponse({
    status: 400,
    description: 'Bad Request - Invalid input data.',
    schema: {
      type: 'object',
      properties: {
        statusCode: { type: 'number', example: 400 },
        message: { type: 'array', items: { type: 'string' }, example: ['Product name is required', 'SKU is required'] },
        error: { type: 'string', example: 'Bad Request' }
      }
    }
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - Invalid or missing authentication token.',
    schema: {
      type: 'object',
      properties: {
        statusCode: { type: 'number', example: 401 },
        message: { type: 'string', example: 'Unauthorized' }
      }
    }
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Requires admin role.',
    schema: {
      type: 'object',
      properties: {
        statusCode: { type: 'number', example: 403 },
        message: { type: 'string', example: 'Forbidden resource' }
      }
    }
  })
  @ApiBody({
    type: CreateProductDto,
    description: 'Product data to create',
    examples: {
      'basic-product': {
        summary: 'Basic Product Example',
        description: 'A simple product with required fields',
        value: {
          name: 'Premium Cotton T-Shirt',
          description: 'High-quality cotton t-shirt with comfortable fit and durable fabric',
          sku: 'TSH-COT-001',
          price: '29.99',
          quantity: 100,
          category: 'Clothing',
          tags: ['cotton', 'casual', 'summer'],
          isActive: true
        }
      },
      'discounted-product': {
        summary: 'Discounted Product Example',
        description: 'A product with discount pricing',
        value: {
          name: 'Designer Jeans',
          description: 'Premium designer jeans with perfect fit',
          sku: 'JNS-DES-002',
          price: '89.99',
          discountedPrice: '69.99',
          quantity: 50,
          category: 'Clothing',
          tags: ['denim', 'designer', 'fashion'],
          images: ['https://example.com/jeans1.jpg', 'https://example.com/jeans2.jpg'],
          isActive: true
        }
      }
    }
  })
  create(@Body() createProductDto: CreateProductDto): Promise<ProductDto> {
    return this.productService.create(createProductDto);
  }

  @Get()
  @UseGuards(JwtAuthGuard, PermissionsGuard)
  @RequirePermissions(createPermission(Resources.PRODUCTS, ResourceActions.LIST))
  @UseInterceptors(CacheInterceptor)
  @ApiOperation({
    summary: 'Get all products',
    description: 'Retrieve a paginated list of all products with optional filtering, searching, and sorting.',
  })
  @ApiResponse({
    status: 200,
    description: 'Successfully retrieved products with pagination metadata.',
    schema: {
      type: 'object',
      properties: {
        data: {
          type: 'array',
          items: { $ref: getSchemaPath(ProductDto) }
        },
        total: { type: 'number', example: 150, description: 'Total number of products' },
        page: { type: 'number', example: 1, description: 'Current page number' },
        limit: { type: 'number', example: 10, description: 'Number of items per page' },
        totalPages: { type: 'number', example: 15, description: 'Total number of pages' }
      }
    },
    examples: {
      'paginated-response': {
        summary: 'Paginated Products Response',
        value: {
          data: [
            {
              id: '123e4567-e89b-12d3-a456-************',
              name: 'Premium Cotton T-Shirt',
              description: 'High-quality cotton t-shirt',
              sku: 'TSH-COT-001',
              price: '29.99',
              quantity: 100,
              category: 'Clothing',
              isActive: true,
              createdAt: '2023-01-15T08:30:00Z',
              updatedAt: '2023-01-15T08:30:00Z'
            }
          ],
          total: 150,
          page: 1,
          limit: 10,
          totalPages: 15
        }
      }
    }
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - Invalid or missing authentication token.'
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Insufficient permissions to list products.'
  })
  @ApiQuery({
    name: 'page',
    required: false,
    type: Number,
    description: 'Page number (starts from 1)',
    example: 1,
    schema: { type: 'integer', minimum: 1, default: 1 }
  })
  @ApiQuery({
    name: 'limit',
    required: false,
    type: Number,
    description: 'Items per page (max 100)',
    example: 10,
    schema: { type: 'integer', minimum: 1, maximum: 100, default: 10 }
  })
  @ApiQuery({
    name: 'where',
    required: false,
    type: String,
    description: 'JSON filtering conditions (e.g., {"category": "Clothing", "isActive": true})',
    example: '{"category": "Clothing", "isActive": true}'
  })
  @ApiQuery({
    name: 'search',
    required: false,
    type: String,
    description: 'Text search parameter (searches in name, description, and SKU)',
    example: 'cotton shirt'
  })
  @ApiQuery({
    name: 'order',
    required: false,
    type: String,
    description: 'Sort order specification (e.g., {"createdAt": "desc", "name": "asc"})',
    example: '{"createdAt": "desc"}'
  })
  findAll(@Query() query: Record<string, any>): Promise<{ data: ProductDto[]; total: number; page: number; limit: number }> {
    const params: StandardListParams = parseQueryParams(query);
    return this.productService.findAll(params);
  }

  @Get('category/:category')
  @ApiOperation({ summary: 'Get products by category' })
  @ApiParam({ name: 'category', description: 'Product category name' })
  @ApiResponse({ status: 200, description: 'Return products in the specified category.', type: [ProductDto] })
  findByCategory(@Param('category') category: string): Promise<ProductDto[]> {
    return this.productService.findByCategory(category);
  }

  @Get(':id')
  @ApiOperation({ summary: 'Get a product by ID' })
  @ApiParam({ name: 'id', description: 'Product ID' })
  @ApiResponse({ status: 200, description: 'Return the product.', type: ProductDto })
  @ApiResponse({ status: 404, description: 'Product not found.' })
  findOne(@Param('id') id: string): Promise<ProductDto> {
    return this.productService.findOne(id);
  }

  @Patch(':id')
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles('admin')
  @ApiOperation({ summary: 'Update a product' })
  @ApiParam({ name: 'id', description: 'Product ID' })
  @ApiBody({ type: UpdateProductDto })
  @ApiResponse({ status: 200, description: 'Product successfully updated.', type: ProductDto })
  @ApiResponse({ status: 400, description: 'Bad Request.' })
  @ApiResponse({ status: 401, description: 'Unauthorized.' })
  @ApiResponse({ status: 403, description: 'Forbidden. Requires admin role.' })
  @ApiResponse({ status: 404, description: 'Product not found.' })
  update(@Param('id') id: string, @Body() updateProductDto: UpdateProductDto): Promise<ProductDto> {
    return this.productService.update(id, updateProductDto);
  }

  @Delete(':id')
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles('admin')
  @ApiOperation({ summary: 'Delete a product' })
  @ApiParam({ name: 'id', description: 'Product ID' })
  @ApiResponse({ status: 200, description: 'Product successfully deleted.' })
  @ApiResponse({ status: 401, description: 'Unauthorized.' })
  @ApiResponse({ status: 403, description: 'Forbidden. Requires admin role.' })
  @ApiResponse({ status: 404, description: 'Product not found.' })
  remove(@Param('id') id: string) {
    return this.productService.remove(id);
  }

  @Patch(':id/stock')
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles('admin', 'inventory')
  @ApiOperation({
    summary: 'Update product stock quantity',
    description: 'Updates the stock quantity for a specific product. Requires admin or inventory role.',
  })
  @ApiParam({
    name: 'id',
    description: 'Unique identifier of the product',
    format: 'uuid',
    example: '123e4567-e89b-12d3-a456-************'
  })
  @ApiBody({
    type: UpdateStockDto,
    description: 'Stock update data',
    examples: {
      'restock': {
        summary: 'Restock Example',
        description: 'Adding inventory to existing stock',
        value: {
          quantity: 50,
          reason: 'Inventory restock from supplier'
        }
      },
      'adjustment': {
        summary: 'Stock Adjustment',
        description: 'Adjusting stock due to inventory count',
        value: {
          quantity: 25,
          reason: 'Inventory count adjustment'
        }
      }
    }
  })
  @ApiResponse({
    status: 200,
    description: 'Product stock successfully updated.',
    type: ProductDto,
    example: {
      id: '123e4567-e89b-12d3-a456-************',
      name: 'Premium Cotton T-Shirt',
      sku: 'TSH-COT-001',
      quantity: 50,
      updatedAt: '2023-01-15T08:30:00Z'
    }
  })
  @ApiResponse({
    status: 400,
    description: 'Bad Request - Invalid stock quantity or data.',
    type: ErrorResponseDto
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - Invalid or missing authentication token.',
    type: UnauthorizedResponseDto
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - Requires admin or inventory role.',
    type: ForbiddenResponseDto
  })
  @ApiResponse({
    status: 404,
    description: 'Product not found.',
    type: NotFoundResponseDto
  })
  updateStock(
    @Param('id') id: string,
    @Body() updateStockDto: UpdateStockDto,
  ): Promise<ProductDto> {
    return this.productService.updateStock(id, updateStockDto.quantity);
  }
}
