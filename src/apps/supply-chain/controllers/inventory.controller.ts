import {
  Controller,
  Get,
  Post,
  Put,
  Body,
  Param,
  Query,
  UseGuards,
  Request,
  HttpStatus,
  ParseIntPipe,
  DefaultValuePipe,
  ParseBoolPipe,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiQuery,
  ApiBearerAuth,
  ApiParam,
} from '@nestjs/swagger';
import { JwtAuthGuard } from '@core/auth/guards/jwt-auth.guard';
import { RolesGuard } from '@core/auth/guards/roles.guard';
import { Roles } from '@core/auth/decorators/roles.decorator';
import { InventoryService } from '../services/inventory.service';
import { 
  CreateInventoryCategoryDto,
  UpdateInventoryCategoryDto,
  CreateInventoryItemDto,
  UpdateInventoryItemDto,
  CreateInventoryTransactionDto,
  AdjustInventoryDto,
  InventoryItemType,
  InventoryStatus
} from '../dto/inventory.dto';

@ApiTags('Supply Chain - Inventory')
@ApiBearerAuth()
@UseGuards(JwtAuthGuard, RolesGuard)
@Controller('supply-chain/inventory')
export class InventoryController {
  constructor(private readonly inventoryService: InventoryService) {}

  // ===== INVENTORY CATEGORIES =====

  @Post('categories')
  @Roles('admin', 'inventory_manager')
  @ApiOperation({
    summary: 'Create inventory category',
    description: 'Create a new inventory category for organizing items.',
  })
  @ApiResponse({
    status: HttpStatus.CREATED,
    description: 'Category created successfully',
  })
  @ApiResponse({
    status: HttpStatus.CONFLICT,
    description: 'Category with name already exists',
  })
  async createCategory(@Body() createCategoryDto: CreateInventoryCategoryDto): Promise<any> {
    return this.inventoryService.createCategory(createCategoryDto);
  }

  @Get('categories')
  @Roles('admin', 'inventory_manager', 'purchase_manager', 'tailor')
  @ApiOperation({
    summary: 'Get all inventory categories',
    description: 'Retrieve all inventory categories with hierarchical structure.',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Categories retrieved successfully',
  })
  async getCategories(): Promise<any[]> {
    return this.inventoryService.getCategories();
  }

  @Put('categories/:id')
  @Roles('admin', 'inventory_manager')
  @ApiOperation({
    summary: 'Update inventory category',
    description: 'Update an existing inventory category.',
  })
  @ApiParam({
    name: 'id',
    description: 'Category ID',
    example: '123e4567-e89b-12d3-a456-426614174000',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Category updated successfully',
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Category not found',
  })
  async updateCategory(
    @Param('id') id: string,
    @Body() updateCategoryDto: UpdateInventoryCategoryDto,
  ): Promise<any> {
    return this.inventoryService.updateCategory(id, updateCategoryDto);
  }

  // ===== INVENTORY ITEMS =====

  @Post('items')
  @Roles('admin', 'inventory_manager')
  @ApiOperation({
    summary: 'Create inventory item',
    description: 'Add a new item to the inventory system.',
  })
  @ApiResponse({
    status: HttpStatus.CREATED,
    description: 'Inventory item created successfully',
  })
  @ApiResponse({
    status: HttpStatus.CONFLICT,
    description: 'Item with code already exists',
  })
  async createInventoryItem(
    @Body() createItemDto: CreateInventoryItemDto,
    @Request() req: any,
  ): Promise<any> {
    return this.inventoryService.createInventoryItem(createItemDto, req.user.id);
  }

  @Get('items')
  @Roles('admin', 'inventory_manager', 'purchase_manager', 'tailor')
  @ApiOperation({
    summary: 'Get inventory items',
    description: 'Retrieve a paginated list of inventory items with filtering options.',
  })
  @ApiQuery({
    name: 'categoryId',
    required: false,
    description: 'Filter by category ID',
  })
  @ApiQuery({
    name: 'itemType',
    required: false,
    enum: InventoryItemType,
    description: 'Filter by item type',
  })
  @ApiQuery({
    name: 'status',
    required: false,
    enum: InventoryStatus,
    description: 'Filter by item status',
  })
  @ApiQuery({
    name: 'lowStock',
    required: false,
    type: Boolean,
    description: 'Filter items with low stock',
  })
  @ApiQuery({
    name: 'search',
    required: false,
    description: 'Search in item name, code, or description',
  })
  @ApiQuery({
    name: 'page',
    required: false,
    type: Number,
    description: 'Page number (default: 1)',
  })
  @ApiQuery({
    name: 'limit',
    required: false,
    type: Number,
    description: 'Items per page (default: 20)',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Inventory items retrieved successfully',
  })
  async getInventoryItems(
    @Query('categoryId') categoryId?: string,
    @Query('itemType') itemType?: InventoryItemType,
    @Query('status') status?: InventoryStatus,
    @Query('lowStock', new DefaultValuePipe(false), ParseBoolPipe) lowStock?: boolean,
    @Query('search') search?: string,
    @Query('page', new DefaultValuePipe(1), ParseIntPipe) page = 1,
    @Query('limit', new DefaultValuePipe(20), ParseIntPipe) limit = 20,
  ): Promise<{
    items: any[];
    total: number;
    page: number;
    totalPages: number;
  }> {
    return this.inventoryService.getInventoryItems(
      categoryId,
      itemType,
      status,
      lowStock,
      search,
      page,
      limit,
    );
  }

  @Get('items/:id')
  @Roles('admin', 'inventory_manager', 'purchase_manager', 'tailor')
  @ApiOperation({
    summary: 'Get inventory item by ID',
    description: 'Retrieve detailed information about a specific inventory item.',
  })
  @ApiParam({
    name: 'id',
    description: 'Inventory item ID',
    example: '123e4567-e89b-12d3-a456-426614174000',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Inventory item retrieved successfully',
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Inventory item not found',
  })
  async getInventoryItemById(@Param('id') id: string): Promise<any> {
    return this.inventoryService.getInventoryItemById(id);
  }

  @Put('items/:id')
  @Roles('admin', 'inventory_manager')
  @ApiOperation({
    summary: 'Update inventory item',
    description: 'Update an existing inventory item.',
  })
  @ApiParam({
    name: 'id',
    description: 'Inventory item ID',
    example: '123e4567-e89b-12d3-a456-426614174000',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Inventory item updated successfully',
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Inventory item not found',
  })
  async updateInventoryItem(
    @Param('id') id: string,
    @Body() updateItemDto: UpdateInventoryItemDto,
    @Request() req: any,
  ): Promise<any> {
    return this.inventoryService.updateInventoryItem(id, updateItemDto, req.user.id);
  }

  // ===== INVENTORY TRANSACTIONS =====

  @Post('transactions')
  @Roles('admin', 'inventory_manager')
  @ApiOperation({
    summary: 'Create inventory transaction',
    description: 'Record an inventory transaction (purchase, sale, adjustment, etc.).',
  })
  @ApiResponse({
    status: HttpStatus.CREATED,
    description: 'Inventory transaction created successfully',
  })
  @ApiResponse({
    status: HttpStatus.BAD_REQUEST,
    description: 'Insufficient stock for transaction',
  })
  async createInventoryTransaction(
    @Body() createTransactionDto: CreateInventoryTransactionDto,
    @Request() req: any,
  ): Promise<any> {
    return this.inventoryService.createInventoryTransaction(createTransactionDto, req.user.id);
  }

  @Post('adjust')
  @Roles('admin', 'inventory_manager')
  @ApiOperation({
    summary: 'Adjust inventory stock',
    description: 'Manually adjust inventory stock levels (e.g., after physical count).',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Inventory adjusted successfully',
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Inventory item not found',
  })
  @ApiResponse({
    status: HttpStatus.BAD_REQUEST,
    description: 'No adjustment needed',
  })
  async adjustInventory(
    @Body() adjustInventoryDto: AdjustInventoryDto,
    @Request() req: any,
  ): Promise<any> {
    return this.inventoryService.adjustInventory(adjustInventoryDto, req.user.id);
  }

  // ===== INVENTORY ALERTS AND REPORTS =====

  @Get('alerts/low-stock')
  @Roles('admin', 'inventory_manager', 'purchase_manager')
  @ApiOperation({
    summary: 'Get low stock alerts',
    description: 'Retrieve items that are running low on stock.',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Low stock items retrieved successfully',
  })
  async getLowStockItems(): Promise<any> {
    const result = await this.inventoryService.getInventoryItems(
      undefined,
      undefined,
      InventoryStatus.ACTIVE,
      true, // lowStock = true
      undefined,
      1,
      1000, // Get all low stock items
    );

    return {
      items: result.items,
      count: result.total,
    };
  }

  @Get('reports/summary')
  @Roles('admin', 'inventory_manager')
  @ApiOperation({
    summary: 'Get inventory summary report',
    description: 'Get a summary of inventory status including totals, values, and alerts.',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Inventory summary retrieved successfully',
  })
  async getInventorySummary(): Promise<any> {
    const [
      totalItems,
      activeItems,
      lowStockItems,
      outOfStockItems,
    ] = await Promise.all([
      this.inventoryService.getInventoryItems(undefined, undefined, undefined, false, undefined, 1, 1),
      this.inventoryService.getInventoryItems(undefined, undefined, InventoryStatus.ACTIVE, false, undefined, 1, 1),
      this.inventoryService.getInventoryItems(undefined, undefined, InventoryStatus.ACTIVE, true, undefined, 1, 1),
      this.inventoryService.getInventoryItems(undefined, undefined, InventoryStatus.ACTIVE, false, undefined, 1, 1000),
    ]);

    const outOfStock = outOfStockItems.items.filter(item => item.currentStock === 0).length;

    return {
      totalItems: totalItems.total,
      activeItems: activeItems.total,
      lowStockItems: lowStockItems.total,
      outOfStockItems: outOfStock,
      categories: await this.inventoryService.getCategories(),
    };
  }

  @Get('search/active')
  @Roles('admin', 'inventory_manager', 'purchase_manager', 'tailor')
  @ApiOperation({
    summary: 'Search active inventory items',
    description: 'Search for active inventory items for use in dropdowns and selection lists.',
  })
  @ApiQuery({
    name: 'search',
    required: false,
    description: 'Search term',
  })
  @ApiQuery({
    name: 'categoryId',
    required: false,
    description: 'Filter by category',
  })
  @ApiQuery({
    name: 'itemType',
    required: false,
    enum: InventoryItemType,
    description: 'Filter by item type',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Active inventory items retrieved successfully',
  })
  async searchActiveItems(
    @Query('search') search?: string,
    @Query('categoryId') categoryId?: string,
    @Query('itemType') itemType?: InventoryItemType,
  ): Promise<any[]> {
    const result = await this.inventoryService.getInventoryItems(
      categoryId,
      itemType,
      InventoryStatus.ACTIVE,
      false,
      search,
      1,
      100, // Limit search results
    );

    return result.items.map(item => ({
      id: item.id,
      itemName: item.itemName,
      itemCode: item.itemCode,
      category: item.category?.name,
      itemType: item.itemType,
      unit: item.unit,
      currentStock: item.currentStock,
      availableStock: item.availableStock,
      unitCost: item.unitCost,
      currency: item.currency,
    }));
  }
}
