import {
  Controller,
  Get,
  Post,
  Put,
  Body,
  Param,
  Query,
  UseGuards,
  HttpStatus,
  UseInterceptors,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiQuery,
  ApiBearerAuth,
  ApiParam,
  ApiBody,
} from '@nestjs/swagger';
import { CacheInterceptor } from '@nestjs/cache-manager';
import { JwtAuthGuard } from '@core/auth/guards/jwt-auth.guard';
import { RolesGuard } from '@core/auth/guards/roles.guard';
import { Roles } from '@core/auth/decorators/roles.decorator';
import { CurrentUser } from '@core/auth/decorators/current-user.decorator';
import { User } from '@apps/user/entities/user.entity';
import { BaseController, SupplyChainRoles, ApiTags as Tags } from '@shared/controllers/base.controller';
import { SearchQueryDto, createStatusQueryDto, createListResponseDto } from '@shared/dto/common-query.dto';
import { IsOptional, IsEnum, IsString, IsBoolean } from 'class-validator';
import { ApiPropertyOptional } from '@nestjs/swagger';
import { InventoryService } from '../services/inventory.service';
import { 
  CreateInventoryCategoryDto,
  UpdateInventoryCategoryDto,
  CreateInventoryItemDto,
  UpdateInventoryItemDto,
  CreateInventoryTransactionDto,
  AdjustInventoryDto,
  InventoryItemType,
  InventoryStatus
} from '../dto/inventory.dto';

// Create typed query DTOs
class InventoryItemQueryDto extends createStatusQueryDto(InventoryStatus) {
  @ApiPropertyOptional({
    description: 'Filter by category ID',
    format: 'uuid',
  })
  @IsOptional()
  @IsString()
  categoryId?: string;

  @ApiPropertyOptional({
    description: 'Filter by item type',
    enum: InventoryItemType,
  })
  @IsOptional()
  @IsEnum(InventoryItemType)
  itemType?: InventoryItemType;

  @ApiPropertyOptional({
    description: 'Filter items with low stock',
    example: false,
  })
  @IsOptional()
  @IsBoolean()
  lowStock?: boolean;
}

class InventorySearchQueryDto extends SearchQueryDto {
  @ApiPropertyOptional({
    description: 'Filter by category ID',
    format: 'uuid',
  })
  @IsOptional()
  @IsString()
  categoryId?: string;

  @ApiPropertyOptional({
    description: 'Filter by item type',
    enum: InventoryItemType,
  })
  @IsOptional()
  @IsEnum(InventoryItemType)
  itemType?: InventoryItemType;
}

// Create typed response DTO
const InventoryItemListResponseDto = createListResponseDto(Object);

@ApiTags(Tags.INVENTORY)
@ApiBearerAuth('JWT-auth')
@UseGuards(JwtAuthGuard, RolesGuard)
@Controller('supply-chain/inventory')
export class InventoryController extends BaseController {
  constructor(private readonly inventoryService: InventoryService) {
    super();
  }

  // ===== INVENTORY CATEGORIES =====

  @Post('categories')
  @Roles('admin', 'inventory_manager')
  @ApiOperation({
    summary: 'Create inventory category',
    description: 'Create a new inventory category for organizing items.',
  })
  @ApiResponse({
    status: HttpStatus.CREATED,
    description: 'Category created successfully',
  })
  @ApiResponse({
    status: HttpStatus.CONFLICT,
    description: 'Category with name already exists',
  })
  async createCategory(@Body() createCategoryDto: CreateInventoryCategoryDto): Promise<any> {
    return this.inventoryService.createCategory(createCategoryDto);
  }

  @Get('categories')
  @Roles('admin', 'inventory_manager', 'purchase_manager', 'tailor')
  @ApiOperation({
    summary: 'Get all inventory categories',
    description: 'Retrieve all inventory categories with hierarchical structure.',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Categories retrieved successfully',
  })
  async getCategories(): Promise<any[]> {
    return this.inventoryService.getCategories();
  }

  @Put('categories/:id')
  @Roles('admin', 'inventory_manager')
  @ApiOperation({
    summary: 'Update inventory category',
    description: 'Update an existing inventory category.',
  })
  @ApiParam({
    name: 'id',
    description: 'Category ID',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Category updated successfully',
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Category not found',
  })
  async updateCategory(
    @Param('id') id: string,
    @Body() updateCategoryDto: UpdateInventoryCategoryDto,
  ): Promise<any> {
    return this.inventoryService.updateCategory(id, updateCategoryDto);
  }

  // ===== INVENTORY ITEMS =====

  @Post('items')
  @Roles(...SupplyChainRoles.INVENTORY_MANAGERS)
  @ApiOperation({
    summary: 'Create inventory item',
    description: 'Add a new item to the inventory system with comprehensive validation.',
  })
  @BaseController.ItemResponse(Object, HttpStatus.CREATED, 'Inventory item created successfully')
  @BaseController.CrudErrorResponses()
  @ApiBody({
    type: CreateInventoryItemDto,
    description: 'Inventory item data',
    examples: {
      'fabric': {
        summary: 'Fabric Item',
        description: 'Example of creating a fabric inventory item',
        value: {
          itemName: 'Premium Cotton Fabric',
          itemCode: 'FAB-COT-001',
          categoryId: '123e4567-e89b-12d3-a456-************',
          itemType: 'FABRIC',
          unit: 'METER',
          unitCost: '25.50',
          currency: 'USD',
          minimumStock: 100,
          maximumStock: 1000,
        },
      },
    },
  })
  async createInventoryItem(
    @Body() createItemDto: CreateInventoryItemDto,
    @CurrentUser() user: User,
  ): Promise<any> {
    return this.inventoryService.createInventoryItem(createItemDto, user.id);
  }

  @Get('items')
  @Roles(...SupplyChainRoles.READ_ONLY_USERS)
  @UseInterceptors(CacheInterceptor)
  @ApiOperation({
    summary: 'Get inventory items with filtering',
    description: 'Retrieve a paginated list of inventory items with comprehensive filtering options.',
  })
  @BaseController.ListResponse(Object)
  @BaseController.StandardErrorResponses()
  @BaseController.PaginationParams()
  @BaseController.StatusFilter(InventoryStatus, 'Filter by inventory status')
  @BaseController.SearchParam('Search inventory items by name or code')
  async getInventoryItems(
    @Query() query: InventoryItemQueryDto,
  ): Promise<InstanceType<typeof InventoryItemListResponseDto>> {
    const { page, limit } = this.createPaginationParams(query.page, query.limit);

    const result = await this.inventoryService.getInventoryItems(
      query.categoryId,
      query.itemType,
      query.status as InventoryStatus,
      query.lowStock,
      query.search,
      page,
      limit,
    );

    return this.createListResponse(result.items, result.total, page, limit);
  }

  @Get('items/:id')
  @Roles(...SupplyChainRoles.READ_ONLY_USERS)
  @UseInterceptors(CacheInterceptor)
  @ApiOperation({
    summary: 'Get inventory item by ID',
    description: 'Retrieve detailed information about a specific inventory item.',
  })
  @ApiParam({
    name: 'id',
    description: 'Inventory item unique identifier',
    format: 'uuid',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @BaseController.ItemResponse(Object)
  @BaseController.StandardErrorResponses()
  @ApiResponse({
    status: 404,
    description: 'Inventory item not found',
    schema: {
      type: 'object',
      properties: {
        statusCode: { type: 'number', example: 404 },
        message: { type: 'string', example: 'Inventory item not found' },
        error: { type: 'string', example: 'Not Found' },
      },
    },
  })
  async getInventoryItemById(@Param('id') id: string): Promise<any> {
    this.validateUuid(id, 'inventory item ID');
    return this.inventoryService.getInventoryItemById(id);
  }

  @Put('items/:id')
  @Roles(...SupplyChainRoles.INVENTORY_MANAGERS)
  @ApiOperation({
    summary: 'Update inventory item',
    description: 'Update an existing inventory item with validation and audit trail.',
  })
  @ApiParam({
    name: 'id',
    description: 'Inventory item unique identifier',
    format: 'uuid',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @BaseController.ItemResponse(Object)
  @BaseController.CrudErrorResponses()
  @ApiBody({
    type: UpdateInventoryItemDto,
    description: 'Inventory item update data',
  })
  async updateInventoryItem(
    @Param('id') id: string,
    @Body() updateItemDto: UpdateInventoryItemDto,
    @CurrentUser() user: User,
  ): Promise<any> {
    this.validateUuid(id, 'inventory item ID');
    return this.inventoryService.updateInventoryItem(id, updateItemDto, user.id);
  }

  // ===== INVENTORY TRANSACTIONS =====

  @Post('transactions')
  @Roles(...SupplyChainRoles.INVENTORY_MANAGERS)
  @ApiOperation({
    summary: 'Create inventory transaction',
    description: 'Record an inventory transaction (purchase, sale, adjustment, etc.) with comprehensive validation.',
  })
  @BaseController.ItemResponse(Object, HttpStatus.CREATED, 'Inventory transaction created successfully')
  @BaseController.CrudErrorResponses()
  @ApiBody({
    type: CreateInventoryTransactionDto,
    description: 'Inventory transaction data',
  })
  async createInventoryTransaction(
    @Body() createTransactionDto: CreateInventoryTransactionDto,
    @CurrentUser() user: User,
  ): Promise<any> {
    return this.inventoryService.createInventoryTransaction(createTransactionDto, user.id);
  }

  @Post('adjust')
  @Roles(...SupplyChainRoles.INVENTORY_MANAGERS)
  @ApiOperation({
    summary: 'Adjust inventory stock',
    description: 'Manually adjust inventory stock levels (e.g., after physical count) with audit trail.',
  })
  @BaseController.ItemResponse(Object)
  @BaseController.CrudErrorResponses()
  @ApiBody({
    type: AdjustInventoryDto,
    description: 'Inventory adjustment data',
    examples: {
      'stock-correction': {
        summary: 'Stock Correction',
        description: 'Example of correcting stock after physical count',
        value: {
          itemId: '123e4567-e89b-12d3-a456-************',
          adjustmentType: 'CORRECTION',
          quantity: 50,
          reason: 'Physical count correction',
          notes: 'Found additional stock in warehouse section B',
        },
      },
    },
  })
  async adjustInventory(
    @Body() adjustInventoryDto: AdjustInventoryDto,
    @CurrentUser() user: User,
  ): Promise<any> {
    return this.inventoryService.adjustInventory(adjustInventoryDto, user.id);
  }

  // ===== INVENTORY ALERTS AND REPORTS =====

  @Get('alerts/low-stock')
  @Roles(...SupplyChainRoles.INVENTORY_MANAGERS)
  @UseInterceptors(CacheInterceptor)
  @ApiOperation({
    summary: 'Get low stock alerts',
    description: 'Retrieve items that are running low on stock for proactive inventory management.',
  })
  @BaseController.ItemResponse(Object)
  @BaseController.StandardErrorResponses()
  async getLowStockItems(): Promise<any> {
    const result = await this.inventoryService.getInventoryItems(
      undefined,
      undefined,
      InventoryStatus.ACTIVE,
      true, // lowStock = true
      undefined,
      1,
      1000, // Get all low stock items
    );

    return {
      items: result.items,
      count: result.total,
      alertLevel: 'LOW_STOCK',
      timestamp: new Date().toISOString(),
    };
  }

  @Get('reports/summary')
  @Roles('admin', 'inventory_manager')
  @ApiOperation({
    summary: 'Get inventory summary report',
    description: 'Get a summary of inventory status including totals, values, and alerts.',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Inventory summary retrieved successfully',
  })
  async getInventorySummary(): Promise<any> {
    const [
      totalItems,
      activeItems,
      lowStockItems,
      outOfStockItems,
    ] = await Promise.all([
      this.inventoryService.getInventoryItems(undefined, undefined, undefined, false, undefined, 1, 1),
      this.inventoryService.getInventoryItems(undefined, undefined, InventoryStatus.ACTIVE, false, undefined, 1, 1),
      this.inventoryService.getInventoryItems(undefined, undefined, InventoryStatus.ACTIVE, true, undefined, 1, 1),
      this.inventoryService.getInventoryItems(undefined, undefined, InventoryStatus.ACTIVE, false, undefined, 1, 1000),
    ]);

    const outOfStock = outOfStockItems.items.filter(item => item.currentStock === 0).length;

    return {
      totalItems: totalItems.total,
      activeItems: activeItems.total,
      lowStockItems: lowStockItems.total,
      outOfStockItems: outOfStock,
      categories: await this.inventoryService.getCategories(),
    };
  }

  @Get('search/active')
  @Roles(...SupplyChainRoles.READ_ONLY_USERS)
  @UseInterceptors(CacheInterceptor)
  @ApiOperation({
    summary: 'Search active inventory items',
    description: 'Search for active inventory items for use in dropdowns and selection lists.',
  })
  @BaseController.SearchParam('Search active inventory items')
  @ApiQuery({
    name: 'categoryId',
    required: false,
    description: 'Filter by category ID',
    format: 'uuid',
  })
  @ApiQuery({
    name: 'itemType',
    required: false,
    enum: InventoryItemType,
    description: 'Filter by item type',
  })
  @ApiResponse({
    status: 200,
    description: 'Active inventory items retrieved successfully',
    schema: {
      type: 'array',
      items: {
        type: 'object',
        properties: {
          id: { type: 'string', format: 'uuid' },
          itemName: { type: 'string' },
          itemCode: { type: 'string' },
          category: { type: 'string' },
          itemType: { type: 'string' },
          unit: { type: 'string' },
          currentStock: { type: 'number' },
          availableStock: { type: 'number' },
          unitCost: { type: 'string' },
          currency: { type: 'string' },
        },
      },
    },
  })
  async searchActiveItems(@Query() query: InventorySearchQueryDto): Promise<any[]> {
    const result = await this.inventoryService.getInventoryItems(
      query.categoryId,
      query.itemType,
      InventoryStatus.ACTIVE,
      false,
      query.search,
      1,
      100, // Limit search results
    );

    return result.items
      .filter(item =>
        !query.search ||
        item.itemName.toLowerCase().includes(query.search.toLowerCase()) ||
        item.itemCode.toLowerCase().includes(query.search.toLowerCase())
      )
      .map(item => ({
        id: item.id,
        itemName: item.itemName,
        itemCode: item.itemCode,
        category: item.category?.name,
        itemType: item.itemType,
        unit: item.unit,
        currentStock: item.currentStock,
        availableStock: item.availableStock,
        unitCost: item.unitCost,
        currency: item.currency,
      }));
  }
}
