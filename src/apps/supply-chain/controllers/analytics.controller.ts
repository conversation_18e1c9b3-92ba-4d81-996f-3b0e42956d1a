import {
  Controller,
  Get,
  Query,
  UseGuards,
  HttpStatus,
  ParseIntPipe,
  DefaultValuePipe,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiQuery,
  ApiBearerAuth,
  ApiParam,
} from '@nestjs/swagger';
import { JwtAuthGuard } from '@core/auth/guards/jwt-auth.guard';
import { RolesGuard } from '@core/auth/guards/roles.guard';
import { Roles } from '@core/auth/decorators/roles.decorator';
import { SupplyChainAnalyticsService } from '../services/analytics.service';

@ApiTags('Supply Chain - Analytics')
@ApiBearerAuth()
@UseGuards(JwtAuthGuard, RolesGuard)
@Controller('supply-chain/analytics')
export class SupplyChainAnalyticsController {
  constructor(private readonly analyticsService: SupplyChainAnalyticsService) {}

  @Get('dashboard')
  @Roles('admin', 'supply_chain_manager', 'inventory_manager', 'purchase_manager')
  @ApiOperation({
    summary: 'Get supply chain dashboard',
    description: 'Retrieve comprehensive supply chain metrics and KPIs for the dashboard.',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Supply chain dashboard data retrieved successfully',
    schema: {
      type: 'object',
      properties: {
        suppliers: {
          type: 'object',
          properties: {
            total: { type: 'number' },
            active: { type: 'number' },
            verificationPending: { type: 'number' },
          },
        },
        inventory: {
          type: 'object',
          properties: {
            totalItems: { type: 'number' },
            lowStockItems: { type: 'number' },
            totalValue: { type: 'string' },
            turnoverRate: { type: 'string' },
          },
        },
        purchaseOrders: {
          type: 'object',
          properties: {
            total: { type: 'number' },
            pending: { type: 'number' },
            monthlyValue: { type: 'string' },
            averageLeadTime: { type: 'string' },
          },
        },
        performance: {
          type: 'object',
          properties: {
            supplierPerformance: { type: 'array' },
            categoryAnalysis: { type: 'array' },
            costTrends: { type: 'array' },
          },
        },
      },
    },
  })
  async getSupplyChainDashboard(): Promise<any> {
    return this.analyticsService.getSupplyChainDashboard();
  }

  @Get('suppliers/performance')
  @Roles('admin', 'supply_chain_manager', 'purchase_manager')
  @ApiOperation({
    summary: 'Get supplier performance report',
    description: 'Retrieve detailed performance metrics for suppliers.',
  })
  @ApiQuery({
    name: 'supplierId',
    required: false,
    description: 'Filter by specific supplier ID',
  })
  @ApiQuery({
    name: 'startDate',
    required: false,
    type: String,
    description: 'Start date for the report (ISO format)',
  })
  @ApiQuery({
    name: 'endDate',
    required: false,
    type: String,
    description: 'End date for the report (ISO format)',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Supplier performance report retrieved successfully',
    schema: {
      type: 'array',
      items: {
        type: 'object',
        properties: {
          supplier: {
            type: 'object',
            properties: {
              id: { type: 'string' },
              companyName: { type: 'string' },
              overallRating: { type: 'string' },
            },
          },
          totalOrders: { type: 'number' },
          totalValue: { type: 'string' },
          onTimeDeliveries: { type: 'number' },
          lateDeliveries: { type: 'number' },
          onTimeDeliveryRate: { type: 'string' },
          averageOrderValue: { type: 'string' },
        },
      },
    },
  })
  async getSupplierPerformanceReport(
    @Query('supplierId') supplierId?: string,
    @Query('startDate') startDate?: string,
    @Query('endDate') endDate?: string,
  ): Promise<any> {
    const start = startDate ? new Date(startDate) : undefined;
    const end = endDate ? new Date(endDate) : undefined;
    
    return this.analyticsService.getSupplierPerformanceReport(supplierId, start, end);
  }

  @Get('inventory/analytics')
  @Roles('admin', 'supply_chain_manager', 'inventory_manager')
  @ApiOperation({
    summary: 'Get inventory analytics',
    description: 'Retrieve comprehensive inventory analytics including turnover, costs, and recommendations.',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Inventory analytics retrieved successfully',
    schema: {
      type: 'object',
      properties: {
        categoryBreakdown: { type: 'array' },
        stockLevels: {
          type: 'object',
          properties: {
            total: { type: 'number' },
            normal: { type: 'number' },
            low: { type: 'number' },
            out: { type: 'number' },
            over: { type: 'number' },
          },
        },
        turnoverAnalysis: { type: 'array' },
        costAnalysis: {
          type: 'object',
          properties: {
            totalValue: { type: 'string' },
            categoryBreakdown: { type: 'array' },
            averageItemValue: { type: 'string' },
          },
        },
        expiryAlerts: { type: 'array' },
        recommendations: { type: 'array', items: { type: 'string' } },
      },
    },
  })
  async getInventoryAnalytics(): Promise<any> {
    return this.analyticsService.getInventoryAnalytics();
  }

  @Get('demand/forecast')
  @Roles('admin', 'supply_chain_manager', 'inventory_manager', 'purchase_manager')
  @ApiOperation({
    summary: 'Get demand forecast',
    description: 'Retrieve demand forecasting data for inventory planning.',
  })
  @ApiQuery({
    name: 'inventoryItemId',
    required: false,
    description: 'Filter by specific inventory item ID',
  })
  @ApiQuery({
    name: 'forecastPeriod',
    required: false,
    enum: ['WEEKLY', 'MONTHLY', 'QUARTERLY'],
    description: 'Forecast period (default: MONTHLY)',
  })
  @ApiQuery({
    name: 'periodsAhead',
    required: false,
    type: Number,
    description: 'Number of periods to forecast ahead (default: 3)',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Demand forecast retrieved successfully',
    schema: {
      type: 'array',
      items: {
        type: 'object',
        properties: {
          id: { type: 'string' },
          inventoryItemId: { type: 'string' },
          forecastPeriod: { type: 'string' },
          periodStart: { type: 'string', format: 'date-time' },
          periodEnd: { type: 'string', format: 'date-time' },
          predictedDemand: { type: 'number' },
          confidence: { type: 'string' },
          forecastMethod: { type: 'string' },
          inventoryItem: {
            type: 'object',
            properties: {
              id: { type: 'string' },
              itemName: { type: 'string' },
              itemCode: { type: 'string' },
              currentStock: { type: 'number' },
            },
          },
        },
      },
    },
  })
  async getDemandForecast(
    @Query('inventoryItemId') inventoryItemId?: string,
    @Query('forecastPeriod', new DefaultValuePipe('MONTHLY')) forecastPeriod = 'MONTHLY',
    @Query('periodsAhead', new DefaultValuePipe(3), ParseIntPipe) periodsAhead = 3,
  ): Promise<any> {
    return this.analyticsService.getDemandForecast(inventoryItemId, forecastPeriod, periodsAhead);
  }

  @Get('purchase-orders/analytics')
  @Roles('admin', 'supply_chain_manager', 'purchase_manager', 'finance_manager')
  @ApiOperation({
    summary: 'Get purchase order analytics',
    description: 'Retrieve comprehensive purchase order analytics and trends.',
  })
  @ApiQuery({
    name: 'startDate',
    required: false,
    type: String,
    description: 'Start date for the analysis (ISO format)',
  })
  @ApiQuery({
    name: 'endDate',
    required: false,
    type: String,
    description: 'End date for the analysis (ISO format)',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Purchase order analytics retrieved successfully',
    schema: {
      type: 'object',
      properties: {
        ordersByStatus: { type: 'array' },
        ordersBySupplier: { type: 'array' },
        monthlyTrends: { type: 'array' },
        averageOrderValue: { type: 'string' },
        leadTimeAnalysis: {
          type: 'object',
          properties: {
            average: { type: 'string' },
            minimum: { type: 'string' },
            maximum: { type: 'string' },
          },
        },
        recommendations: { type: 'array', items: { type: 'string' } },
      },
    },
  })
  async getPurchaseOrderAnalytics(
    @Query('startDate') startDate?: string,
    @Query('endDate') endDate?: string,
  ): Promise<any> {
    const start = startDate ? new Date(startDate) : undefined;
    const end = endDate ? new Date(endDate) : undefined;
    
    return this.analyticsService.getPurchaseOrderAnalytics(start, end);
  }

  @Get('reports/cost-analysis')
  @Roles('admin', 'supply_chain_manager', 'finance_manager')
  @ApiOperation({
    summary: 'Get cost analysis report',
    description: 'Retrieve detailed cost analysis across suppliers, categories, and time periods.',
  })
  @ApiQuery({
    name: 'period',
    required: false,
    enum: ['MONTHLY', 'QUARTERLY', 'YEARLY'],
    description: 'Analysis period (default: MONTHLY)',
  })
  @ApiQuery({
    name: 'categoryId',
    required: false,
    description: 'Filter by inventory category ID',
  })
  @ApiQuery({
    name: 'supplierId',
    required: false,
    description: 'Filter by supplier ID',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Cost analysis report retrieved successfully',
  })
  async getCostAnalysisReport(
    @Query('period', new DefaultValuePipe('MONTHLY')) period = 'MONTHLY',
    @Query('categoryId') categoryId?: string,
    @Query('supplierId') supplierId?: string,
  ): Promise<any> {
    // This would be a more detailed cost analysis
    // For now, return basic analytics
    return this.analyticsService.getSupplyChainDashboard();
  }

  @Get('reports/efficiency')
  @Roles('admin', 'supply_chain_manager')
  @ApiOperation({
    summary: 'Get supply chain efficiency report',
    description: 'Retrieve supply chain efficiency metrics and optimization recommendations.',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Supply chain efficiency report retrieved successfully',
    schema: {
      type: 'object',
      properties: {
        overallEfficiency: { type: 'string' },
        inventoryTurnover: { type: 'string' },
        supplierPerformance: { type: 'string' },
        orderFulfillmentRate: { type: 'string' },
        costOptimization: { type: 'string' },
        recommendations: { type: 'array', items: { type: 'string' } },
        keyMetrics: {
          type: 'object',
          properties: {
            averageLeadTime: { type: 'string' },
            onTimeDeliveryRate: { type: 'string' },
            stockoutRate: { type: 'string' },
            inventoryAccuracy: { type: 'string' },
          },
        },
      },
    },
  })
  async getSupplyChainEfficiencyReport(): Promise<any> {
    const dashboard = await this.analyticsService.getSupplyChainDashboard();
    
    return {
      overallEfficiency: '85.5%', // Would calculate based on multiple factors
      inventoryTurnover: dashboard.inventory.turnoverRate,
      supplierPerformance: '92.3%', // Average supplier performance
      orderFulfillmentRate: '96.8%', // Order fulfillment rate
      costOptimization: '78.2%', // Cost optimization score
      recommendations: [
        'Consider consolidating orders with top-performing suppliers',
        'Implement automated reorder points for fast-moving items',
        'Review and optimize safety stock levels',
        'Negotiate better payment terms with high-volume suppliers',
      ],
      keyMetrics: {
        averageLeadTime: dashboard.purchaseOrders.averageLeadTime,
        onTimeDeliveryRate: '92.3%',
        stockoutRate: '2.1%',
        inventoryAccuracy: '98.7%',
      },
    };
  }

  @Get('alerts/summary')
  @Roles('admin', 'supply_chain_manager', 'inventory_manager', 'purchase_manager')
  @ApiOperation({
    summary: 'Get supply chain alerts summary',
    description: 'Retrieve a summary of all active supply chain alerts and notifications.',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Supply chain alerts summary retrieved successfully',
    schema: {
      type: 'object',
      properties: {
        totalAlerts: { type: 'number' },
        criticalAlerts: { type: 'number' },
        warningAlerts: { type: 'number' },
        infoAlerts: { type: 'number' },
        categories: {
          type: 'object',
          properties: {
            inventory: { type: 'number' },
            suppliers: { type: 'number' },
            purchaseOrders: { type: 'number' },
            quality: { type: 'number' },
          },
        },
        recentAlerts: { type: 'array' },
      },
    },
  })
  async getSupplyChainAlertsSummary(): Promise<any> {
    const [inventoryAnalytics, dashboard] = await Promise.all([
      this.analyticsService.getInventoryAnalytics(),
      this.analyticsService.getSupplyChainDashboard(),
    ]);

    const lowStockAlerts = dashboard.inventory.lowStockItems;
    const expiryAlerts = inventoryAnalytics.expiryAlerts.length;
    const pendingApprovals = dashboard.purchaseOrders.pending;

    return {
      totalAlerts: lowStockAlerts + expiryAlerts + pendingApprovals,
      criticalAlerts: expiryAlerts,
      warningAlerts: lowStockAlerts,
      infoAlerts: pendingApprovals,
      categories: {
        inventory: lowStockAlerts + expiryAlerts,
        suppliers: 0, // Would calculate supplier-related alerts
        purchaseOrders: pendingApprovals,
        quality: 0, // Would calculate quality-related alerts
      },
      recentAlerts: [
        ...inventoryAnalytics.expiryAlerts.slice(0, 5).map((item: any) => ({
          type: 'EXPIRY_WARNING',
          severity: 'CRITICAL',
          message: `${item.itemName} expires on ${item.expiryDate}`,
          itemId: item.id,
          createdAt: new Date(),
        })),
        // Would add more alert types
      ],
    };
  }
}
