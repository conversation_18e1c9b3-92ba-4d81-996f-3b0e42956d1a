import {
  Controller,
  Get,
  Post,
  Put,
  Delete,
  Body,
  Param,
  Query,
  UseGuards,
  HttpStatus,
  UseInterceptors,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
  ApiParam,
  ApiBody,
  ApiQuery,
} from '@nestjs/swagger';
import { CacheInterceptor } from '@nestjs/cache-manager';
import { JwtAuthGuard } from '@core/auth/guards/jwt-auth.guard';
import { RolesGuard } from '@core/auth/guards/roles.guard';
import { Roles } from '@core/auth/decorators/roles.decorator';
import { CurrentUser } from '@core/auth/decorators/current-user.decorator';
import { User } from '@apps/user/entities/user.entity';
import { BaseController, SupplyChainRoles, ApiTags as Tags } from '@shared/controllers/base.controller';
import { SearchQueryDto, createStatusQueryDto, createListResponseDto } from '@shared/dto/common-query.dto';
import { IsOptional, IsEnum, IsString } from 'class-validator';
import { ApiPropertyOptional } from '@nestjs/swagger';
import { SupplierService } from '../services/supplier.service';
import {
  CreateSupplierDto,
  UpdateSupplierDto,
  VerifySupplierDto,
  SupplierStatus,
  VerificationStatus
} from '../dto/supplier.dto';
import { SupplierDto } from '../dto/supplier-response.dto';

// Create typed query DTOs
class SupplierStatusQueryDto extends createStatusQueryDto(SupplierStatus) {
  @ApiPropertyOptional({
    description: 'Filter by verification status',
    enum: VerificationStatus,
  })
  @IsOptional()
  @IsEnum(VerificationStatus)
  verificationStatus?: VerificationStatus;

  @ApiPropertyOptional({
    description: 'Filter by business type',
    example: 'MANUFACTURER',
    enum: ['MANUFACTURER', 'DISTRIBUTOR', 'WHOLESALER', 'RETAILER'],
  })
  @IsOptional()
  @IsString()
  businessType?: string;

  @ApiPropertyOptional({
    description: 'Filter by specialization',
    example: 'COTTON_FABRICS',
  })
  @IsOptional()
  @IsString()
  specialization?: string;
}

// Create typed response DTO
const SupplierListResponseDto = createListResponseDto(SupplierDto);

@ApiTags(Tags.SUPPLIERS)
@ApiBearerAuth('JWT-auth')
@UseGuards(JwtAuthGuard, RolesGuard)
@Controller('supply-chain/suppliers')
export class SupplierController extends BaseController {
  constructor(private readonly supplierService: SupplierService) {
    super();
  }

  @Post()
  @Roles(...SupplyChainRoles.SUPPLY_CHAIN_MANAGERS)
  @ApiOperation({
    summary: 'Create new supplier',
    description: 'Register a new supplier in the system with comprehensive validation and verification workflow.',
  })
  @BaseController.ItemResponse(SupplierDto, HttpStatus.CREATED, 'Supplier created successfully')
  @BaseController.CrudErrorResponses()
  @ApiBody({
    type: CreateSupplierDto,
    description: 'Supplier registration data',
    examples: {
      'manufacturer': {
        summary: 'Fabric Manufacturer',
        description: 'Example of registering a fabric manufacturer',
        value: {
          companyName: 'Premium Fabrics Ltd.',
          contactPerson: 'John Smith',
          email: '<EMAIL>',
          phone: '******-123-4567',
          businessType: 'MANUFACTURER',
          specializations: ['COTTON_FABRICS', 'SILK_FABRICS'],
          certifications: ['ISO_9001', 'OEKO_TEX_100'],
          paymentTerms: 'NET_30',
          creditLimit: '50000.00',
        },
      },
    },
  })
  async createSupplier(
    @Body() createSupplierDto: CreateSupplierDto,
    @CurrentUser() user: User,
  ): Promise<SupplierDto> {
    return this.supplierService.createSupplier(createSupplierDto, user.id);
  }

  @Get()
  @Roles(...SupplyChainRoles.READ_ONLY_USERS)
  @UseInterceptors(CacheInterceptor)
  @ApiOperation({
    summary: 'Get suppliers with filtering',
    description: 'Retrieve a paginated list of suppliers with comprehensive filtering options.',
  })
  @BaseController.ListResponse(SupplierDto)
  @BaseController.StandardErrorResponses()
  @BaseController.PaginationParams()
  @BaseController.StatusFilter(SupplierStatus, 'Filter by supplier status')
  @BaseController.SearchParam('Search suppliers by company name, email, or contact person')
  async getSuppliers(
    @Query() query: SupplierStatusQueryDto,
  ): Promise<InstanceType<typeof SupplierListResponseDto>> {
    const { page, limit } = this.createPaginationParams(query.page, query.limit);

    const result = await this.supplierService.getSuppliers(
      query.status as SupplierStatus,
      query.verificationStatus,
      query.businessType,
      query.specialization,
      page,
      limit,
    );

    return this.createListResponse(result.suppliers, result.total, page, limit);
  }

  @Get(':id')
  @Roles(...SupplyChainRoles.READ_ONLY_USERS)
  @UseInterceptors(CacheInterceptor)
  @ApiOperation({
    summary: 'Get supplier by ID',
    description: 'Retrieve detailed information about a specific supplier including performance metrics.',
  })
  @ApiParam({
    name: 'id',
    description: 'Supplier unique identifier',
    format: 'uuid',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @BaseController.ItemResponse(SupplierDto)
  @BaseController.StandardErrorResponses()
  @ApiResponse({
    status: 404,
    description: 'Supplier not found',
    schema: {
      type: 'object',
      properties: {
        statusCode: { type: 'number', example: 404 },
        message: { type: 'string', example: 'Supplier with ID 123e4567-e89b-12d3-a456-************ not found' },
        error: { type: 'string', example: 'Not Found' },
      },
    },
  })
  async getSupplierById(@Param('id') id: string): Promise<SupplierDto> {
    this.validateUuid(id, 'supplier ID');
    return this.supplierService.getSupplierById(id);
  }

  @Put(':id')
  @Roles(...SupplyChainRoles.SUPPLY_CHAIN_MANAGERS)
  @ApiOperation({
    summary: 'Update supplier information',
    description: 'Update supplier details with validation and audit trail.',
  })
  @ApiParam({
    name: 'id',
    description: 'Supplier unique identifier',
    format: 'uuid',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @BaseController.ItemResponse(SupplierDto)
  @BaseController.CrudErrorResponses()
  @ApiBody({
    type: UpdateSupplierDto,
    description: 'Supplier update data',
    examples: {
      'status-update': {
        summary: 'Update Supplier Status',
        description: 'Example of updating supplier status',
        value: {
          status: 'ACTIVE',
          paymentTerms: 'NET_15',
        },
      },
      'contact-update': {
        summary: 'Update Contact Information',
        description: 'Example of updating contact details',
        value: {
          contactPerson: 'Jane Smith',
          phone: '******-987-6543',
          email: '<EMAIL>',
        },
      },
    },
  })
  async updateSupplier(
    @Param('id') id: string,
    @Body() updateSupplierDto: UpdateSupplierDto,
    @CurrentUser() user: User,
  ): Promise<SupplierDto> {
    this.validateUuid(id, 'supplier ID');
    return this.supplierService.updateSupplier(id, updateSupplierDto, user.id);
  }

  @Post('verify')
  @Roles(...SupplyChainRoles.SUPPLY_CHAIN_MANAGERS)
  @ApiOperation({
    summary: 'Verify supplier registration',
    description: 'Approve or reject supplier verification with detailed audit trail.',
  })
  @BaseController.ItemResponse(SupplierDto)
  @BaseController.StandardErrorResponses()
  @ApiResponse({
    status: 400,
    description: 'Bad Request - Supplier already verified or invalid decision',
    schema: {
      type: 'object',
      properties: {
        statusCode: { type: 'number', example: 400 },
        message: { type: 'string', example: 'Supplier is already verified' },
        error: { type: 'string', example: 'Bad Request' },
      },
    },
  })
  @ApiBody({
    type: VerifySupplierDto,
    description: 'Supplier verification decision',
    examples: {
      'approve': {
        summary: 'Approve Supplier',
        description: 'Example of approving a supplier',
        value: {
          supplierId: '123e4567-e89b-12d3-a456-************',
          decision: 'VERIFIED',
          notes: 'All documents verified successfully. Company meets all requirements.',
        },
      },
      'reject': {
        summary: 'Reject Supplier',
        description: 'Example of rejecting a supplier',
        value: {
          supplierId: '123e4567-e89b-12d3-a456-************',
          decision: 'REJECTED',
          notes: 'Missing required certifications. Please provide ISO 9001 certificate.',
        },
      },
    },
  })
  async verifySupplier(
    @Body() verifySupplierDto: VerifySupplierDto,
    @CurrentUser() user: User,
  ): Promise<SupplierDto> {
    return this.supplierService.verifySupplier(verifySupplierDto, user.id);
  }

  @Delete(':id')
  @Roles(...SupplyChainRoles.ADMIN_ONLY)
  @ApiOperation({
    summary: 'Delete supplier',
    description: 'Permanently delete a supplier. Only allowed if no active purchase orders exist.',
  })
  @ApiParam({
    name: 'id',
    description: 'Supplier unique identifier',
    format: 'uuid',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @ApiResponse({
    status: 204,
    description: 'Supplier deleted successfully',
  })
  @BaseController.StandardErrorResponses()
  @ApiResponse({
    status: 400,
    description: 'Bad Request - Cannot delete supplier with active orders',
    schema: {
      type: 'object',
      properties: {
        statusCode: { type: 'number', example: 400 },
        message: { type: 'string', example: 'Cannot delete supplier with active purchase orders' },
        error: { type: 'string', example: 'Bad Request' },
      },
    },
  })
  async deleteSupplier(@Param('id') id: string): Promise<void> {
    this.validateUuid(id, 'supplier ID');
    await this.supplierService.deleteSupplier(id);
  }

  @Get(':id/performance')
  @Roles(...SupplyChainRoles.READ_ONLY_USERS)
  @UseInterceptors(CacheInterceptor)
  @ApiOperation({
    summary: 'Get supplier performance metrics',
    description: 'Retrieve comprehensive performance analytics for a specific supplier.',
  })
  @ApiParam({
    name: 'id',
    description: 'Supplier unique identifier',
    format: 'uuid',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @BaseController.ItemResponse(Object, 200, 'Performance metrics retrieved successfully')
  @BaseController.StandardErrorResponses()
  @ApiQuery({
    name: 'period',
    required: false,
    enum: ['MONTHLY', 'QUARTERLY', 'YEARLY'],
    description: 'Metric period filter',
    example: 'MONTHLY',
  })
  async getSupplierPerformanceMetrics(
    @Param('id') id: string,
    @Query('period') period?: string,
  ): Promise<any> {
    this.validateUuid(id, 'supplier ID');
    return this.supplierService.getSupplierPerformanceMetrics(id, period);
  }

  @Get('search/active')
  @Roles(...SupplyChainRoles.READ_ONLY_USERS)
  @UseInterceptors(CacheInterceptor)
  @ApiOperation({
    summary: 'Search active suppliers',
    description: 'Get active, verified suppliers for selection in forms and dropdowns.',
  })
  @BaseController.SearchParam('Search active suppliers')
  @ApiResponse({
    status: 200,
    description: 'Active suppliers retrieved successfully',
    schema: {
      type: 'array',
      items: {
        type: 'object',
        properties: {
          id: { type: 'string', format: 'uuid' },
          companyName: { type: 'string' },
          email: { type: 'string', format: 'email' },
          businessType: { type: 'string' },
          specializations: { type: 'array', items: { type: 'string' } },
          overallRating: { type: 'string' },
          paymentTerms: { type: 'string' },
          currency: { type: 'string' },
        },
      },
    },
  })
  async getActiveSuppliers(@Query() query: SearchQueryDto): Promise<any[]> {
    const result = await this.supplierService.getSuppliers(
      SupplierStatus.ACTIVE,
      VerificationStatus.VERIFIED,
      undefined,
      undefined,
      1,
      1000,
    );

    return result.suppliers
      .filter(supplier =>
        !query.search ||
        supplier.companyName.toLowerCase().includes(query.search.toLowerCase()) ||
        supplier.email.toLowerCase().includes(query.search.toLowerCase())
      )
      .map(supplier => ({
        id: supplier.id,
        companyName: supplier.companyName,
        email: supplier.email,
        businessType: supplier.businessType,
        specializations: supplier.specializations,
        overallRating: supplier.overallRating,
        paymentTerms: supplier.paymentTerms,
        currency: supplier.currency,
      }));
  }
}
