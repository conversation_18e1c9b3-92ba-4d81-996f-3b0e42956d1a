import {
  Controller,
  Get,
  Post,
  Put,
  Delete,
  Body,
  Param,
  Query,
  UseGuards,
  Request,
  HttpStatus,
  ParseIntPipe,
  DefaultValuePipe,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiQuery,
  ApiBearerAuth,
  ApiParam,
} from '@nestjs/swagger';
import { JwtAuthGuard } from '@core/auth/guards/jwt-auth.guard';
import { RolesGuard } from '@core/auth/guards/roles.guard';
import { Roles } from '@core/auth/decorators/roles.decorator';
import { SupplierService } from '../services/supplier.service';
import { 
  CreateSupplierDto, 
  UpdateSupplierDto, 
  VerifySupplierDto,
  SupplierStatus,
  VerificationStatus 
} from '../dto/supplier.dto';
import { SupplierDto } from '../dto/supplier-response.dto';

@ApiTags('Supply Chain - Suppliers')
@ApiBearerAuth()
@UseGuards(JwtAuthGuard, RolesGuard)
@Controller('supply-chain/suppliers')
export class SupplierController {
  constructor(private readonly supplierService: SupplierService) {}

  @Post()
  @Roles('admin', 'supplier_manager')
  @ApiOperation({
    summary: 'Create new supplier',
    description: 'Register a new supplier in the system. Requires admin or supplier manager role.',
  })
  @ApiResponse({
    status: HttpStatus.CREATED,
    description: 'Supplier created successfully',
    type: SupplierDto,
  })
  @ApiResponse({
    status: HttpStatus.CONFLICT,
    description: 'Supplier with email already exists',
  })
  async createSupplier(
    @Body() createSupplierDto: CreateSupplierDto,
    @Request() req: any,
  ): Promise<SupplierDto> {
    return this.supplierService.createSupplier(createSupplierDto, req.user.id);
  }

  @Get()
  @Roles('admin', 'supplier_manager', 'inventory_manager', 'purchase_manager')
  @ApiOperation({
    summary: 'Get all suppliers',
    description: 'Retrieve a paginated list of suppliers with optional filtering.',
  })
  @ApiQuery({
    name: 'status',
    required: false,
    enum: SupplierStatus,
    description: 'Filter by supplier status',
  })
  @ApiQuery({
    name: 'verificationStatus',
    required: false,
    enum: VerificationStatus,
    description: 'Filter by verification status',
  })
  @ApiQuery({
    name: 'businessType',
    required: false,
    description: 'Filter by business type',
  })
  @ApiQuery({
    name: 'specialization',
    required: false,
    description: 'Filter by specialization',
  })
  @ApiQuery({
    name: 'page',
    required: false,
    type: Number,
    description: 'Page number (default: 1)',
  })
  @ApiQuery({
    name: 'limit',
    required: false,
    type: Number,
    description: 'Items per page (default: 20)',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Suppliers retrieved successfully',
    schema: {
      type: 'object',
      properties: {
        suppliers: {
          type: 'array',
          items: { $ref: '#/components/schemas/SupplierDto' },
        },
        total: { type: 'number' },
        page: { type: 'number' },
        totalPages: { type: 'number' },
      },
    },
  })
  async getSuppliers(
    @Query('status') status?: SupplierStatus,
    @Query('verificationStatus') verificationStatus?: VerificationStatus,
    @Query('businessType') businessType?: string,
    @Query('specialization') specialization?: string,
    @Query('page', new DefaultValuePipe(1), ParseIntPipe) page = 1,
    @Query('limit', new DefaultValuePipe(20), ParseIntPipe) limit = 20,
  ): Promise<{
    suppliers: SupplierDto[];
    total: number;
    page: number;
    totalPages: number;
  }> {
    return this.supplierService.getSuppliers(
      status,
      verificationStatus,
      businessType,
      specialization,
      page,
      limit,
    );
  }

  @Get(':id')
  @Roles('admin', 'supplier_manager', 'inventory_manager', 'purchase_manager')
  @ApiOperation({
    summary: 'Get supplier by ID',
    description: 'Retrieve detailed information about a specific supplier.',
  })
  @ApiParam({
    name: 'id',
    description: 'Supplier ID',
    example: '123e4567-e89b-12d3-a456-426614174000',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Supplier retrieved successfully',
    type: SupplierDto,
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Supplier not found',
  })
  async getSupplierById(@Param('id') id: string): Promise<SupplierDto> {
    return this.supplierService.getSupplierById(id);
  }

  @Put(':id')
  @Roles('admin', 'supplier_manager')
  @ApiOperation({
    summary: 'Update supplier',
    description: 'Update supplier information. Requires admin or supplier manager role.',
  })
  @ApiParam({
    name: 'id',
    description: 'Supplier ID',
    example: '123e4567-e89b-12d3-a456-426614174000',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Supplier updated successfully',
    type: SupplierDto,
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Supplier not found',
  })
  @ApiResponse({
    status: HttpStatus.CONFLICT,
    description: 'Email already exists',
  })
  async updateSupplier(
    @Param('id') id: string,
    @Body() updateSupplierDto: UpdateSupplierDto,
    @Request() req: any,
  ): Promise<SupplierDto> {
    return this.supplierService.updateSupplier(id, updateSupplierDto, req.user.id);
  }

  @Post('verify')
  @Roles('admin', 'supplier_manager')
  @ApiOperation({
    summary: 'Verify supplier',
    description: 'Verify or reject a supplier registration. Requires admin or supplier manager role.',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Supplier verification updated successfully',
    type: SupplierDto,
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Supplier not found',
  })
  @ApiResponse({
    status: HttpStatus.BAD_REQUEST,
    description: 'Supplier is already verified',
  })
  async verifySupplier(
    @Body() verifySupplierDto: VerifySupplierDto,
    @Request() req: any,
  ): Promise<SupplierDto> {
    return this.supplierService.verifySupplier(verifySupplierDto, req.user.id);
  }

  @Delete(':id')
  @Roles('admin')
  @ApiOperation({
    summary: 'Delete supplier',
    description: 'Delete a supplier from the system. Requires admin role. Cannot delete suppliers with active purchase orders.',
  })
  @ApiParam({
    name: 'id',
    description: 'Supplier ID',
    example: '123e4567-e89b-12d3-a456-426614174000',
  })
  @ApiResponse({
    status: HttpStatus.NO_CONTENT,
    description: 'Supplier deleted successfully',
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Supplier not found',
  })
  @ApiResponse({
    status: HttpStatus.BAD_REQUEST,
    description: 'Cannot delete supplier with active purchase orders',
  })
  async deleteSupplier(@Param('id') id: string): Promise<void> {
    await this.supplierService.deleteSupplier(id);
  }

  @Get(':id/performance')
  @Roles('admin', 'supplier_manager', 'purchase_manager')
  @ApiOperation({
    summary: 'Get supplier performance metrics',
    description: 'Retrieve performance metrics for a specific supplier.',
  })
  @ApiParam({
    name: 'id',
    description: 'Supplier ID',
    example: '123e4567-e89b-12d3-a456-426614174000',
  })
  @ApiQuery({
    name: 'period',
    required: false,
    enum: ['MONTHLY', 'QUARTERLY', 'YEARLY'],
    description: 'Metric period filter',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Supplier performance metrics retrieved successfully',
    schema: {
      type: 'object',
      properties: {
        supplier: {
          type: 'object',
          properties: {
            id: { type: 'string' },
            companyName: { type: 'string' },
            overallRating: { type: 'string' },
            totalOrders: { type: 'number' },
            totalOrderValue: { type: 'string' },
            onTimeDeliveryRate: { type: 'string' },
            qualityRating: { type: 'string' },
          },
        },
        metrics: {
          type: 'array',
          items: {
            type: 'object',
            properties: {
              id: { type: 'string' },
              metricPeriod: { type: 'string' },
              periodStart: { type: 'string', format: 'date-time' },
              periodEnd: { type: 'string', format: 'date-time' },
              totalOrders: { type: 'number' },
              totalOrderValue: { type: 'string' },
              onTimeDeliveries: { type: 'number' },
              lateDeliveries: { type: 'number' },
              cancelledOrders: { type: 'number' },
              qualityIssues: { type: 'number' },
              onTimeDeliveryRate: { type: 'string' },
              averageLeadTime: { type: 'string' },
              defectRate: { type: 'string' },
              fillRate: { type: 'string' },
              createdAt: { type: 'string', format: 'date-time' },
            },
          },
        },
      },
    },
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Supplier not found',
  })
  async getSupplierPerformanceMetrics(
    @Param('id') id: string,
    @Query('period') period?: string,
  ): Promise<any> {
    return this.supplierService.getSupplierPerformanceMetrics(id, period);
  }

  @Get('search/active')
  @Roles('admin', 'supplier_manager', 'inventory_manager', 'purchase_manager')
  @ApiOperation({
    summary: 'Get active suppliers for selection',
    description: 'Retrieve a list of active, verified suppliers for use in dropdowns and selection lists.',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Active suppliers retrieved successfully',
    schema: {
      type: 'array',
      items: {
        type: 'object',
        properties: {
          id: { type: 'string' },
          companyName: { type: 'string' },
          email: { type: 'string' },
          businessType: { type: 'string' },
          specializations: { type: 'array', items: { type: 'string' } },
          overallRating: { type: 'string' },
          paymentTerms: { type: 'string' },
          currency: { type: 'string' },
        },
      },
    },
  })
  async getActiveSuppliers(): Promise<any[]> {
    const result = await this.supplierService.getSuppliers(
      SupplierStatus.ACTIVE,
      VerificationStatus.VERIFIED,
      undefined,
      undefined,
      1,
      1000, // Get all active suppliers
    );

    return result.suppliers.map(supplier => ({
      id: supplier.id,
      companyName: supplier.companyName,
      email: supplier.email,
      businessType: supplier.businessType,
      specializations: supplier.specializations,
      overallRating: supplier.overallRating,
      paymentTerms: supplier.paymentTerms,
      currency: supplier.currency,
    }));
  }
}
