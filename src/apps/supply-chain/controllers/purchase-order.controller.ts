import {
  Controller,
  Get,
  Post,
  Put,
  Body,
  Param,
  Query,
  UseGuards,
  HttpStatus,
  UseInterceptors,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiQuery,
  ApiBearerAuth,
  ApiParam,
  ApiBody,
} from '@nestjs/swagger';
import { CacheInterceptor } from '@nestjs/cache-manager';
import { JwtAuthGuard } from '@core/auth/guards/jwt-auth.guard';
import { RolesGuard } from '@core/auth/guards/roles.guard';
import { Roles } from '@core/auth/decorators/roles.decorator';
import { CurrentUser } from '@core/auth/decorators/current-user.decorator';
import { User } from '@apps/user/entities/user.entity';
import { BaseController, SupplyChainRoles, ApiTags as Tags } from '@shared/controllers/base.controller';
import { SearchQueryDto, createStatusQueryDto, createListResponseDto } from '@shared/dto/common-query.dto';
import { IsOptional, IsEnum, IsString } from 'class-validator';
import { ApiPropertyOptional } from '@nestjs/swagger';
import { PurchaseOrderService } from '../services/purchase-order.service';
import { 
  CreatePurchaseOrderDto,
  UpdatePurchaseOrderDto,
  UpdatePurchaseOrderStatusDto,
  ApprovePurchaseOrderDto,
  ReceivePurchaseOrderDto,
  PurchaseOrderStatus,
  OrderPriority
} from '../dto/purchase-order.dto';

// Create typed query DTOs
class PurchaseOrderQueryDto extends createStatusQueryDto(PurchaseOrderStatus) {
  @ApiPropertyOptional({
    description: 'Filter by supplier ID',
    format: 'uuid',
  })
  @IsOptional()
  @IsString()
  supplierId?: string;

  @ApiPropertyOptional({
    description: 'Filter by requester ID',
    format: 'uuid',
  })
  @IsOptional()
  @IsString()
  requestedBy?: string;

  @ApiPropertyOptional({
    description: 'Filter by priority',
    enum: OrderPriority,
  })
  @IsOptional()
  @IsEnum(OrderPriority)
  priority?: OrderPriority;
}

// Create typed response DTO
const PurchaseOrderListResponseDto = createListResponseDto(Object);

@ApiTags(Tags.PURCHASE_ORDERS)
@ApiBearerAuth('JWT-auth')
@UseGuards(JwtAuthGuard, RolesGuard)
@Controller('supply-chain/purchase-orders')
export class PurchaseOrderController extends BaseController {
  constructor(private readonly purchaseOrderService: PurchaseOrderService) {
    super();
  }

  @Post()
  @Roles(...SupplyChainRoles.PURCHASE_MANAGERS)
  @ApiOperation({
    summary: 'Create purchase order',
    description: 'Create a new purchase order for procurement with comprehensive validation.',
  })
  @BaseController.ItemResponse(Object, HttpStatus.CREATED, 'Purchase order created successfully')
  @BaseController.CrudErrorResponses()
  @ApiBody({
    type: CreatePurchaseOrderDto,
    description: 'Purchase order data',
    examples: {
      'standard-order': {
        summary: 'Standard Purchase Order',
        description: 'Example of creating a standard purchase order',
        value: {
          supplierId: '123e4567-e89b-12d3-a456-426614174000',
          orderNumber: 'PO-2024-001',
          priority: 'MEDIUM',
          expectedDeliveryDate: '2024-02-15T00:00:00Z',
          items: [
            {
              inventoryItemId: '123e4567-e89b-12d3-a456-426614174001',
              quantity: 100,
              unitPrice: '25.50',
              totalPrice: '2550.00',
            },
          ],
          notes: 'Standard fabric order for spring collection',
        },
      },
    },
  })
  async createPurchaseOrder(
    @Body() createPurchaseOrderDto: CreatePurchaseOrderDto,
    @CurrentUser() user: User,
  ): Promise<object> {
    return this.purchaseOrderService.createPurchaseOrder(createPurchaseOrderDto, user.id);
  }

  @Get()
  @Roles('admin', 'purchase_manager', 'inventory_manager', 'finance_manager')
  @ApiOperation({
    summary: 'Get purchase orders',
    description: 'Retrieve a paginated list of purchase orders with filtering options.',
  })
  @ApiQuery({
    name: 'status',
    required: false,
    enum: PurchaseOrderStatus,
    description: 'Filter by order status',
  })
  @ApiQuery({
    name: 'supplierId',
    required: false,
    description: 'Filter by supplier ID',
  })
  @ApiQuery({
    name: 'requestedBy',
    required: false,
    description: 'Filter by requester user ID',
  })
  @ApiQuery({
    name: 'priority',
    required: false,
    enum: OrderPriority,
    description: 'Filter by order priority',
  })
  @ApiQuery({
    name: 'page',
    required: false,
    type: Number,
    description: 'Page number (default: 1)',
  })
  @ApiQuery({
    name: 'limit',
    required: false,
    type: Number,
    description: 'Items per page (default: 20)',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Purchase orders retrieved successfully',
  })
  async getPurchaseOrders(
    @Query('status') status?: PurchaseOrderStatus,
    @Query('supplierId') supplierId?: string,
    @Query('requestedBy') requestedBy?: string,
    @Query('priority') priority?: OrderPriority,
    @Query('page', new DefaultValuePipe(1), ParseIntPipe) page = 1,
    @Query('limit', new DefaultValuePipe(20), ParseIntPipe) limit = 20,
  ): Promise<{
    orders: any[];
    total: number;
    page: number;
    totalPages: number;
  }> {
    return this.purchaseOrderService.getPurchaseOrders(
      status,
      supplierId,
      requestedBy,
      priority,
      page,
      limit,
    );
  }

  @Get('my-orders')
  @Roles('admin', 'purchase_manager', 'inventory_manager')
  @ApiOperation({
    summary: 'Get my purchase orders',
    description: 'Retrieve purchase orders created by the current user.',
  })
  @ApiQuery({
    name: 'status',
    required: false,
    enum: PurchaseOrderStatus,
    description: 'Filter by order status',
  })
  @ApiQuery({
    name: 'page',
    required: false,
    type: Number,
    description: 'Page number (default: 1)',
  })
  @ApiQuery({
    name: 'limit',
    required: false,
    type: Number,
    description: 'Items per page (default: 20)',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'User purchase orders retrieved successfully',
  })
  async getMyPurchaseOrders(
    @Request() req: any,
    @Query('status') status?: PurchaseOrderStatus,
    @Query('page', new DefaultValuePipe(1), ParseIntPipe) page = 1,
    @Query('limit', new DefaultValuePipe(20), ParseIntPipe) limit = 20,
  ): Promise<{
    orders: any[];
    total: number;
    page: number;
    totalPages: number;
  }> {
    return this.purchaseOrderService.getPurchaseOrders(
      status,
      undefined,
      req.user.id,
      undefined,
      page,
      limit,
    );
  }

  @Get(':id')
  @Roles('admin', 'purchase_manager', 'inventory_manager', 'finance_manager')
  @ApiOperation({
    summary: 'Get purchase order by ID',
    description: 'Retrieve detailed information about a specific purchase order.',
  })
  @ApiParam({
    name: 'id',
    description: 'Purchase order ID',
    example: '123e4567-e89b-12d3-a456-426614174000',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Purchase order retrieved successfully',
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Purchase order not found',
  })
  async getPurchaseOrderById(@Param('id') id: string): Promise<any> {
    return this.purchaseOrderService.getPurchaseOrderById(id);
  }

  @Put(':id')
  @Roles('admin', 'purchase_manager', 'inventory_manager')
  @ApiOperation({
    summary: 'Update purchase order',
    description: 'Update a purchase order. Only allowed for orders in DRAFT or PENDING_APPROVAL status.',
  })
  @ApiParam({
    name: 'id',
    description: 'Purchase order ID',
    example: '123e4567-e89b-12d3-a456-426614174000',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Purchase order updated successfully',
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Purchase order not found',
  })
  @ApiResponse({
    status: HttpStatus.BAD_REQUEST,
    description: 'Cannot update order in current status',
  })
  @ApiResponse({
    status: HttpStatus.FORBIDDEN,
    description: 'Insufficient permissions to update this order',
  })
  async updatePurchaseOrder(
    @Param('id') id: string,
    @Body() updatePurchaseOrderDto: UpdatePurchaseOrderDto,
    @Request() req: any,
  ): Promise<any> {
    return this.purchaseOrderService.updatePurchaseOrder(id, updatePurchaseOrderDto, req.user.id);
  }

  @Put(':id/status')
  @Roles('admin', 'purchase_manager', 'inventory_manager')
  @ApiOperation({
    summary: 'Update purchase order status',
    description: 'Update the status of a purchase order.',
  })
  @ApiParam({
    name: 'id',
    description: 'Purchase order ID',
    example: '123e4567-e89b-12d3-a456-426614174000',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Purchase order status updated successfully',
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Purchase order not found',
  })
  @ApiResponse({
    status: HttpStatus.BAD_REQUEST,
    description: 'Invalid status transition',
  })
  async updatePurchaseOrderStatus(
    @Param('id') id: string,
    @Body() updateStatusDto: UpdatePurchaseOrderStatusDto,
    @Request() req: any,
  ): Promise<any> {
    return this.purchaseOrderService.updatePurchaseOrderStatus(id, updateStatusDto, req.user.id);
  }

  @Post('approve')
  @Roles('admin', 'finance_manager', 'purchase_manager')
  @ApiOperation({
    summary: 'Approve or reject purchase order',
    description: 'Approve or reject a purchase order that is pending approval.',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Purchase order approval decision recorded successfully',
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Purchase order not found',
  })
  @ApiResponse({
    status: HttpStatus.BAD_REQUEST,
    description: 'Purchase order is not pending approval',
  })
  @ApiResponse({
    status: HttpStatus.FORBIDDEN,
    description: 'Insufficient permissions to approve purchase orders',
  })
  async approvePurchaseOrder(
    @Body() approvePurchaseOrderDto: ApprovePurchaseOrderDto,
    @Request() req: any,
  ): Promise<any> {
    return this.purchaseOrderService.approvePurchaseOrder(approvePurchaseOrderDto, req.user.id);
  }

  @Get('pending/approvals')
  @Roles('admin', 'finance_manager', 'purchase_manager')
  @ApiOperation({
    summary: 'Get pending approvals',
    description: 'Retrieve purchase orders that are pending approval.',
  })
  @ApiQuery({
    name: 'page',
    required: false,
    type: Number,
    description: 'Page number (default: 1)',
  })
  @ApiQuery({
    name: 'limit',
    required: false,
    type: Number,
    description: 'Items per page (default: 20)',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Pending approvals retrieved successfully',
  })
  async getPendingApprovals(
    @Query('page', new DefaultValuePipe(1), ParseIntPipe) page = 1,
    @Query('limit', new DefaultValuePipe(20), ParseIntPipe) limit = 20,
  ): Promise<{
    orders: any[];
    total: number;
    page: number;
    totalPages: number;
  }> {
    return this.purchaseOrderService.getPurchaseOrders(
      PurchaseOrderStatus.PENDING_APPROVAL,
      undefined,
      undefined,
      undefined,
      page,
      limit,
    );
  }

  @Post(':id/receive')
  @Roles('admin', 'inventory_manager', 'warehouse_manager')
  @ApiOperation({
    summary: 'Receive purchase order delivery',
    description: 'Record the receipt of a purchase order delivery with quality checks and inventory updates.',
  })
  @ApiParam({
    name: 'id',
    description: 'Purchase order ID',
    example: '123e4567-e89b-12d3-a456-426614174000',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Purchase order delivery received successfully',
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Purchase order not found',
  })
  @ApiResponse({
    status: HttpStatus.BAD_REQUEST,
    description: 'Purchase order cannot be received in current status',
  })
  async receivePurchaseOrder(
    @Param('id') id: string,
    @Body() receivePurchaseOrderDto: ReceivePurchaseOrderDto,
    @Request() req: any,
  ): Promise<any> {
    // Delegate to service for proper business logic handling
    return this.purchaseOrderService.receivePurchaseOrder(
      id,
      receivePurchaseOrderDto,
      req.user.id,
    );
  }

  @Get('reports/summary')
  @Roles('admin', 'purchase_manager', 'finance_manager')
  @ApiOperation({
    summary: 'Get purchase order summary report',
    description: 'Get a summary of purchase order statistics and metrics.',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Purchase order summary retrieved successfully',
  })
  async getPurchaseOrderSummary(): Promise<any> {
    const [
      totalOrders,
      pendingOrders,
      approvedOrders,
      completedOrders,
      cancelledOrders,
    ] = await Promise.all([
      this.purchaseOrderService.getPurchaseOrders(undefined, undefined, undefined, undefined, 1, 1),
      this.purchaseOrderService.getPurchaseOrders(PurchaseOrderStatus.PENDING_APPROVAL, undefined, undefined, undefined, 1, 1),
      this.purchaseOrderService.getPurchaseOrders(PurchaseOrderStatus.APPROVED, undefined, undefined, undefined, 1, 1),
      this.purchaseOrderService.getPurchaseOrders(PurchaseOrderStatus.COMPLETED, undefined, undefined, undefined, 1, 1),
      this.purchaseOrderService.getPurchaseOrders(PurchaseOrderStatus.CANCELLED, undefined, undefined, undefined, 1, 1),
    ]);

    return {
      totalOrders: totalOrders.total,
      pendingApproval: pendingOrders.total,
      approved: approvedOrders.total,
      completed: completedOrders.total,
      cancelled: cancelledOrders.total,
      statusBreakdown: {
        [PurchaseOrderStatus.DRAFT]: 0, // Would calculate actual counts
        [PurchaseOrderStatus.PENDING_APPROVAL]: pendingOrders.total,
        [PurchaseOrderStatus.APPROVED]: approvedOrders.total,
        [PurchaseOrderStatus.SENT]: 0,
        [PurchaseOrderStatus.ACKNOWLEDGED]: 0,
        [PurchaseOrderStatus.IN_PROGRESS]: 0,
        [PurchaseOrderStatus.PARTIALLY_RECEIVED]: 0,
        [PurchaseOrderStatus.COMPLETED]: completedOrders.total,
        [PurchaseOrderStatus.CANCELLED]: cancelledOrders.total,
        [PurchaseOrderStatus.REJECTED]: 0,
      },
    };
  }
}
