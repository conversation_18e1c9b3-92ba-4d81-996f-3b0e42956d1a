import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsString, IsEmail, IsOptional, IsArray, IsEnum, IsDecimal, IsBoolean, IsInt, Min, Max, IsUUID } from 'class-validator';
import { Transform } from 'class-transformer';

export enum SupplierStatus {
  PENDING = 'PENDING',
  ACTIVE = 'ACTIVE',
  INACTIVE = 'INACTIVE',
  SUSPENDED = 'SUSPENDED',
  TERMINATED = 'TERMINATED',
}

export enum VerificationStatus {
  UNVERIFIED = 'UNVERIFIED',
  PENDING = 'PENDING',
  VERIFIED = 'VERIFIED',
  REJECTED = 'REJECTED',
  EXPIRED = 'EXPIRED',
}

export class CreateSupplierDto {
  @ApiProperty({
    description: 'Company name',
    example: 'Premium Fabrics Ltd.',
  })
  @IsString()
  companyName: string;

  @ApiProperty({
    description: 'Primary contact person name',
    example: '<PERSON>',
  })
  @IsString()
  contactPerson: string;

  @ApiProperty({
    description: 'Company email address',
    example: '<EMAIL>',
  })
  @IsEmail()
  email: string;

  @ApiPropertyOptional({
    description: 'Company phone number',
    example: '******-123-4567',
  })
  @IsOptional()
  @IsString()
  phone?: string;

  @ApiPropertyOptional({
    description: 'Company address',
    example: '123 Industrial Ave, Suite 100',
  })
  @IsOptional()
  @IsString()
  address?: string;

  @ApiPropertyOptional({
    description: 'City',
    example: 'New York',
  })
  @IsOptional()
  @IsString()
  city?: string;

  @ApiPropertyOptional({
    description: 'State or province',
    example: 'NY',
  })
  @IsOptional()
  @IsString()
  state?: string;

  @ApiPropertyOptional({
    description: 'Country',
    example: 'United States',
  })
  @IsOptional()
  @IsString()
  country?: string;

  @ApiPropertyOptional({
    description: 'Postal code',
    example: '10001',
  })
  @IsOptional()
  @IsString()
  postalCode?: string;

  @ApiPropertyOptional({
    description: 'Company website',
    example: 'https://www.premiumfabrics.com',
  })
  @IsOptional()
  @IsString()
  website?: string;

  @ApiPropertyOptional({
    description: 'Tax identification number',
    example: 'TAX123456789',
  })
  @IsOptional()
  @IsString()
  taxId?: string;

  @ApiPropertyOptional({
    description: 'Business license number',
    example: 'BL987654321',
  })
  @IsOptional()
  @IsString()
  businessLicense?: string;

  @ApiProperty({
    description: 'Type of business',
    example: 'MANUFACTURER',
    enum: ['MANUFACTURER', 'DISTRIBUTOR', 'WHOLESALER', 'RETAILER'],
  })
  @IsString()
  businessType: string;

  @ApiProperty({
    description: 'Array of specialization categories',
    example: ['COTTON_FABRICS', 'SILK_FABRICS', 'SYNTHETIC_MATERIALS'],
    type: [String],
  })
  @IsArray()
  @IsString({ each: true })
  specializations: string[];

  @ApiPropertyOptional({
    description: 'Quality certifications and compliance certificates',
    example: ['ISO_9001', 'OEKO_TEX_100', 'GOTS_CERTIFIED'],
    type: [String],
  })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  certifications?: string[];

  @ApiPropertyOptional({
    description: 'Payment terms',
    example: 'NET_30',
    enum: ['COD', 'NET_15', 'NET_30', 'NET_60', 'NET_90'],
  })
  @IsOptional()
  @IsString()
  paymentTerms?: string;

  @ApiPropertyOptional({
    description: 'Credit limit for this supplier',
    example: '50000.00',
  })
  @IsOptional()
  @IsString()
  creditLimit?: string;

  @ApiPropertyOptional({
    description: 'Currency for transactions',
    example: 'USD',
    default: 'USD',
  })
  @IsOptional()
  @IsString()
  currency?: string;
}

export class UpdateSupplierDto {
  @ApiPropertyOptional({
    description: 'Company name',
    example: 'Premium Fabrics Ltd.',
  })
  @IsOptional()
  @IsString()
  companyName?: string;

  @ApiPropertyOptional({
    description: 'Primary contact person name',
    example: 'John Smith',
  })
  @IsOptional()
  @IsString()
  contactPerson?: string;

  @ApiPropertyOptional({
    description: 'Company email address',
    example: '<EMAIL>',
  })
  @IsOptional()
  @IsEmail()
  email?: string;

  @ApiPropertyOptional({
    description: 'Company phone number',
    example: '******-123-4567',
  })
  @IsOptional()
  @IsString()
  phone?: string;

  @ApiPropertyOptional({
    description: 'Company address',
    example: '123 Industrial Ave, Suite 100',
  })
  @IsOptional()
  @IsString()
  address?: string;

  @ApiPropertyOptional({
    description: 'City',
    example: 'New York',
  })
  @IsOptional()
  @IsString()
  city?: string;

  @ApiPropertyOptional({
    description: 'State or province',
    example: 'NY',
  })
  @IsOptional()
  @IsString()
  state?: string;

  @ApiPropertyOptional({
    description: 'Country',
    example: 'United States',
  })
  @IsOptional()
  @IsString()
  country?: string;

  @ApiPropertyOptional({
    description: 'Postal code',
    example: '10001',
  })
  @IsOptional()
  @IsString()
  postalCode?: string;

  @ApiPropertyOptional({
    description: 'Company website',
    example: 'https://www.premiumfabrics.com',
  })
  @IsOptional()
  @IsString()
  website?: string;

  @ApiPropertyOptional({
    description: 'Tax identification number',
    example: 'TAX123456789',
  })
  @IsOptional()
  @IsString()
  taxId?: string;

  @ApiPropertyOptional({
    description: 'Business license number',
    example: 'BL987654321',
  })
  @IsOptional()
  @IsString()
  businessLicense?: string;

  @ApiPropertyOptional({
    description: 'Type of business',
    example: 'MANUFACTURER',
    enum: ['MANUFACTURER', 'DISTRIBUTOR', 'WHOLESALER', 'RETAILER'],
  })
  @IsOptional()
  @IsString()
  businessType?: string;

  @ApiPropertyOptional({
    description: 'Array of specialization categories',
    example: ['COTTON_FABRICS', 'SILK_FABRICS', 'SYNTHETIC_MATERIALS'],
    type: [String],
  })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  specializations?: string[];

  @ApiPropertyOptional({
    description: 'Quality certifications and compliance certificates',
    example: ['ISO_9001', 'OEKO_TEX_100', 'GOTS_CERTIFIED'],
    type: [String],
  })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  certifications?: string[];

  @ApiPropertyOptional({
    description: 'Payment terms',
    example: 'NET_30',
    enum: ['COD', 'NET_15', 'NET_30', 'NET_60', 'NET_90'],
  })
  @IsOptional()
  @IsString()
  paymentTerms?: string;

  @ApiPropertyOptional({
    description: 'Credit limit for this supplier',
    example: '50000.00',
  })
  @IsOptional()
  @IsString()
  creditLimit?: string;

  @ApiPropertyOptional({
    description: 'Currency for transactions',
    example: 'USD',
  })
  @IsOptional()
  @IsString()
  currency?: string;

  @ApiPropertyOptional({
    description: 'Supplier status',
    enum: SupplierStatus,
    example: SupplierStatus.ACTIVE,
  })
  @IsOptional()
  @IsEnum(SupplierStatus)
  status?: SupplierStatus;
}

export class VerifySupplierDto {
  @ApiProperty({
    description: 'Supplier ID to verify',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @IsUUID()
  supplierId: string;

  @ApiProperty({
    description: 'Verification decision',
    enum: ['VERIFIED', 'REJECTED'],
    example: 'VERIFIED',
  })
  @IsEnum(['VERIFIED', 'REJECTED'])
  decision: 'VERIFIED' | 'REJECTED';

  @ApiPropertyOptional({
    description: 'Verification notes or rejection reason',
    example: 'All documents verified successfully',
  })
  @IsOptional()
  @IsString()
  notes?: string;
}
