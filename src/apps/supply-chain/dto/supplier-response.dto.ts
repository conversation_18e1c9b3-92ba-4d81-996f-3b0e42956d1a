import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { SupplierStatus, VerificationStatus } from './supplier.dto';

export class SupplierContactDto {
  @ApiProperty({
    description: 'Contact ID',
    example: '123e4567-e89b-12d3-a456-426614174000',
  })
  id: string;

  @ApiProperty({
    description: 'Contact name',
    example: '<PERSON>',
  })
  name: string;

  @ApiPropertyOptional({
    description: 'Contact title',
    example: 'Sales Manager',
  })
  title?: string;

  @ApiProperty({
    description: 'Contact email',
    example: '<EMAIL>',
  })
  email: string;

  @ApiPropertyOptional({
    description: 'Contact phone',
    example: '******-123-4568',
  })
  phone?: string;

  @ApiPropertyOptional({
    description: 'Department',
    example: 'SALES',
    enum: ['SALES', 'SUPPORT', 'FINANCE', 'LOGISTICS'],
  })
  department?: string;

  @ApiProperty({
    description: 'Is primary contact',
    example: true,
  })
  isPrimary: boolean;

  @ApiProperty({
    description: 'Is contact active',
    example: true,
  })
  isActive: boolean;

  @ApiProperty({
    description: 'Contact creation timestamp',
    example: '2023-01-15T08:30:00Z',
  })
  createdAt: Date;

  @ApiProperty({
    description: 'Contact last update timestamp',
    example: '2023-01-15T08:30:00Z',
  })
  updatedAt: Date;
}

export class SupplierCatalogItemDto {
  @ApiProperty({
    description: 'Catalog item ID',
    example: '123e4567-e89b-12d3-a456-426614174000',
  })
  id: string;

  @ApiProperty({
    description: 'Item name',
    example: 'Premium Cotton Fabric',
  })
  itemName: string;

  @ApiPropertyOptional({
    description: 'Item code',
    example: 'PCF-001',
  })
  itemCode?: string;

  @ApiPropertyOptional({
    description: 'Item description',
    example: '100% organic cotton fabric, 200 GSM',
  })
  description?: string;

  @ApiProperty({
    description: 'Item category',
    example: 'FABRIC',
  })
  category: string;

  @ApiPropertyOptional({
    description: 'Item subcategory',
    example: 'COTTON',
  })
  subcategory?: string;

  @ApiProperty({
    description: 'Unit price',
    example: '15.50',
  })
  unitPrice: string;

  @ApiProperty({
    description: 'Currency',
    example: 'USD',
  })
  currency: string;

  @ApiProperty({
    description: 'Minimum order quantity',
    example: 10,
  })
  minimumOrderQty: number;

  @ApiPropertyOptional({
    description: 'Available quantity',
    example: 1000,
  })
  availableQuantity?: number;

  @ApiPropertyOptional({
    description: 'Lead time in days',
    example: 7,
  })
  leadTimeDays?: number;

  @ApiPropertyOptional({
    description: 'Product specifications',
    example: {
      color: 'Navy Blue',
      material: '100% Cotton',
      weight: '200 GSM',
      width: '150 cm'
    },
  })
  specifications?: any;

  @ApiProperty({
    description: 'Product images',
    example: ['https://example.com/image1.jpg', 'https://example.com/image2.jpg'],
    type: [String],
  })
  images: string[];

  @ApiProperty({
    description: 'Product documents',
    example: ['https://example.com/spec.pdf', 'https://example.com/cert.pdf'],
    type: [String],
  })
  documents: string[];

  @ApiProperty({
    description: 'Is item active',
    example: true,
  })
  isActive: boolean;

  @ApiProperty({
    description: 'Is item discontinued',
    example: false,
  })
  isDiscontinued: boolean;

  @ApiProperty({
    description: 'Item creation timestamp',
    example: '2023-01-15T08:30:00Z',
  })
  createdAt: Date;

  @ApiProperty({
    description: 'Item last update timestamp',
    example: '2023-01-15T08:30:00Z',
  })
  updatedAt: Date;
}

export class SupplierRatingDto {
  @ApiProperty({
    description: 'Rating ID',
    example: '123e4567-e89b-12d3-a456-426614174000',
  })
  id: string;

  @ApiProperty({
    description: 'Overall rating (1.00 to 5.00)',
    example: '4.50',
  })
  overallRating: string;

  @ApiProperty({
    description: 'Quality rating (1.00 to 5.00)',
    example: '4.80',
  })
  qualityRating: string;

  @ApiProperty({
    description: 'Delivery rating (1.00 to 5.00)',
    example: '4.20',
  })
  deliveryRating: string;

  @ApiProperty({
    description: 'Service rating (1.00 to 5.00)',
    example: '4.60',
  })
  serviceRating: string;

  @ApiProperty({
    description: 'Value rating (1.00 to 5.00)',
    example: '4.30',
  })
  valueRating: string;

  @ApiPropertyOptional({
    description: 'Review title',
    example: 'Excellent quality fabrics',
  })
  title?: string;

  @ApiPropertyOptional({
    description: 'Review comment',
    example: 'Great quality fabrics with fast delivery. Highly recommended.',
  })
  comment?: string;

  @ApiProperty({
    description: 'Positive aspects',
    example: ['High quality', 'Fast delivery', 'Good customer service'],
    type: [String],
  })
  pros: string[];

  @ApiProperty({
    description: 'Negative aspects',
    example: ['Slightly expensive'],
    type: [String],
  })
  cons: string[];

  @ApiProperty({
    description: 'Is rating verified',
    example: true,
  })
  isVerified: boolean;

  @ApiProperty({
    description: 'Is rating public',
    example: true,
  })
  isPublic: boolean;

  @ApiProperty({
    description: 'Rating creation timestamp',
    example: '2023-01-15T08:30:00Z',
  })
  createdAt: Date;

  @ApiProperty({
    description: 'Rater information',
    example: {
      id: '123e4567-e89b-12d3-a456-426614174002',
      name: 'John Smith',
      email: '<EMAIL>'
    },
  })
  rater: {
    id: string;
    name: string;
    email: string;
  };
}

export class SupplierPerformanceMetricDto {
  @ApiProperty({
    description: 'Metric ID',
    example: '123e4567-e89b-12d3-a456-426614174000',
  })
  id: string;

  @ApiProperty({
    description: 'Metric period',
    example: 'MONTHLY',
    enum: ['MONTHLY', 'QUARTERLY', 'YEARLY'],
  })
  metricPeriod: string;

  @ApiProperty({
    description: 'Period start date',
    example: '2023-01-01T00:00:00Z',
  })
  periodStart: Date;

  @ApiProperty({
    description: 'Period end date',
    example: '2023-01-31T23:59:59Z',
  })
  periodEnd: Date;

  @ApiProperty({
    description: 'Total orders in period',
    example: 25,
  })
  totalOrders: number;

  @ApiProperty({
    description: 'Total order value',
    example: '125000.00',
  })
  totalOrderValue: string;

  @ApiProperty({
    description: 'On-time deliveries',
    example: 23,
  })
  onTimeDeliveries: number;

  @ApiProperty({
    description: 'Late deliveries',
    example: 2,
  })
  lateDeliveries: number;

  @ApiProperty({
    description: 'Cancelled orders',
    example: 0,
  })
  cancelledOrders: number;

  @ApiProperty({
    description: 'Quality issues',
    example: 1,
  })
  qualityIssues: number;

  @ApiPropertyOptional({
    description: 'On-time delivery rate percentage',
    example: '92.00',
  })
  onTimeDeliveryRate?: string;

  @ApiPropertyOptional({
    description: 'Average lead time in days',
    example: '5.50',
  })
  averageLeadTime?: string;

  @ApiPropertyOptional({
    description: 'Defect rate percentage',
    example: '0.50',
  })
  defectRate?: string;

  @ApiPropertyOptional({
    description: 'Fill rate percentage',
    example: '98.00',
  })
  fillRate?: string;

  @ApiProperty({
    description: 'Metric creation timestamp',
    example: '2023-02-01T08:30:00Z',
  })
  createdAt: Date;
}

export class SupplierDto {
  @ApiProperty({
    description: 'Supplier ID',
    example: '123e4567-e89b-12d3-a456-426614174000',
  })
  id: string;

  @ApiProperty({
    description: 'Company name',
    example: 'Premium Fabrics Ltd.',
  })
  companyName: string;

  @ApiProperty({
    description: 'Primary contact person name',
    example: 'John Smith',
  })
  contactPerson: string;

  @ApiProperty({
    description: 'Company email address',
    example: '<EMAIL>',
  })
  email: string;

  @ApiPropertyOptional({
    description: 'Company phone number',
    example: '******-123-4567',
  })
  phone?: string;

  @ApiPropertyOptional({
    description: 'Company address',
    example: '123 Industrial Ave, Suite 100',
  })
  address?: string;

  @ApiPropertyOptional({
    description: 'City',
    example: 'New York',
  })
  city?: string;

  @ApiPropertyOptional({
    description: 'State or province',
    example: 'NY',
  })
  state?: string;

  @ApiPropertyOptional({
    description: 'Country',
    example: 'United States',
  })
  country?: string;

  @ApiPropertyOptional({
    description: 'Postal code',
    example: '10001',
  })
  postalCode?: string;

  @ApiPropertyOptional({
    description: 'Company website',
    example: 'https://www.premiumfabrics.com',
  })
  website?: string;

  @ApiPropertyOptional({
    description: 'Tax identification number',
    example: 'TAX123456789',
  })
  taxId?: string;

  @ApiPropertyOptional({
    description: 'Business license number',
    example: 'BL987654321',
  })
  businessLicense?: string;

  @ApiProperty({
    description: 'Type of business',
    example: 'MANUFACTURER',
  })
  businessType: string;

  @ApiProperty({
    description: 'Array of specialization categories',
    example: ['COTTON_FABRICS', 'SILK_FABRICS', 'SYNTHETIC_MATERIALS'],
    type: [String],
  })
  specializations: string[];

  @ApiProperty({
    description: 'Quality certifications and compliance certificates',
    example: ['ISO_9001', 'OEKO_TEX_100', 'GOTS_CERTIFIED'],
    type: [String],
  })
  certifications: string[];

  @ApiPropertyOptional({
    description: 'Payment terms',
    example: 'NET_30',
  })
  paymentTerms?: string;

  @ApiPropertyOptional({
    description: 'Credit limit for this supplier',
    example: '50000.00',
  })
  creditLimit?: string;

  @ApiProperty({
    description: 'Currency for transactions',
    example: 'USD',
  })
  currency: string;

  @ApiProperty({
    description: 'Supplier status',
    enum: SupplierStatus,
    example: SupplierStatus.ACTIVE,
  })
  status: SupplierStatus;

  @ApiProperty({
    description: 'Verification status',
    enum: VerificationStatus,
    example: VerificationStatus.VERIFIED,
  })
  verificationStatus: VerificationStatus;

  @ApiPropertyOptional({
    description: 'Verification timestamp',
    example: '2023-01-16T10:30:00Z',
  })
  verifiedAt?: Date;

  @ApiPropertyOptional({
    description: 'ID of user who verified the supplier',
    example: '123e4567-e89b-12d3-a456-426614174003',
  })
  verifiedBy?: string;

  @ApiPropertyOptional({
    description: 'Overall rating (0.00 to 5.00)',
    example: '4.50',
  })
  overallRating?: string;

  @ApiProperty({
    description: 'Total number of orders',
    example: 150,
  })
  totalOrders: number;

  @ApiProperty({
    description: 'Total order value',
    example: '750000.00',
  })
  totalOrderValue: string;

  @ApiPropertyOptional({
    description: 'On-time delivery rate percentage',
    example: '92.50',
  })
  onTimeDeliveryRate?: string;

  @ApiPropertyOptional({
    description: 'Quality rating (0.00 to 5.00)',
    example: '4.60',
  })
  qualityRating?: string;

  @ApiProperty({
    description: 'Supplier creation timestamp',
    example: '2023-01-15T08:30:00Z',
  })
  createdAt: Date;

  @ApiProperty({
    description: 'Supplier last update timestamp',
    example: '2023-01-15T08:30:00Z',
  })
  updatedAt: Date;

  @ApiPropertyOptional({
    description: 'Supplier contacts',
    type: [SupplierContactDto],
  })
  contacts?: SupplierContactDto[];

  @ApiPropertyOptional({
    description: 'Supplier catalog items',
    type: [SupplierCatalogItemDto],
  })
  catalogs?: SupplierCatalogItemDto[];

  @ApiPropertyOptional({
    description: 'Supplier ratings',
    type: [SupplierRatingDto],
  })
  ratings?: SupplierRatingDto[];

  @ApiPropertyOptional({
    description: 'Performance metrics',
    type: [SupplierPerformanceMetricDto],
  })
  performanceMetrics?: SupplierPerformanceMetricDto[];
}
