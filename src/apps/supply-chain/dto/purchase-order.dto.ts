import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsString, IsOptional, IsInt, IsEnum, IsUUID, IsArray, IsDateString, Min, ValidateNested } from 'class-validator';
import { Type } from 'class-transformer';

export enum PurchaseOrderStatus {
  DRAFT = 'DRAFT',
  PENDING_APPROVAL = 'PENDING_APPROVAL',
  APPROVED = 'APPROVED',
  SENT = 'SENT',
  ACKNOWLEDGED = 'ACKNOWLEDGED',
  IN_PROGRESS = 'IN_PROGRESS',
  PARTIALLY_RECEIVED = 'PARTIALLY_RECEIVED',
  COMPLETED = 'COMPLETED',
  CANCELLED = 'CANCELLED',
  REJECTED = 'REJECTED',
}

export enum OrderPriority {
  LOW = 'LOW',
  NORMAL = 'NORMAL',
  HIGH = 'HIGH',
  URGENT = 'URGENT',
  CRITICAL = 'CRITICAL',
}

export enum PaymentStatus {
  PENDING = 'PENDING',
  PAID = 'PAID',
  PARTIALLY_PAID = 'PARTIALLY_PAID',
  OVERDUE = 'OVERDUE',
  CANCELLED = 'CANCELLED',
}

export enum PurchaseOrderItemStatus {
  PENDING = 'PENDING',
  CONFIRMED = 'CONFIRMED',
  IN_PRODUCTION = 'IN_PRODUCTION',
  SHIPPED = 'SHIPPED',
  RECEIVED = 'RECEIVED',
  REJECTED = 'REJECTED',
  CANCELLED = 'CANCELLED',
}

export class CreatePurchaseOrderItemDto {
  @ApiPropertyOptional({
    description: 'Inventory item ID (if ordering existing inventory item)',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @IsOptional()
  @IsUUID()
  inventoryItemId?: string;

  @ApiPropertyOptional({
    description: 'Supplier catalog ID (if ordering from supplier catalog)',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @IsOptional()
  @IsUUID()
  supplierCatalogId?: string;

  @ApiProperty({
    description: 'Item name',
    example: 'Premium Cotton Fabric - Navy Blue',
  })
  @IsString()
  itemName: string;

  @ApiPropertyOptional({
    description: 'Item code',
    example: 'PCF-NB-001',
  })
  @IsOptional()
  @IsString()
  itemCode?: string;

  @ApiPropertyOptional({
    description: 'Item description',
    example: '100% organic cotton fabric, 200 GSM, navy blue color',
  })
  @IsOptional()
  @IsString()
  description?: string;

  @ApiPropertyOptional({
    description: 'Item specifications',
    example: {
      color: 'Navy Blue',
      material: '100% Cotton',
      weight: '200 GSM',
      width: '150 cm'
    },
  })
  @IsOptional()
  specifications?: any;

  @ApiProperty({
    description: 'Quantity to order',
    example: 100,
    minimum: 1,
  })
  @IsInt()
  @Min(1)
  quantityOrdered: number;

  @ApiProperty({
    description: 'Unit price',
    example: '15.50',
  })
  @IsString()
  unitPrice: string;

  @ApiPropertyOptional({
    description: 'Discount percentage',
    example: '5.00',
    default: '0.00',
  })
  @IsOptional()
  @IsString()
  discountPercent?: string;

  @ApiPropertyOptional({
    description: 'Expected delivery date',
    example: '2023-02-15T00:00:00Z',
  })
  @IsOptional()
  @IsDateString()
  expectedDeliveryDate?: string;
}

export class CreatePurchaseOrderDto {
  @ApiProperty({
    description: 'Supplier ID',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @IsUUID()
  supplierId: string;

  @ApiPropertyOptional({
    description: 'Order priority',
    enum: OrderPriority,
    example: OrderPriority.NORMAL,
    default: OrderPriority.NORMAL,
  })
  @IsOptional()
  @IsEnum(OrderPriority)
  priority?: OrderPriority;

  @ApiPropertyOptional({
    description: 'Order type',
    example: 'STANDARD',
    enum: ['STANDARD', 'URGENT', 'BLANKET'],
    default: 'STANDARD',
  })
  @IsOptional()
  @IsString()
  orderType?: string;

  @ApiPropertyOptional({
    description: 'Payment terms',
    example: 'NET_30',
    enum: ['COD', 'NET_15', 'NET_30', 'NET_60', 'NET_90'],
  })
  @IsOptional()
  @IsString()
  paymentTerms?: string;

  @ApiPropertyOptional({
    description: 'Payment method',
    example: 'WALLET',
    enum: ['WALLET', 'BANK_TRANSFER', 'CREDIT', 'CHECK'],
  })
  @IsOptional()
  @IsString()
  paymentMethod?: string;

  @ApiPropertyOptional({
    description: 'Requested delivery date',
    example: '2023-02-15T00:00:00Z',
  })
  @IsOptional()
  @IsDateString()
  requestedDeliveryDate?: string;

  @ApiPropertyOptional({
    description: 'Delivery address',
    example: '123 Main St, Warehouse A, New York, NY 10001',
  })
  @IsOptional()
  @IsString()
  deliveryAddress?: string;

  @ApiPropertyOptional({
    description: 'Shipping method',
    example: 'GROUND',
    enum: ['GROUND', 'EXPRESS', 'OVERNIGHT', 'FREIGHT'],
  })
  @IsOptional()
  @IsString()
  shippingMethod?: string;

  @ApiPropertyOptional({
    description: 'Order notes',
    example: 'Please ensure all items are quality checked before shipping',
  })
  @IsOptional()
  @IsString()
  notes?: string;

  @ApiPropertyOptional({
    description: 'Internal notes (not visible to supplier)',
    example: 'Urgent order for customer project deadline',
  })
  @IsOptional()
  @IsString()
  internalNotes?: string;

  @ApiPropertyOptional({
    description: 'Special instructions',
    example: 'Deliver to loading dock B, contact John at ext. 123',
  })
  @IsOptional()
  @IsString()
  specialInstructions?: string;

  @ApiProperty({
    description: 'Purchase order items',
    type: [CreatePurchaseOrderItemDto],
  })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => CreatePurchaseOrderItemDto)
  items: CreatePurchaseOrderItemDto[];
}

export class UpdatePurchaseOrderDto {
  @ApiPropertyOptional({
    description: 'Order priority',
    enum: OrderPriority,
    example: OrderPriority.HIGH,
  })
  @IsOptional()
  @IsEnum(OrderPriority)
  priority?: OrderPriority;

  @ApiPropertyOptional({
    description: 'Order type',
    example: 'URGENT',
    enum: ['STANDARD', 'URGENT', 'BLANKET'],
  })
  @IsOptional()
  @IsString()
  orderType?: string;

  @ApiPropertyOptional({
    description: 'Payment terms',
    example: 'NET_15',
    enum: ['COD', 'NET_15', 'NET_30', 'NET_60', 'NET_90'],
  })
  @IsOptional()
  @IsString()
  paymentTerms?: string;

  @ApiPropertyOptional({
    description: 'Payment method',
    example: 'BANK_TRANSFER',
    enum: ['WALLET', 'BANK_TRANSFER', 'CREDIT', 'CHECK'],
  })
  @IsOptional()
  @IsString()
  paymentMethod?: string;

  @ApiPropertyOptional({
    description: 'Requested delivery date',
    example: '2023-02-10T00:00:00Z',
  })
  @IsOptional()
  @IsDateString()
  requestedDeliveryDate?: string;

  @ApiPropertyOptional({
    description: 'Delivery address',
    example: '123 Main St, Warehouse B, New York, NY 10001',
  })
  @IsOptional()
  @IsString()
  deliveryAddress?: string;

  @ApiPropertyOptional({
    description: 'Shipping method',
    example: 'EXPRESS',
    enum: ['GROUND', 'EXPRESS', 'OVERNIGHT', 'FREIGHT'],
  })
  @IsOptional()
  @IsString()
  shippingMethod?: string;

  @ApiPropertyOptional({
    description: 'Order notes',
    example: 'Updated: Please expedite this order',
  })
  @IsOptional()
  @IsString()
  notes?: string;

  @ApiPropertyOptional({
    description: 'Internal notes',
    example: 'Customer agreed to pay rush charges',
  })
  @IsOptional()
  @IsString()
  internalNotes?: string;

  @ApiPropertyOptional({
    description: 'Special instructions',
    example: 'Call before delivery, new contact: Jane at ext. 456',
  })
  @IsOptional()
  @IsString()
  specialInstructions?: string;
}

export class UpdatePurchaseOrderStatusDto {
  @ApiProperty({
    description: 'New purchase order status',
    enum: PurchaseOrderStatus,
    example: PurchaseOrderStatus.APPROVED,
  })
  @IsEnum(PurchaseOrderStatus)
  status: PurchaseOrderStatus;

  @ApiPropertyOptional({
    description: 'Status change notes',
    example: 'Approved by finance manager',
  })
  @IsOptional()
  @IsString()
  notes?: string;
}

export class ApprovePurchaseOrderDto {
  @ApiProperty({
    description: 'Purchase order ID to approve',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @IsUUID()
  purchaseOrderId: string;

  @ApiProperty({
    description: 'Approval decision',
    enum: ['APPROVED', 'REJECTED', 'NEEDS_REVISION'],
    example: 'APPROVED',
  })
  @IsEnum(['APPROVED', 'REJECTED', 'NEEDS_REVISION'])
  decision: 'APPROVED' | 'REJECTED' | 'NEEDS_REVISION';

  @ApiPropertyOptional({
    description: 'Approval comments',
    example: 'Approved within budget limits',
  })
  @IsOptional()
  @IsString()
  comments?: string;

  @ApiPropertyOptional({
    description: 'Conditions for approval',
    example: ['Must be delivered by specified date', 'Quality inspection required'],
    type: [String],
  })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  conditions?: string[];
}

export class ReceivePurchaseOrderDto {
  @ApiProperty({
    description: 'Purchase order ID',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @IsUUID()
  purchaseOrderId: string;

  @ApiPropertyOptional({
    description: 'Delivery tracking number',
    example: 'TRK123456789',
  })
  @IsOptional()
  @IsString()
  trackingNumber?: string;

  @ApiPropertyOptional({
    description: 'Delivery method',
    example: 'DELIVERY',
    enum: ['PICKUP', 'DELIVERY', 'COURIER'],
  })
  @IsOptional()
  @IsString()
  deliveryMethod?: string;

  @ApiPropertyOptional({
    description: 'Is this a partial delivery',
    example: false,
    default: false,
  })
  @IsOptional()
  isPartialDelivery?: boolean;

  @ApiPropertyOptional({
    description: 'Quality check status',
    example: 'PASSED',
    enum: ['PASSED', 'FAILED', 'PENDING'],
  })
  @IsOptional()
  @IsString()
  qualityCheckStatus?: string;

  @ApiPropertyOptional({
    description: 'Quality check notes',
    example: 'All items inspected and meet quality standards',
  })
  @IsOptional()
  @IsString()
  qualityNotes?: string;

  @ApiPropertyOptional({
    description: 'Delivery notes',
    example: 'Delivered in good condition, all packages intact',
  })
  @IsOptional()
  @IsString()
  deliveryNotes?: string;

  @ApiPropertyOptional({
    description: 'Any issues with the delivery',
    example: ['One package slightly damaged', 'Delivery was 1 day late'],
    type: [String],
  })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  issues?: string[];
}
