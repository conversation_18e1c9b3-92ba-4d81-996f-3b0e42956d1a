import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsString, IsOptional, IsInt, IsEnum, IsUUID, IsBoolean, IsDateString, Min, IsObject } from 'class-validator';
import { Transform } from 'class-transformer';

export enum InventoryItemType {
  FABRIC = 'FABRIC',
  THREAD = 'THREAD',
  ACCESSORY = 'ACCESSORY',
  TOOL = 'TOOL',
  EQUIPMENT = 'EQUIPMENT',
  CONSUMABLE = 'CONSUMABLE',
  PACKAGING = 'PACKAGING',
}

export enum InventoryStatus {
  ACTIVE = 'ACTIVE',
  INACTIVE = 'INACTIVE',
  DISCONTINUED = 'DISCONTINUED',
  QUARANTINE = 'QUARANTINE',
  DAMAGED = 'DAMAGED',
  EXPIRED = 'EXPIRED',
}

export enum InventoryTransactionType {
  PURCHASE = 'PURCHASE',
  SALE = 'SALE',
  ADJUSTMENT = 'ADJUSTMENT',
  TRANSFER = 'TRANSFER',
  RETURN = 'RETURN',
  WASTE = 'WASTE',
  THEFT = 'THEFT',
  DAMAGE = 'DAMAGE',
}

export enum StockAlertType {
  LOW_STOCK = 'LOW_STOCK',
  OUT_OF_STOCK = 'OUT_OF_STOCK',
  OVERSTOCK = 'OVERSTOCK',
  EXPIRY_WARNING = 'EXPIRY_WARNING',
  REORDER_POINT = 'REORDER_POINT',
}

export class CreateInventoryCategoryDto {
  @ApiProperty({
    description: 'Category name',
    example: 'Cotton Fabrics',
  })
  @IsString()
  name: string;

  @ApiPropertyOptional({
    description: 'Category description',
    example: 'All types of cotton fabrics and materials',
  })
  @IsOptional()
  @IsString()
  description?: string;

  @ApiPropertyOptional({
    description: 'Parent category ID for hierarchical structure',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @IsOptional()
  @IsUUID()
  parentId?: string;
}

export class UpdateInventoryCategoryDto {
  @ApiPropertyOptional({
    description: 'Category name',
    example: 'Premium Cotton Fabrics',
  })
  @IsOptional()
  @IsString()
  name?: string;

  @ApiPropertyOptional({
    description: 'Category description',
    example: 'Premium quality cotton fabrics and materials',
  })
  @IsOptional()
  @IsString()
  description?: string;

  @ApiPropertyOptional({
    description: 'Parent category ID',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @IsOptional()
  @IsUUID()
  parentId?: string;

  @ApiPropertyOptional({
    description: 'Is category active',
    example: true,
  })
  @IsOptional()
  @IsBoolean()
  isActive?: boolean;
}

export class CreateInventoryItemDto {
  @ApiProperty({
    description: 'Item name',
    example: 'Premium Cotton Fabric - Navy Blue',
  })
  @IsString()
  itemName: string;

  @ApiProperty({
    description: 'Unique item code',
    example: 'PCF-NB-001',
  })
  @IsString()
  itemCode: string;

  @ApiPropertyOptional({
    description: 'Item description',
    example: '100% organic cotton fabric, 200 GSM, navy blue color',
  })
  @IsOptional()
  @IsString()
  description?: string;

  @ApiProperty({
    description: 'Category ID',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @IsUUID()
  categoryId: string;

  @ApiPropertyOptional({
    description: 'Supplier ID',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @IsOptional()
  @IsUUID()
  supplierId?: string;

  @ApiPropertyOptional({
    description: 'Supplier catalog ID',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @IsOptional()
  @IsUUID()
  supplierCatalogId?: string;

  @ApiProperty({
    description: 'Type of inventory item',
    enum: InventoryItemType,
    example: InventoryItemType.FABRIC,
  })
  @IsEnum(InventoryItemType)
  itemType: InventoryItemType;

  @ApiProperty({
    description: 'Unit of measurement',
    example: 'METER',
  })
  @IsString()
  unit: string;

  @ApiPropertyOptional({
    description: 'Item specifications (color, material, size, etc.)',
    example: {
      color: 'Navy Blue',
      material: '100% Cotton',
      weight: '200 GSM',
      width: '150 cm'
    },
  })
  @IsOptional()
  @IsObject()
  specifications?: any;

  @ApiProperty({
    description: 'Current stock quantity',
    example: 500,
    minimum: 0,
  })
  @IsInt()
  @Min(0)
  currentStock: number;

  @ApiProperty({
    description: 'Minimum stock level (reorder point)',
    example: 50,
    minimum: 0,
  })
  @IsInt()
  @Min(0)
  minimumStock: number;

  @ApiPropertyOptional({
    description: 'Maximum stock level',
    example: 1000,
    minimum: 0,
  })
  @IsOptional()
  @IsInt()
  @Min(0)
  maximumStock?: number;

  @ApiProperty({
    description: 'Unit cost',
    example: '15.50',
  })
  @IsString()
  unitCost: string;

  @ApiPropertyOptional({
    description: 'Currency',
    example: 'USD',
    default: 'USD',
  })
  @IsOptional()
  @IsString()
  currency?: string;

  @ApiPropertyOptional({
    description: 'Storage location',
    example: 'Warehouse A, Section 1',
  })
  @IsOptional()
  @IsString()
  location?: string;

  @ApiPropertyOptional({
    description: 'Specific bin/shelf location',
    example: 'A1-B2-C3',
  })
  @IsOptional()
  @IsString()
  binLocation?: string;

  @ApiPropertyOptional({
    description: 'Special storage conditions',
    example: 'Keep dry, temperature controlled',
  })
  @IsOptional()
  @IsString()
  storageConditions?: string;

  @ApiPropertyOptional({
    description: 'Is item perishable',
    example: false,
    default: false,
  })
  @IsOptional()
  @IsBoolean()
  isPerishable?: boolean;

  @ApiPropertyOptional({
    description: 'Expiry date (for perishable items)',
    example: '2024-12-31T23:59:59Z',
  })
  @IsOptional()
  @IsDateString()
  expiryDate?: string;

  @ApiPropertyOptional({
    description: 'Batch number',
    example: 'BATCH-2023-001',
  })
  @IsOptional()
  @IsString()
  batchNumber?: string;

  @ApiPropertyOptional({
    description: 'Serial number',
    example: 'SN-123456789',
  })
  @IsOptional()
  @IsString()
  serialNumber?: string;
}

export class UpdateInventoryItemDto {
  @ApiPropertyOptional({
    description: 'Item name',
    example: 'Premium Cotton Fabric - Navy Blue',
  })
  @IsOptional()
  @IsString()
  itemName?: string;

  @ApiPropertyOptional({
    description: 'Item description',
    example: '100% organic cotton fabric, 200 GSM, navy blue color',
  })
  @IsOptional()
  @IsString()
  description?: string;

  @ApiPropertyOptional({
    description: 'Category ID',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @IsOptional()
  @IsUUID()
  categoryId?: string;

  @ApiPropertyOptional({
    description: 'Supplier ID',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @IsOptional()
  @IsUUID()
  supplierId?: string;

  @ApiPropertyOptional({
    description: 'Type of inventory item',
    enum: InventoryItemType,
    example: InventoryItemType.FABRIC,
  })
  @IsOptional()
  @IsEnum(InventoryItemType)
  itemType?: InventoryItemType;

  @ApiPropertyOptional({
    description: 'Unit of measurement',
    example: 'METER',
  })
  @IsOptional()
  @IsString()
  unit?: string;

  @ApiPropertyOptional({
    description: 'Item specifications',
    example: {
      color: 'Navy Blue',
      material: '100% Cotton',
      weight: '200 GSM',
      width: '150 cm'
    },
  })
  @IsOptional()
  @IsObject()
  specifications?: any;

  @ApiPropertyOptional({
    description: 'Minimum stock level',
    example: 50,
    minimum: 0,
  })
  @IsOptional()
  @IsInt()
  @Min(0)
  minimumStock?: number;

  @ApiPropertyOptional({
    description: 'Maximum stock level',
    example: 1000,
    minimum: 0,
  })
  @IsOptional()
  @IsInt()
  @Min(0)
  maximumStock?: number;

  @ApiPropertyOptional({
    description: 'Unit cost',
    example: '15.50',
  })
  @IsOptional()
  @IsString()
  unitCost?: string;

  @ApiPropertyOptional({
    description: 'Storage location',
    example: 'Warehouse A, Section 1',
  })
  @IsOptional()
  @IsString()
  location?: string;

  @ApiPropertyOptional({
    description: 'Specific bin/shelf location',
    example: 'A1-B2-C3',
  })
  @IsOptional()
  @IsString()
  binLocation?: string;

  @ApiPropertyOptional({
    description: 'Special storage conditions',
    example: 'Keep dry, temperature controlled',
  })
  @IsOptional()
  @IsString()
  storageConditions?: string;

  @ApiPropertyOptional({
    description: 'Item status',
    enum: InventoryStatus,
    example: InventoryStatus.ACTIVE,
  })
  @IsOptional()
  @IsEnum(InventoryStatus)
  status?: InventoryStatus;

  @ApiPropertyOptional({
    description: 'Expiry date',
    example: '2024-12-31T23:59:59Z',
  })
  @IsOptional()
  @IsDateString()
  expiryDate?: string;

  @ApiPropertyOptional({
    description: 'Batch number',
    example: 'BATCH-2023-001',
  })
  @IsOptional()
  @IsString()
  batchNumber?: string;

  @ApiPropertyOptional({
    description: 'Serial number',
    example: 'SN-123456789',
  })
  @IsOptional()
  @IsString()
  serialNumber?: string;
}

export class CreateInventoryTransactionDto {
  @ApiProperty({
    description: 'Inventory item ID',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @IsUUID()
  inventoryItemId: string;

  @ApiProperty({
    description: 'Transaction type',
    enum: InventoryTransactionType,
    example: InventoryTransactionType.PURCHASE,
  })
  @IsEnum(InventoryTransactionType)
  transactionType: InventoryTransactionType;

  @ApiProperty({
    description: 'Quantity (positive for IN, negative for OUT)',
    example: 100,
  })
  @IsInt()
  quantity: number;

  @ApiPropertyOptional({
    description: 'Unit cost for this transaction',
    example: '15.50',
  })
  @IsOptional()
  @IsString()
  unitCost?: string;

  @ApiPropertyOptional({
    description: 'Reference number (PO number, order number, etc.)',
    example: 'PO-2023-001',
  })
  @IsOptional()
  @IsString()
  reference?: string;

  @ApiPropertyOptional({
    description: 'Reference type',
    example: 'PURCHASE_ORDER',
  })
  @IsOptional()
  @IsString()
  referenceType?: string;

  @ApiPropertyOptional({
    description: 'Transaction notes',
    example: 'Received from supplier Premium Fabrics Ltd.',
  })
  @IsOptional()
  @IsString()
  notes?: string;

  @ApiPropertyOptional({
    description: 'Batch number',
    example: 'BATCH-2023-001',
  })
  @IsOptional()
  @IsString()
  batchNumber?: string;

  @ApiPropertyOptional({
    description: 'Expiry date',
    example: '2024-12-31T23:59:59Z',
  })
  @IsOptional()
  @IsDateString()
  expiryDate?: string;
}

export class AdjustInventoryDto {
  @ApiProperty({
    description: 'Inventory item ID',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @IsUUID()
  inventoryItemId: string;

  @ApiProperty({
    description: 'New stock quantity',
    example: 450,
    minimum: 0,
  })
  @IsInt()
  @Min(0)
  newQuantity: number;

  @ApiProperty({
    description: 'Reason for adjustment',
    example: 'Physical count adjustment - found discrepancy',
  })
  @IsString()
  reason: string;

  @ApiPropertyOptional({
    description: 'Additional notes',
    example: 'Conducted physical inventory count on 2023-01-15',
  })
  @IsOptional()
  @IsString()
  notes?: string;
}
