import { Injectable, Logger } from '@nestjs/common';
import { PrismaService } from '@core/prisma/prisma.service';
import { DecimalService } from '@shared/decimal/decimal.service';
import { MongoDbService } from '@infra/mongodb/mongodb.service';

@Injectable()
export class SupplyChainAnalyticsService {
  private readonly logger = new Logger(SupplyChainAnalyticsService.name);

  constructor(
    private readonly prisma: PrismaService,
    private readonly decimalService: DecimalService,
    private readonly mongoDbService: MongoDbService,
  ) {}

  async getSupplyChainDashboard(): Promise<any> {
    try {
      const [
        totalSuppliers,
        activeSuppliers,
        totalInventoryItems,
        lowStockItems,
        totalPurchaseOrders,
        pendingPurchaseOrders,
        totalInventoryValue,
        monthlyPurchaseValue,
      ] = await Promise.all([
        this.getTotalSuppliers(),
        this.getActiveSuppliers(),
        this.getTotalInventoryItems(),
        this.getLowStockItems(),
        this.getTotalPurchaseOrders(),
        this.getPendingPurchaseOrders(),
        this.getTotalInventoryValue(),
        this.getMonthlyPurchaseValue(),
      ]);

      return {
        suppliers: {
          total: totalSuppliers,
          active: activeSuppliers,
          verificationPending: totalSuppliers - activeSuppliers,
        },
        inventory: {
          totalItems: totalInventoryItems,
          lowStockItems,
          totalValue: totalInventoryValue,
          turnoverRate: await this.getInventoryTurnoverRate(),
        },
        purchaseOrders: {
          total: totalPurchaseOrders,
          pending: pendingPurchaseOrders,
          monthlyValue: monthlyPurchaseValue,
          averageLeadTime: await this.getAverageLeadTime(),
        },
        performance: {
          supplierPerformance: await this.getTopSuppliersByPerformance(),
          categoryAnalysis: await this.getInventoryCategoryAnalysis(),
          costTrends: await this.getCostTrends(),
        },
      };
    } catch (error) {
      this.logger.error(`Failed to get supply chain dashboard: ${error.message}`, error.stack);
      throw error;
    }
  }

  async getSupplierPerformanceReport(
    supplierId?: string,
    startDate?: Date,
    endDate?: Date,
  ): Promise<any> {
    try {
      const where: any = {};
      if (supplierId) where.supplierId = supplierId;
      if (startDate && endDate) {
        where.createdAt = {
          gte: startDate,
          lte: endDate,
        };
      }

      const purchaseOrders = await this.prisma.purchaseOrder.findMany({
        where,
        include: {
          supplier: {
            select: { id: true, companyName: true, overallRating: true },
          },
          items: true,
        },
      });

      const supplierMetrics = new Map();

      purchaseOrders.forEach(order => {
        const supplierId = order.supplier.id;
        if (!supplierMetrics.has(supplierId)) {
          supplierMetrics.set(supplierId, {
            supplier: order.supplier,
            totalOrders: 0,
            totalValue: this.decimalService.create('0'),
            onTimeDeliveries: 0,
            lateDeliveries: 0,
            averageLeadTime: 0,
            qualityIssues: 0,
          });
        }

        const metrics = supplierMetrics.get(supplierId);
        metrics.totalOrders += 1;
        metrics.totalValue = metrics.totalValue.add(order.totalAmount);

        // Calculate delivery performance
        if (order.actualDeliveryDate && order.expectedDeliveryDate) {
          if (order.actualDeliveryDate <= order.expectedDeliveryDate) {
            metrics.onTimeDeliveries += 1;
          } else {
            metrics.lateDeliveries += 1;
          }
        }
      });

      // Convert to array and calculate percentages
      const results = Array.from(supplierMetrics.values()).map(metrics => ({
        ...metrics,
        totalValue: metrics.totalValue.toString(),
        onTimeDeliveryRate: metrics.totalOrders > 0 
          ? ((metrics.onTimeDeliveries / metrics.totalOrders) * 100).toFixed(2)
          : '0.00',
        averageOrderValue: metrics.totalOrders > 0
          ? metrics.totalValue.div(metrics.totalOrders).toString()
          : '0.00',
      }));

      return results.sort((a, b) => parseFloat(b.totalValue) - parseFloat(a.totalValue));
    } catch (error) {
      this.logger.error(`Failed to get supplier performance report: ${error.message}`, error.stack);
      throw error;
    }
  }

  async getInventoryAnalytics(): Promise<any> {
    try {
      const [
        categoryBreakdown,
        stockLevels,
        turnoverAnalysis,
        costAnalysis,
        expiryAlerts,
      ] = await Promise.all([
        this.getInventoryCategoryBreakdown(),
        this.getStockLevelAnalysis(),
        this.getInventoryTurnoverAnalysis(),
        this.getInventoryCostAnalysis(),
        this.getExpiryAlerts(),
      ]);

      return {
        categoryBreakdown,
        stockLevels,
        turnoverAnalysis,
        costAnalysis,
        expiryAlerts,
        recommendations: await this.getInventoryRecommendations(),
      };
    } catch (error) {
      this.logger.error(`Failed to get inventory analytics: ${error.message}`, error.stack);
      throw error;
    }
  }

  async getDemandForecast(
    inventoryItemId?: string,
    forecastPeriod = 'MONTHLY',
    periodsAhead = 3,
  ): Promise<any> {
    try {
      const where: any = { forecastPeriod };
      if (inventoryItemId) where.inventoryItemId = inventoryItemId;

      const forecasts = await this.prisma.demandForecast.findMany({
        where,
        include: {
          inventoryItem: {
            select: { id: true, itemName: true, itemCode: true, currentStock: true },
          },
        },
        orderBy: { periodStart: 'desc' },
        take: 20,
      });

      // Generate new forecasts if needed
      if (inventoryItemId && forecasts.length === 0) {
        const newForecasts = await this.generateDemandForecast(inventoryItemId, forecastPeriod, periodsAhead);
        return newForecasts;
      }

      return forecasts;
    } catch (error) {
      this.logger.error(`Failed to get demand forecast: ${error.message}`, error.stack);
      throw error;
    }
  }

  async getPurchaseOrderAnalytics(startDate?: Date, endDate?: Date): Promise<any> {
    try {
      const where: any = {};
      if (startDate && endDate) {
        where.createdAt = {
          gte: startDate,
          lte: endDate,
        };
      }

      const [
        ordersByStatus,
        ordersBySupplier,
        monthlyTrends,
        averageOrderValue,
        leadTimeAnalysis,
      ] = await Promise.all([
        this.getPurchaseOrdersByStatus(where),
        this.getPurchaseOrdersBySupplier(where),
        this.getPurchaseOrderMonthlyTrends(where),
        this.getAveragePurchaseOrderValue(where),
        this.getPurchaseOrderLeadTimeAnalysis(where),
      ]);

      return {
        ordersByStatus,
        ordersBySupplier,
        monthlyTrends,
        averageOrderValue,
        leadTimeAnalysis,
        recommendations: await this.getPurchaseOrderRecommendations(),
      };
    } catch (error) {
      this.logger.error(`Failed to get purchase order analytics: ${error.message}`, error.stack);
      throw error;
    }
  }

  // Private helper methods
  private async getTotalSuppliers(): Promise<number> {
    return this.prisma.supplier.count();
  }

  private async getActiveSuppliers(): Promise<number> {
    return this.prisma.supplier.count({
      where: { status: 'ACTIVE' },
    });
  }

  private async getTotalInventoryItems(): Promise<number> {
    return this.prisma.inventoryItem.count({
      where: { status: 'ACTIVE' },
    });
  }

  private async getLowStockItems(): Promise<number> {
    const items = await this.prisma.inventoryItem.findMany({
      where: {
        status: 'ACTIVE',
      },
      select: { currentStock: true, minimumStock: true },
    });

    return items.filter(item => item.currentStock <= item.minimumStock).length;
  }

  private async getTotalPurchaseOrders(): Promise<number> {
    return this.prisma.purchaseOrder.count();
  }

  private async getPendingPurchaseOrders(): Promise<number> {
    return this.prisma.purchaseOrder.count({
      where: {
        status: {
          in: ['DRAFT', 'PENDING_APPROVAL', 'APPROVED', 'SENT', 'ACKNOWLEDGED', 'IN_PROGRESS'],
        },
      },
    });
  }

  private async getTotalInventoryValue(): Promise<string> {
    const items = await this.prisma.inventoryItem.findMany({
      where: { status: 'ACTIVE' },
      select: { currentStock: true, unitCost: true },
    });

    let totalValue = this.decimalService.create('0');
    items.forEach(item => {
      const itemValue = this.decimalService.create(item.unitCost)
        .mul(item.currentStock);
      totalValue = totalValue.add(itemValue);
    });

    return totalValue.toString();
  }

  private async getMonthlyPurchaseValue(): Promise<string> {
    const startOfMonth = new Date();
    startOfMonth.setDate(1);
    startOfMonth.setHours(0, 0, 0, 0);

    const orders = await this.prisma.purchaseOrder.findMany({
      where: {
        createdAt: { gte: startOfMonth },
        status: { not: 'CANCELLED' },
      },
      select: { totalAmount: true },
    });

    let totalValue = this.decimalService.create('0');
    orders.forEach(order => {
      totalValue = totalValue.add(order.totalAmount);
    });

    return totalValue.toString();
  }

  private async getInventoryTurnoverRate(): Promise<string> {
    // Simplified calculation - in real system would be more sophisticated
    const thirtyDaysAgo = new Date();
    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

    const transactions = await this.prisma.inventoryTransaction.findMany({
      where: {
        transactionType: 'SALE',
        transactionDate: { gte: thirtyDaysAgo },
      },
      select: { quantity: true, unitCost: true },
    });

    let totalSold = this.decimalService.create('0');
    transactions.forEach(transaction => {
      const value = this.decimalService.create(transaction.unitCost || '0')
        .mul(Math.abs(transaction.quantity));
      totalSold = totalSold.add(value);
    });

    const totalInventoryValue = await this.getTotalInventoryValue();
    const inventoryValue = this.decimalService.create(totalInventoryValue);

    if (inventoryValue.gt('0')) {
      return totalSold.div(inventoryValue).mul('12').toString(); // Annualized
    }

    return '0.00';
  }

  private async getAverageLeadTime(): Promise<string> {
    const completedOrders = await this.prisma.purchaseOrder.findMany({
      where: {
        status: 'COMPLETED',
        orderDate: { not: null as any },
        actualDeliveryDate: { not: null },
      },
      select: { orderDate: true, actualDeliveryDate: true },
      take: 100, // Last 100 completed orders
    });

    if (completedOrders.length === 0) return '0.00';

    let totalDays = 0;
    completedOrders.forEach(order => {
      const leadTime = Math.ceil(
        (order.actualDeliveryDate!.getTime() - order.orderDate.getTime()) / (1000 * 60 * 60 * 24)
      );
      totalDays += leadTime;
    });

    return (totalDays / completedOrders.length).toFixed(2);
  }

  private async getTopSuppliersByPerformance(): Promise<any[]> {
    const suppliers = await this.prisma.supplier.findMany({
      where: { status: 'ACTIVE' },
      select: {
        id: true,
        companyName: true,
        overallRating: true,
        totalOrders: true,
        totalOrderValue: true,
        onTimeDeliveryRate: true,
      },
      orderBy: { overallRating: 'desc' },
      take: 5,
    });

    return suppliers;
  }

  private async getInventoryCategoryAnalysis(): Promise<any[]> {
    const categories = await this.prisma.inventoryCategory.findMany({
      include: {
        inventoryItems: {
          select: { currentStock: true, unitCost: true, status: true },
        },
      },
    });

    return categories.map(category => {
      const activeItems = category.inventoryItems.filter(item => item.status === 'ACTIVE');
      let totalValue = this.decimalService.create('0');
      
      activeItems.forEach(item => {
        const itemValue = this.decimalService.create(item.unitCost)
          .mul(item.currentStock);
        totalValue = totalValue.add(itemValue);
      });

      return {
        id: category.id,
        name: category.name,
        itemCount: activeItems.length,
        totalValue: totalValue.toString(),
      };
    });
  }

  private async getCostTrends(): Promise<any[]> {
    // Simplified cost trend analysis
    const months: any[] = [];
    for (let i = 5; i >= 0; i--) {
      const date = new Date();
      date.setMonth(date.getMonth() - i);
      const startOfMonth = new Date(date.getFullYear(), date.getMonth(), 1);
      const endOfMonth = new Date(date.getFullYear(), date.getMonth() + 1, 0);

      const orders = await this.prisma.purchaseOrder.findMany({
        where: {
          createdAt: {
            gte: startOfMonth,
            lte: endOfMonth,
          },
          status: { not: 'CANCELLED' },
        },
        select: { totalAmount: true },
      });

      let totalCost = this.decimalService.create('0');
      orders.forEach(order => {
        totalCost = totalCost.add(order.totalAmount);
      });

      months.push({
        month: startOfMonth.toISOString().substring(0, 7), // YYYY-MM format
        totalCost: totalCost.toString(),
        orderCount: orders.length,
      });
    }

    return months;
  }

  private async getInventoryCategoryBreakdown(): Promise<any[]> {
    return this.getInventoryCategoryAnalysis();
  }

  private async getStockLevelAnalysis(): Promise<any> {
    const items = await this.prisma.inventoryItem.findMany({
      where: { status: 'ACTIVE' },
      select: { currentStock: true, minimumStock: true, maximumStock: true },
    });

    const lowStock = items.filter(item => item.currentStock <= item.minimumStock).length;
    const outOfStock = items.filter(item => item.currentStock === 0).length;
    const overStock = items.filter(item => 
      item.maximumStock && item.currentStock > item.maximumStock
    ).length;
    const normalStock = items.length - lowStock - outOfStock - overStock;

    return {
      total: items.length,
      normal: normalStock,
      low: lowStock,
      out: outOfStock,
      over: overStock,
    };
  }

  private async getInventoryTurnoverAnalysis(): Promise<any[]> {
    // Simplified turnover analysis by category
    const categories = await this.getInventoryCategoryAnalysis();
    return categories.map(category => ({
      ...category,
      turnoverRate: '0.00', // Would calculate based on sales data
    }));
  }

  private async getInventoryCostAnalysis(): Promise<any> {
    const totalValue = await this.getTotalInventoryValue();
    const categories = await this.getInventoryCategoryAnalysis();
    
    return {
      totalValue,
      categoryBreakdown: categories,
      averageItemValue: categories.length > 0 
        ? this.decimalService.create(totalValue).div(categories.length).toString()
        : '0.00',
    };
  }

  private async getExpiryAlerts(): Promise<any[]> {
    const thirtyDaysFromNow = new Date();
    thirtyDaysFromNow.setDate(thirtyDaysFromNow.getDate() + 30);

    const expiringItems = await this.prisma.inventoryItem.findMany({
      where: {
        isPerishable: true,
        expiryDate: {
          lte: thirtyDaysFromNow,
        },
        status: 'ACTIVE',
      },
      select: {
        id: true,
        itemName: true,
        itemCode: true,
        currentStock: true,
        expiryDate: true,
      },
      orderBy: { expiryDate: 'asc' },
    });

    return expiringItems;
  }

  private async getInventoryRecommendations(): Promise<string[]> {
    const recommendations: string[] = [];
    
    const lowStockCount = await this.getLowStockItems();
    if (lowStockCount > 0) {
      recommendations.push(`${lowStockCount} items are running low on stock and need reordering`);
    }

    const expiringItems = await this.getExpiryAlerts();
    if (expiringItems.length > 0) {
      recommendations.push(`${expiringItems.length} items are expiring within 30 days`);
    }

    return recommendations;
  }

  private async getPurchaseOrdersByStatus(where: any): Promise<any[]> {
    const statuses = ['DRAFT', 'PENDING_APPROVAL', 'APPROVED', 'SENT', 'ACKNOWLEDGED', 'IN_PROGRESS', 'COMPLETED', 'CANCELLED'];
    
    const results = await Promise.all(
      statuses.map(async status => ({
        status,
        count: await this.prisma.purchaseOrder.count({
          where: { ...where, status },
        }),
      }))
    );

    return results;
  }

  private async getPurchaseOrdersBySupplier(where: any): Promise<any[]> {
    const orders = await this.prisma.purchaseOrder.findMany({
      where,
      include: {
        supplier: {
          select: { id: true, companyName: true },
        },
      },
    });

    const supplierMap = new Map();
    orders.forEach(order => {
      const supplierId = order.supplier.id;
      if (!supplierMap.has(supplierId)) {
        supplierMap.set(supplierId, {
          supplier: order.supplier,
          orderCount: 0,
          totalValue: this.decimalService.create('0'),
        });
      }
      
      const data = supplierMap.get(supplierId);
      data.orderCount += 1;
      data.totalValue = data.totalValue.add(order.totalAmount);
    });

    return Array.from(supplierMap.values()).map(data => ({
      ...data,
      totalValue: data.totalValue.toString(),
    }));
  }

  private async getPurchaseOrderMonthlyTrends(where: any): Promise<any[]> {
    // Similar to getCostTrends but with additional filters
    return this.getCostTrends();
  }

  private async getAveragePurchaseOrderValue(where: any): Promise<string> {
    const orders = await this.prisma.purchaseOrder.findMany({
      where: { ...where, status: { not: 'CANCELLED' } },
      select: { totalAmount: true },
    });

    if (orders.length === 0) return '0.00';

    let totalValue = this.decimalService.create('0');
    orders.forEach(order => {
      totalValue = totalValue.add(order.totalAmount);
    });

    return totalValue.div(orders.length).toString();
  }

  private async getPurchaseOrderLeadTimeAnalysis(where: any): Promise<any> {
    const completedOrders = await this.prisma.purchaseOrder.findMany({
      where: {
        ...where,
        status: 'COMPLETED',
        orderDate: { not: null },
        actualDeliveryDate: { not: null },
      },
      select: { orderDate: true, actualDeliveryDate: true },
    });

    if (completedOrders.length === 0) {
      return { average: '0.00', minimum: '0.00', maximum: '0.00' };
    }

    const leadTimes = completedOrders.map(order => 
      Math.ceil((order.actualDeliveryDate!.getTime() - order.orderDate.getTime()) / (1000 * 60 * 60 * 24))
    );

    return {
      average: (leadTimes.reduce((sum, time) => sum + time, 0) / leadTimes.length).toFixed(2),
      minimum: Math.min(...leadTimes).toString(),
      maximum: Math.max(...leadTimes).toString(),
    };
  }

  private async getPurchaseOrderRecommendations(): Promise<string[]> {
    const recommendations = [];
    
    const pendingApprovals = await this.prisma.purchaseOrder.count({
      where: { status: 'PENDING_APPROVAL' },
    });
    
    if (pendingApprovals > 0) {
      recommendations.push(`${pendingApprovals} purchase orders are pending approval`);
    }

    return recommendations;
  }

  private async generateDemandForecast(
    inventoryItemId: string,
    forecastPeriod: string,
    periodsAhead: number,
  ): Promise<any[]> {
    // Simplified demand forecasting - in real system would use more sophisticated algorithms
    const item = await this.prisma.inventoryItem.findUnique({
      where: { id: inventoryItemId },
    });

    if (!item) return [];

    const forecasts = [];
    const baseDate = new Date();
    
    for (let i = 1; i <= periodsAhead; i++) {
      const periodStart = new Date(baseDate);
      const periodEnd = new Date(baseDate);
      
      if (forecastPeriod === 'MONTHLY') {
        periodStart.setMonth(periodStart.getMonth() + i);
        periodEnd.setMonth(periodEnd.getMonth() + i + 1);
      } else if (forecastPeriod === 'WEEKLY') {
        periodStart.setDate(periodStart.getDate() + (i * 7));
        periodEnd.setDate(periodEnd.getDate() + ((i + 1) * 7));
      }

      // Simple forecast based on current stock and minimum stock
      const predictedDemand = Math.max(1, Math.floor(item.minimumStock * 1.2));
      
      const forecast = await this.prisma.demandForecast.create({
        data: {
          inventoryItemId,
          forecastPeriod,
          periodStart,
          periodEnd,
          predictedDemand,
          confidence: '75.00', // Simplified confidence level
          historicalPeriods: 3,
          forecastMethod: 'MOVING_AVERAGE',
        },
        include: {
          inventoryItem: {
            select: { id: true, itemName: true, itemCode: true, currentStock: true },
          },
        },
      });

      forecasts.push(forecast);
    }

    return forecasts;
  }
}
