import { Injectable, Logger, NotFoundException, BadRequestException, ConflictException } from '@nestjs/common';
import { PrismaService } from '@core/prisma/prisma.service';
import { DecimalService } from '@shared/decimal/decimal.service';
import { WebsocketService } from '@infra/websocket/websocket.service';
import { RabbitMQService } from '@infra/rabbitmq/rabbitmq.service';
import { MongoDbService } from '@infra/mongodb/mongodb.service';
import { CreateSupplierDto, UpdateSupplierDto, VerifySupplierDto, SupplierStatus, VerificationStatus } from '../dto/supplier.dto';
import { SupplierDto } from '../dto/supplier-response.dto';

@Injectable()
export class SupplierService {
  private readonly logger = new Logger(SupplierService.name);

  constructor(
    private readonly prisma: PrismaService,
    private readonly decimalService: DecimalService,
    private readonly websocketService: WebsocketService,
    private readonly rabbitMQService: RabbitMQService,
    private readonly mongoDbService: MongoDbService,
  ) {}

  async createSupplier(createSupplierDto: CreateSupplierDto, createdBy?: string): Promise<SupplierDto> {
    try {
      // Check if supplier with email already exists
      const existingSupplier = await this.prisma.supplier.findUnique({
        where: { email: createSupplierDto.email },
      });

      if (existingSupplier) {
        throw new ConflictException(`Supplier with email ${createSupplierDto.email} already exists`);
      }

      // Generate unique supplier code
      const supplierCode = await this.generateSupplierCode();

      const supplier = await this.prisma.supplier.create({
        data: {
          ...createSupplierDto,
          creditLimit: createSupplierDto.creditLimit || '0.00',
          currency: createSupplierDto.currency || 'USD',
          status: SupplierStatus.PENDING,
          verificationStatus: VerificationStatus.UNVERIFIED,
          createdBy,
        },
        include: {
          contacts: true,
          catalogs: true,
          ratings: {
            include: {
              rater: {
                select: { id: true, firstName: true, lastName: true, email: true },
              },
            },
          },
          performanceMetrics: true,
        },
      });

      // Sync to MongoDB
      await this.mongoDbService.syncDocument('suppliers', supplier);

      // Send notification
      await this.notifySupplierCreated(supplier);

      // Publish event
      await this.rabbitMQService.publish({
        type: 'supplier_created',
        data: { supplier },
      });

      return this.mapToSupplierDto(supplier);
    } catch (error) {
      this.logger.error(`Failed to create supplier: ${error.message}`, error.stack);
      throw error;
    }
  }

  async getSuppliers(
    status?: SupplierStatus,
    verificationStatus?: VerificationStatus,
    businessType?: string,
    specialization?: string,
    page = 1,
    limit = 20,
  ): Promise<{ suppliers: SupplierDto[]; total: number; page: number; totalPages: number }> {
    try {
      const where: any = {};
      
      if (status) where.status = status;
      if (verificationStatus) where.verificationStatus = verificationStatus;
      if (businessType) where.businessType = businessType;
      if (specialization) {
        where.specializations = {
          has: specialization,
        };
      }

      const [suppliers, total] = await Promise.all([
        this.prisma.supplier.findMany({
          where,
          include: {
            contacts: true,
            catalogs: true,
            ratings: {
              include: {
                rater: {
                  select: { id: true, firstName: true, lastName: true, email: true },
                },
              },
            },
            performanceMetrics: true,
          },
          orderBy: { createdAt: 'desc' },
          skip: (page - 1) * limit,
          take: limit,
        }),
        this.prisma.supplier.count({ where }),
      ]);

      return {
        suppliers: suppliers.map(supplier => this.mapToSupplierDto(supplier)),
        total,
        page,
        totalPages: Math.ceil(total / limit),
      };
    } catch (error) {
      this.logger.error(`Failed to get suppliers: ${error.message}`, error.stack);
      throw error;
    }
  }

  async getSupplierById(id: string): Promise<SupplierDto> {
    try {
      const supplier = await this.prisma.supplier.findUnique({
        where: { id },
        include: {
          contacts: true,
          catalogs: true,
          ratings: {
            include: {
              rater: {
                select: { id: true, firstName: true, lastName: true, email: true },
              },
            },
          },
          performanceMetrics: true,
        },
      });

      if (!supplier) {
        throw new NotFoundException(`Supplier with ID ${id} not found`);
      }

      return this.mapToSupplierDto(supplier);
    } catch (error) {
      this.logger.error(`Failed to get supplier: ${error.message}`, error.stack);
      throw error;
    }
  }

  async updateSupplier(id: string, updateSupplierDto: UpdateSupplierDto, updatedBy?: string): Promise<SupplierDto> {
    try {
      const existingSupplier = await this.prisma.supplier.findUnique({
        where: { id },
      });

      if (!existingSupplier) {
        throw new NotFoundException(`Supplier with ID ${id} not found`);
      }

      // Check email uniqueness if email is being updated
      if (updateSupplierDto.email && updateSupplierDto.email !== existingSupplier.email) {
        const emailExists = await this.prisma.supplier.findUnique({
          where: { email: updateSupplierDto.email },
        });

        if (emailExists) {
          throw new ConflictException(`Supplier with email ${updateSupplierDto.email} already exists`);
        }
      }

      const supplier = await this.prisma.supplier.update({
        where: { id },
        data: {
          ...updateSupplierDto,
          updatedBy,
        },
        include: {
          contacts: true,
          catalogs: true,
          ratings: {
            include: {
              rater: {
                select: { id: true, firstName: true, lastName: true, email: true },
              },
            },
          },
          performanceMetrics: true,
        },
      });

      // Sync to MongoDB
      await this.mongoDbService.syncDocument('suppliers', supplier);

      // Send notification if status changed
      if (updateSupplierDto.status && updateSupplierDto.status !== existingSupplier.status) {
        await this.notifySupplierStatusChanged(supplier, updateSupplierDto.status);
      }

      // Publish event
      await this.rabbitMQService.publish({
        type: 'supplier_updated',
        data: { supplier, previousData: existingSupplier },
      });

      return this.mapToSupplierDto(supplier);
    } catch (error) {
      this.logger.error(`Failed to update supplier: ${error.message}`, error.stack);
      throw error;
    }
  }

  async verifySupplier(verifySupplierDto: VerifySupplierDto, verifiedBy: string): Promise<SupplierDto> {
    try {
      const supplier = await this.prisma.supplier.findUnique({
        where: { id: verifySupplierDto.supplierId },
      });

      if (!supplier) {
        throw new NotFoundException(`Supplier with ID ${verifySupplierDto.supplierId} not found`);
      }

      if (supplier.verificationStatus === VerificationStatus.VERIFIED) {
        throw new BadRequestException('Supplier is already verified');
      }

      const updatedSupplier = await this.prisma.supplier.update({
        where: { id: verifySupplierDto.supplierId },
        data: {
          verificationStatus: verifySupplierDto.decision === 'VERIFIED' 
            ? VerificationStatus.VERIFIED 
            : VerificationStatus.REJECTED,
          verifiedAt: verifySupplierDto.decision === 'VERIFIED' ? new Date() : null,
          verifiedBy: verifySupplierDto.decision === 'VERIFIED' ? verifiedBy : null,
          status: verifySupplierDto.decision === 'VERIFIED' 
            ? SupplierStatus.ACTIVE 
            : SupplierStatus.TERMINATED,
        },
        include: {
          contacts: true,
          catalogs: true,
          ratings: {
            include: {
              rater: {
                select: { id: true, firstName: true, lastName: true, email: true },
              },
            },
          },
          performanceMetrics: true,
        },
      });

      // Sync to MongoDB
      await this.mongoDbService.syncDocument('suppliers', updatedSupplier);

      // Send notification
      await this.notifySupplierVerificationChanged(updatedSupplier, verifySupplierDto.decision, verifySupplierDto.notes);

      // Publish event
      await this.rabbitMQService.publish({
        type: 'supplier_verification_changed',
        data: { 
          supplier: updatedSupplier, 
          decision: verifySupplierDto.decision,
          notes: verifySupplierDto.notes,
          verifiedBy 
        },
      });

      return this.mapToSupplierDto(updatedSupplier);
    } catch (error) {
      this.logger.error(`Failed to verify supplier: ${error.message}`, error.stack);
      throw error;
    }
  }

  async deleteSupplier(id: string): Promise<void> {
    try {
      const supplier = await this.prisma.supplier.findUnique({
        where: { id },
        include: { purchaseOrders: true },
      });

      if (!supplier) {
        throw new NotFoundException(`Supplier with ID ${id} not found`);
      }

      // Check if supplier has active purchase orders
      const activePurchaseOrders = supplier.purchaseOrders.filter(
        po => !['COMPLETED', 'CANCELLED'].includes(po.status)
      );

      if (activePurchaseOrders.length > 0) {
        throw new BadRequestException(
          'Cannot delete supplier with active purchase orders. Please complete or cancel all orders first.'
        );
      }

      await this.prisma.supplier.delete({
        where: { id },
      });

      // Remove from MongoDB
      await this.mongoDbService.deleteDocument('suppliers', id);

      // Publish event
      await this.rabbitMQService.publish({
        type: 'supplier_deleted',
        data: { supplierId: id, supplier },
      });

      this.logger.log(`Supplier ${id} deleted successfully`);
    } catch (error) {
      this.logger.error(`Failed to delete supplier: ${error.message}`, error.stack);
      throw error;
    }
  }

  async getSupplierPerformanceMetrics(supplierId: string, period?: string): Promise<any> {
    try {
      const supplier = await this.prisma.supplier.findUnique({
        where: { id: supplierId },
      });

      if (!supplier) {
        throw new NotFoundException(`Supplier with ID ${supplierId} not found`);
      }

      const where: any = { supplierId };
      if (period) where.metricPeriod = period;

      const metrics = await this.prisma.supplierPerformanceMetric.findMany({
        where,
        orderBy: { periodStart: 'desc' },
      });

      return {
        supplier: {
          id: supplier.id,
          companyName: supplier.companyName,
          overallRating: supplier.overallRating,
          totalOrders: supplier.totalOrders,
          totalOrderValue: supplier.totalOrderValue,
          onTimeDeliveryRate: supplier.onTimeDeliveryRate,
          qualityRating: supplier.qualityRating,
        },
        metrics,
      };
    } catch (error) {
      this.logger.error(`Failed to get supplier performance metrics: ${error.message}`, error.stack);
      throw error;
    }
  }

  // Private helper methods
  private async generateSupplierCode(): Promise<string> {
    const count = await this.prisma.supplier.count();
    return `SUP-${String(count + 1).padStart(6, '0')}`;
  }

  private async notifySupplierCreated(supplier: any): Promise<void> {
    // Notify admins about new supplier registration
    await this.websocketService.sendToRole('admin', 'supplier_created', {
      message: `New supplier ${supplier.companyName} has registered`,
      supplier: {
        id: supplier.id,
        companyName: supplier.companyName,
        email: supplier.email,
        status: supplier.status,
      },
    });
  }

  private async notifySupplierStatusChanged(supplier: any, newStatus: SupplierStatus): Promise<void> {
    // Notify supplier about status change
    await this.websocketService.sendToUser(supplier.email, 'supplier_status_changed', {
      message: `Your supplier status has been changed to ${newStatus}`,
      supplierId: supplier.id,
      newStatus,
    });

    // Notify admins
    await this.websocketService.sendToRole('admin', 'supplier_status_changed', {
      message: `Supplier ${supplier.companyName} status changed to ${newStatus}`,
      supplier: {
        id: supplier.id,
        companyName: supplier.companyName,
        newStatus,
      },
    });
  }

  private async notifySupplierVerificationChanged(
    supplier: any, 
    decision: string, 
    notes?: string
  ): Promise<void> {
    const message = decision === 'VERIFIED' 
      ? 'Your supplier account has been verified and activated'
      : `Your supplier verification was rejected${notes ? `: ${notes}` : ''}`;

    await this.websocketService.sendToUser(supplier.email, 'supplier_verification_changed', {
      message,
      supplierId: supplier.id,
      decision,
      notes,
    });
  }

  private mapToSupplierDto(supplier: any): SupplierDto {
    return {
      id: supplier.id,
      companyName: supplier.companyName,
      contactPerson: supplier.contactPerson,
      email: supplier.email,
      phone: supplier.phone,
      address: supplier.address,
      city: supplier.city,
      state: supplier.state,
      country: supplier.country,
      postalCode: supplier.postalCode,
      website: supplier.website,
      taxId: supplier.taxId,
      businessLicense: supplier.businessLicense,
      businessType: supplier.businessType,
      specializations: supplier.specializations,
      certifications: supplier.certifications,
      paymentTerms: supplier.paymentTerms,
      creditLimit: supplier.creditLimit,
      currency: supplier.currency,
      status: supplier.status,
      verificationStatus: supplier.verificationStatus,
      verifiedAt: supplier.verifiedAt,
      verifiedBy: supplier.verifiedBy,
      overallRating: supplier.overallRating,
      totalOrders: supplier.totalOrders,
      totalOrderValue: supplier.totalOrderValue,
      onTimeDeliveryRate: supplier.onTimeDeliveryRate,
      qualityRating: supplier.qualityRating,
      createdAt: supplier.createdAt,
      updatedAt: supplier.updatedAt,
      contacts: supplier.contacts?.map((contact: any) => ({
        id: contact.id,
        name: contact.name,
        title: contact.title,
        email: contact.email,
        phone: contact.phone,
        department: contact.department,
        isPrimary: contact.isPrimary,
        isActive: contact.isActive,
        createdAt: contact.createdAt,
        updatedAt: contact.updatedAt,
      })),
      catalogs: supplier.catalogs?.map((catalog: any) => ({
        id: catalog.id,
        itemName: catalog.itemName,
        itemCode: catalog.itemCode,
        description: catalog.description,
        category: catalog.category,
        subcategory: catalog.subcategory,
        unitPrice: catalog.unitPrice,
        currency: catalog.currency,
        minimumOrderQty: catalog.minimumOrderQty,
        availableQuantity: catalog.availableQuantity,
        leadTimeDays: catalog.leadTimeDays,
        specifications: catalog.specifications,
        images: catalog.images,
        documents: catalog.documents,
        isActive: catalog.isActive,
        isDiscontinued: catalog.isDiscontinued,
        createdAt: catalog.createdAt,
        updatedAt: catalog.updatedAt,
      })),
      ratings: supplier.ratings?.map((rating: any) => ({
        id: rating.id,
        overallRating: rating.overallRating,
        qualityRating: rating.qualityRating,
        deliveryRating: rating.deliveryRating,
        serviceRating: rating.serviceRating,
        valueRating: rating.valueRating,
        title: rating.title,
        comment: rating.comment,
        pros: rating.pros,
        cons: rating.cons,
        isVerified: rating.isVerified,
        isPublic: rating.isPublic,
        createdAt: rating.createdAt,
        rater: {
          id: rating.rater.id,
          name: `${rating.rater.firstName} ${rating.rater.lastName}`,
          email: rating.rater.email,
        },
      })),
      performanceMetrics: supplier.performanceMetrics?.map((metric: any) => ({
        id: metric.id,
        metricPeriod: metric.metricPeriod,
        periodStart: metric.periodStart,
        periodEnd: metric.periodEnd,
        totalOrders: metric.totalOrders,
        totalOrderValue: metric.totalOrderValue,
        onTimeDeliveries: metric.onTimeDeliveries,
        lateDeliveries: metric.lateDeliveries,
        cancelledOrders: metric.cancelledOrders,
        qualityIssues: metric.qualityIssues,
        onTimeDeliveryRate: metric.onTimeDeliveryRate,
        averageLeadTime: metric.averageLeadTime,
        defectRate: metric.defectRate,
        fillRate: metric.fillRate,
        createdAt: metric.createdAt,
      })),
    };
  }
}
