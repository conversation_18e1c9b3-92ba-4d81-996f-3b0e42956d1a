import { Injectable, Logger, NotFoundException, BadRequestException, ForbiddenException } from '@nestjs/common';
import { PrismaService } from '@core/prisma/prisma.service';
import { DecimalService } from '@shared/decimal/decimal.service';
import { WebsocketService } from '@infra/websocket/websocket.service';
import { RabbitMQService } from '@infra/rabbitmq/rabbitmq.service';
import { MongoDbService } from '@infra/mongodb/mongodb.service';
import { 
  CreatePurchaseOrderDto,
  UpdatePurchaseOrderDto,
  UpdatePurchaseOrderStatusDto,
  ApprovePurchaseOrderDto,
  ReceivePurchaseOrderDto,
  PurchaseOrderStatus,
  OrderPriority,
  PaymentStatus
} from '../dto/purchase-order.dto';

@Injectable()
export class PurchaseOrderService {
  private readonly logger = new Logger(PurchaseOrderService.name);

  constructor(
    private readonly prisma: PrismaService,
    private readonly decimalService: DecimalService,
    private readonly websocketService: WebsocketService,
    private readonly rabbitMQService: RabbitMQService,
    private readonly mongoDbService: MongoDbService,
  ) {}

  async createPurchaseOrder(createPurchaseOrderDto: CreatePurchaseOrderDto, requestedBy: string): Promise<any> {
    try {
      // Validate supplier exists
      const supplier = await this.prisma.supplier.findUnique({
        where: { id: createPurchaseOrderDto.supplierId },
      });

      if (!supplier) {
        throw new NotFoundException(`Supplier with ID ${createPurchaseOrderDto.supplierId} not found`);
      }

      if (supplier.status !== 'ACTIVE') {
        throw new BadRequestException('Cannot create purchase order for inactive supplier');
      }

      // Generate unique order number
      const orderNumber = await this.generateOrderNumber();

      // Calculate totals
      let subtotal = this.decimalService.create('0');
      let totalDiscountAmount = this.decimalService.create('0');

      const processedItems = createPurchaseOrderDto.items.map(item => {
        const unitPrice = this.decimalService.create(item.unitPrice);
        const quantity = this.decimalService.create(item.quantityOrdered.toString());
        const itemTotal = unitPrice.mul(quantity);
        
        const discountPercent = this.decimalService.create(item.discountPercent || '0');
        const discountAmount = itemTotal.mul(discountPercent).div('100');
        const itemTotalAfterDiscount = itemTotal.sub(discountAmount);

        subtotal = subtotal.add(itemTotalAfterDiscount);
        totalDiscountAmount = totalDiscountAmount.add(discountAmount);

        return {
          ...item,
          totalPrice: itemTotalAfterDiscount.toString(),
          discountAmount: discountAmount.toString(),
        };
      });

      // For now, assume no tax and shipping (can be configured later)
      const taxAmount = this.decimalService.create('0');
      const shippingCost = this.decimalService.create('0');
      const totalAmount = subtotal.add(taxAmount).add(shippingCost);

      // Create purchase order
      const purchaseOrder = await this.prisma.purchaseOrder.create({
        data: {
          orderNumber,
          supplierId: createPurchaseOrderDto.supplierId,
          requestedBy,
          status: PurchaseOrderStatus.DRAFT,
          priority: createPurchaseOrderDto.priority || OrderPriority.NORMAL,
          orderType: createPurchaseOrderDto.orderType || 'STANDARD',
          subtotal: subtotal.toString(),
          taxAmount: taxAmount.toString(),
          shippingCost: shippingCost.toString(),
          discountAmount: totalDiscountAmount.toString(),
          totalAmount: totalAmount.toString(),
          currency: supplier.currency || 'USD',
          paymentTerms: createPurchaseOrderDto.paymentTerms || supplier.paymentTerms,
          paymentMethod: createPurchaseOrderDto.paymentMethod,
          paymentStatus: PaymentStatus.PENDING,
          requestedDeliveryDate: createPurchaseOrderDto.requestedDeliveryDate 
            ? new Date(createPurchaseOrderDto.requestedDeliveryDate) 
            : null,
          deliveryAddress: createPurchaseOrderDto.deliveryAddress,
          shippingMethod: createPurchaseOrderDto.shippingMethod,
          notes: createPurchaseOrderDto.notes,
          internalNotes: createPurchaseOrderDto.internalNotes,
          specialInstructions: createPurchaseOrderDto.specialInstructions,
          items: {
            create: processedItems.map(item => ({
              inventoryItemId: item.inventoryItemId,
              supplierCatalogId: item.supplierCatalogId,
              itemName: item.itemName,
              itemCode: item.itemCode,
              description: item.description,
              specifications: item.specifications,
              quantityOrdered: item.quantityOrdered,
              unitPrice: item.unitPrice,
              totalPrice: item.totalPrice,
              discountPercent: item.discountPercent || '0.00',
              discountAmount: item.discountAmount,
              expectedDeliveryDate: item.expectedDeliveryDate 
                ? new Date(item.expectedDeliveryDate) 
                : null,
            })),
          },
        },
        include: {
          supplier: true,
          requester: {
            select: { id: true, firstName: true, lastName: true, email: true },
          },
          items: {
            include: {
              inventoryItem: true,
              supplierCatalog: true,
            },
          },
        },
      });

      // Check if approval is required
      const requiresApproval = await this.checkApprovalRequired(totalAmount.toString(), requestedBy);
      
      if (requiresApproval) {
        await this.createApprovalRequest(purchaseOrder.id, totalAmount.toString());
      }

      // Sync to MongoDB
      await this.mongoDbService.syncDocument('purchase_orders', purchaseOrder);

      // Send notifications
      await this.notifyPurchaseOrderCreated(purchaseOrder);

      // Publish event
      await this.rabbitMQService.publish({
        type: 'purchase_order_created',
        data: { purchaseOrder },
      });

      return purchaseOrder;
    } catch (error) {
      this.logger.error(`Failed to create purchase order: ${error.message}`, error.stack);
      throw error;
    }
  }

  async getPurchaseOrders(
    status?: PurchaseOrderStatus,
    supplierId?: string,
    requestedBy?: string,
    priority?: OrderPriority,
    page = 1,
    limit = 20,
  ): Promise<{ orders: any[]; total: number; page: number; totalPages: number }> {
    try {
      const where: any = {};
      
      if (status) where.status = status;
      if (supplierId) where.supplierId = supplierId;
      if (requestedBy) where.requestedBy = requestedBy;
      if (priority) where.priority = priority;

      const [orders, total] = await Promise.all([
        this.prisma.purchaseOrder.findMany({
          where,
          include: {
            supplier: {
              select: { id: true, companyName: true, email: true, status: true },
            },
            requester: {
              select: { id: true, firstName: true, lastName: true, email: true },
            },
            approver: {
              select: { id: true, firstName: true, lastName: true, email: true },
            },
            items: {
              include: {
                inventoryItem: {
                  select: { id: true, itemName: true, itemCode: true },
                },
                supplierCatalog: {
                  select: { id: true, itemName: true, itemCode: true },
                },
              },
            },
            approvals: {
              include: {
                approver: {
                  select: { id: true, firstName: true, lastName: true, email: true },
                },
              },
            },
          },
          orderBy: { createdAt: 'desc' },
          skip: (page - 1) * limit,
          take: limit,
        }),
        this.prisma.purchaseOrder.count({ where }),
      ]);

      return {
        orders,
        total,
        page,
        totalPages: Math.ceil(total / limit),
      };
    } catch (error) {
      this.logger.error(`Failed to get purchase orders: ${error.message}`, error.stack);
      throw error;
    }
  }

  async getPurchaseOrderById(id: string): Promise<any> {
    try {
      const order = await this.prisma.purchaseOrder.findUnique({
        where: { id },
        include: {
          supplier: true,
          requester: {
            select: { id: true, firstName: true, lastName: true, email: true },
          },
          approver: {
            select: { id: true, firstName: true, lastName: true, email: true },
          },
          items: {
            include: {
              inventoryItem: true,
              supplierCatalog: true,
            },
          },
          approvals: {
            include: {
              approver: {
                select: { id: true, firstName: true, lastName: true, email: true },
              },
            },
            orderBy: { requestedAt: 'desc' },
          },
          deliveries: {
            include: {
              receiver: {
                select: { id: true, firstName: true, lastName: true, email: true },
              },
              qualityChecker: {
                select: { id: true, firstName: true, lastName: true, email: true },
              },
            },
            orderBy: { deliveryDate: 'desc' },
          },
        },
      });

      if (!order) {
        throw new NotFoundException(`Purchase order with ID ${id} not found`);
      }

      return order;
    } catch (error) {
      this.logger.error(`Failed to get purchase order: ${error.message}`, error.stack);
      throw error;
    }
  }

  async updatePurchaseOrder(
    id: string, 
    updatePurchaseOrderDto: UpdatePurchaseOrderDto, 
    updatedBy: string
  ): Promise<any> {
    try {
      const existingOrder = await this.prisma.purchaseOrder.findUnique({
        where: { id },
      });

      if (!existingOrder) {
        throw new NotFoundException(`Purchase order with ID ${id} not found`);
      }

      // Check if order can be updated
      if (!['DRAFT', 'PENDING_APPROVAL'].includes(existingOrder.status)) {
        throw new BadRequestException('Cannot update purchase order in current status');
      }

      // Check permissions
      if (existingOrder.requestedBy !== updatedBy) {
        // Check if user has admin role or purchase order management permissions
        const user = await this.prisma.user.findUnique({
          where: { id: updatedBy },
        });

        if (!user?.roles.includes('admin') && !user?.permissions.includes('manage_purchase_orders')) {
          throw new ForbiddenException('Insufficient permissions to update this purchase order');
        }
      }

      const order = await this.prisma.purchaseOrder.update({
        where: { id },
        data: {
          ...updatePurchaseOrderDto,
          requestedDeliveryDate: updatePurchaseOrderDto.requestedDeliveryDate 
            ? new Date(updatePurchaseOrderDto.requestedDeliveryDate) 
            : undefined,
        },
        include: {
          supplier: true,
          requester: {
            select: { id: true, firstName: true, lastName: true, email: true },
          },
          items: {
            include: {
              inventoryItem: true,
              supplierCatalog: true,
            },
          },
        },
      });

      // Sync to MongoDB
      await this.mongoDbService.syncDocument('purchase_orders', order);

      // Send notifications
      await this.notifyPurchaseOrderUpdated(order);

      // Publish event
      await this.rabbitMQService.publish({
        type: 'purchase_order_updated',
        data: { order, previousData: existingOrder },
      });

      return order;
    } catch (error) {
      this.logger.error(`Failed to update purchase order: ${error.message}`, error.stack);
      throw error;
    }
  }

  async updatePurchaseOrderStatus(
    id: string, 
    updateStatusDto: UpdatePurchaseOrderStatusDto, 
    updatedBy: string
  ): Promise<any> {
    try {
      const existingOrder = await this.prisma.purchaseOrder.findUnique({
        where: { id },
        include: { supplier: true },
      });

      if (!existingOrder) {
        throw new NotFoundException(`Purchase order with ID ${id} not found`);
      }

      // Validate status transition
      const validTransitions = this.getValidStatusTransitions(existingOrder.status);
      if (!validTransitions.includes(updateStatusDto.status)) {
        throw new BadRequestException(
          `Invalid status transition from ${existingOrder.status} to ${updateStatusDto.status}`
        );
      }

      const updateData: any = {
        status: updateStatusDto.status,
      };

      // Set timestamps based on status
      switch (updateStatusDto.status) {
        case PurchaseOrderStatus.SENT:
          updateData.sentToSupplier = new Date();
          break;
        case PurchaseOrderStatus.ACKNOWLEDGED:
          updateData.acknowledgedAt = new Date();
          break;
        case PurchaseOrderStatus.APPROVED:
          updateData.approvedAt = new Date();
          updateData.approvedBy = updatedBy;
          break;
      }

      const order = await this.prisma.purchaseOrder.update({
        where: { id },
        data: updateData,
        include: {
          supplier: true,
          requester: {
            select: { id: true, firstName: true, lastName: true, email: true },
          },
          items: true,
        },
      });

      // Sync to MongoDB
      await this.mongoDbService.syncDocument('purchase_orders', order);

      // Send notifications
      await this.notifyPurchaseOrderStatusChanged(order, updateStatusDto.status, updateStatusDto.notes);

      // Publish event
      await this.rabbitMQService.publish({
        type: 'purchase_order_status_changed',
        data: { 
          order, 
          previousStatus: existingOrder.status, 
          newStatus: updateStatusDto.status,
          notes: updateStatusDto.notes,
          updatedBy 
        },
      });

      return order;
    } catch (error) {
      this.logger.error(`Failed to update purchase order status: ${error.message}`, error.stack);
      throw error;
    }
  }

  async approvePurchaseOrder(approvePurchaseOrderDto: ApprovePurchaseOrderDto, approverId: string): Promise<any> {
    try {
      const order = await this.prisma.purchaseOrder.findUnique({
        where: { id: approvePurchaseOrderDto.purchaseOrderId },
        include: { approvals: true },
      });

      if (!order) {
        throw new NotFoundException(`Purchase order with ID ${approvePurchaseOrderDto.purchaseOrderId} not found`);
      }

      if (order.status !== PurchaseOrderStatus.PENDING_APPROVAL) {
        throw new BadRequestException('Purchase order is not pending approval');
      }

      // Check if user has approval permissions
      const approver = await this.prisma.user.findUnique({
        where: { id: approverId },
      });

      if (!approver?.roles.includes('admin') && !approver?.permissions.includes('approve_purchase_orders')) {
        throw new ForbiddenException('Insufficient permissions to approve purchase orders');
      }

      // Create approval record
      const approval = await this.prisma.purchaseOrderApproval.create({
        data: {
          purchaseOrderId: approvePurchaseOrderDto.purchaseOrderId,
          approverId,
          approvalLevel: 1, // For now, single-level approval
          status: approvePurchaseOrderDto.decision === 'APPROVED' ? 'APPROVED' : 'REJECTED',
          decision: approvePurchaseOrderDto.decision,
          comments: approvePurchaseOrderDto.comments,
          conditions: approvePurchaseOrderDto.conditions || [],
          respondedAt: new Date(),
        },
      });

      // Update purchase order status
      const newStatus = approvePurchaseOrderDto.decision === 'APPROVED' 
        ? PurchaseOrderStatus.APPROVED 
        : PurchaseOrderStatus.REJECTED;

      const updatedOrder = await this.prisma.purchaseOrder.update({
        where: { id: approvePurchaseOrderDto.purchaseOrderId },
        data: {
          status: newStatus,
          approvedBy: approvePurchaseOrderDto.decision === 'APPROVED' ? approverId : null,
          approvedAt: approvePurchaseOrderDto.decision === 'APPROVED' ? new Date() : null,
        },
        include: {
          supplier: true,
          requester: {
            select: { id: true, firstName: true, lastName: true, email: true },
          },
          items: true,
        },
      });

      // Sync to MongoDB
      await this.mongoDbService.syncDocument('purchase_orders', updatedOrder);
      await this.mongoDbService.syncDocument('purchase_order_approvals', approval);

      // Send notifications
      await this.notifyPurchaseOrderApprovalDecision(updatedOrder, approvePurchaseOrderDto.decision, approvePurchaseOrderDto.comments);

      // Publish event
      await this.rabbitMQService.publish({
        type: 'purchase_order_approval_decision',
        data: { 
          order: updatedOrder, 
          approval,
          decision: approvePurchaseOrderDto.decision,
          approverId 
        },
      });

      return { order: updatedOrder, approval };
    } catch (error) {
      this.logger.error(`Failed to approve purchase order: ${error.message}`, error.stack);
      throw error;
    }
  }

  // Private helper methods
  private async generateOrderNumber(): Promise<string> {
    const count = await this.prisma.purchaseOrder.count();
    const year = new Date().getFullYear();
    return `PO-${year}-${String(count + 1).padStart(6, '0')}`;
  }

  private async checkApprovalRequired(totalAmount: string, requestedBy: string): Promise<boolean> {
    // Simple approval logic - orders over $1000 require approval
    // In a real system, this would be configurable based on user roles and limits
    const amount = this.decimalService.create(totalAmount);
    const approvalThreshold = this.decimalService.create('1000.00');
    
    return amount.gte(approvalThreshold);
  }

  private async createApprovalRequest(purchaseOrderId: string, totalAmount: string): Promise<void> {
    // Update order status to pending approval
    await this.prisma.purchaseOrder.update({
      where: { id: purchaseOrderId },
      data: { status: PurchaseOrderStatus.PENDING_APPROVAL },
    });

    // Create approval request (for now, just update status - in real system, would create approval workflow)
    await this.prisma.purchaseOrderApproval.create({
      data: {
        purchaseOrderId,
        approverId: 'system', // Would be determined by approval workflow
        approvalLevel: 1,
        status: 'PENDING',
        approvalLimit: totalAmount,
      },
    });
  }

  private getValidStatusTransitions(currentStatus: string): string[] {
    const transitions: Record<string, string[]> = {
      [PurchaseOrderStatus.DRAFT]: [PurchaseOrderStatus.PENDING_APPROVAL, PurchaseOrderStatus.APPROVED, PurchaseOrderStatus.CANCELLED],
      [PurchaseOrderStatus.PENDING_APPROVAL]: [PurchaseOrderStatus.APPROVED, PurchaseOrderStatus.REJECTED, PurchaseOrderStatus.CANCELLED],
      [PurchaseOrderStatus.APPROVED]: [PurchaseOrderStatus.SENT, PurchaseOrderStatus.CANCELLED],
      [PurchaseOrderStatus.SENT]: [PurchaseOrderStatus.ACKNOWLEDGED, PurchaseOrderStatus.CANCELLED],
      [PurchaseOrderStatus.ACKNOWLEDGED]: [PurchaseOrderStatus.IN_PROGRESS, PurchaseOrderStatus.CANCELLED],
      [PurchaseOrderStatus.IN_PROGRESS]: [PurchaseOrderStatus.PARTIALLY_RECEIVED, PurchaseOrderStatus.COMPLETED, PurchaseOrderStatus.CANCELLED],
      [PurchaseOrderStatus.PARTIALLY_RECEIVED]: [PurchaseOrderStatus.COMPLETED, PurchaseOrderStatus.CANCELLED],
    };

    return transitions[currentStatus] || [];
  }

  private async notifyPurchaseOrderCreated(order: any): Promise<void> {
    // Notify admins and purchase managers
    await this.websocketService.sendToRole('admin', 'purchase_order_created', {
      message: `New purchase order ${order.orderNumber} created`,
      order: {
        id: order.id,
        orderNumber: order.orderNumber,
        supplier: order.supplier.companyName,
        totalAmount: order.totalAmount,
        status: order.status,
      },
    });

    // Notify requester
    await this.websocketService.sendToUser(order.requester.email, 'purchase_order_created', {
      message: `Your purchase order ${order.orderNumber} has been created`,
      orderId: order.id,
      orderNumber: order.orderNumber,
      status: order.status,
    });
  }

  private async notifyPurchaseOrderUpdated(order: any): Promise<void> {
    // Notify requester
    await this.websocketService.sendToUser(order.requester.email, 'purchase_order_updated', {
      message: `Purchase order ${order.orderNumber} has been updated`,
      orderId: order.id,
      orderNumber: order.orderNumber,
      status: order.status,
    });
  }

  private async notifyPurchaseOrderStatusChanged(order: any, newStatus: string, notes?: string): Promise<void> {
    // Notify requester
    await this.websocketService.sendToUser(order.requester.email, 'purchase_order_status_changed', {
      message: `Purchase order ${order.orderNumber} status changed to ${newStatus}`,
      orderId: order.id,
      orderNumber: order.orderNumber,
      newStatus,
      notes,
    });

    // Notify admins
    await this.websocketService.sendToRole('admin', 'purchase_order_status_changed', {
      message: `Purchase order ${order.orderNumber} status changed to ${newStatus}`,
      order: {
        id: order.id,
        orderNumber: order.orderNumber,
        supplier: order.supplier.companyName,
        newStatus,
      },
    });
  }

  private async notifyPurchaseOrderApprovalDecision(order: any, decision: string, comments?: string): Promise<void> {
    const message = decision === 'APPROVED' 
      ? `Purchase order ${order.orderNumber} has been approved`
      : `Purchase order ${order.orderNumber} has been rejected${comments ? `: ${comments}` : ''}`;

    // Notify requester
    await this.websocketService.sendToUser(order.requester.email, 'purchase_order_approval_decision', {
      message,
      orderId: order.id,
      orderNumber: order.orderNumber,
      decision,
      comments,
    });
  }

  async receivePurchaseOrder(
    id: string,
    receivePurchaseOrderDto: any,
    receivedBy: string
  ): Promise<any> {
    try {
      const order = await this.prisma.purchaseOrder.findUnique({
        where: { id },
        include: { items: true },
      });

      if (!order) {
        throw new NotFoundException(`Purchase order with ID ${id} not found`);
      }

      // Validate order can be received
      if (!['SENT', 'ACKNOWLEDGED', 'IN_PROGRESS'].includes(order.status)) {
        throw new BadRequestException('Purchase order cannot be received in current status');
      }

      // Create delivery record
      const deliveryNumber = await this.generateDeliveryNumber();

      const delivery = await this.prisma.purchaseOrderDelivery.create({
        data: {
          purchaseOrderId: id,
          deliveryNumber,
          deliveryDate: new Date(),
          deliveryMethod: receivePurchaseOrderDto.deliveryMethod || 'DELIVERY',
          trackingNumber: receivePurchaseOrderDto.trackingNumber,
          receivedBy,
          status: 'DELIVERED',
          isPartialDelivery: receivePurchaseOrderDto.isPartialDelivery || false,
          qualityCheckStatus: receivePurchaseOrderDto.qualityCheckStatus || 'PENDING',
          qualityNotes: receivePurchaseOrderDto.qualityNotes,
          deliveryNotes: receivePurchaseOrderDto.deliveryNotes,
          issues: receivePurchaseOrderDto.issues || [],
        },
      });

      // Update order status
      const newStatus = receivePurchaseOrderDto.isPartialDelivery
        ? PurchaseOrderStatus.PARTIALLY_RECEIVED
        : PurchaseOrderStatus.COMPLETED;

      const updatedOrder = await this.prisma.purchaseOrder.update({
        where: { id },
        data: {
          status: newStatus,
          actualDeliveryDate: new Date(),
        },
        include: {
          supplier: true,
          requester: {
            select: { id: true, firstName: true, lastName: true, email: true },
          },
          items: true,
        },
      });

      // Sync to MongoDB
      await this.mongoDbService.syncDocument('purchase_orders', updatedOrder);
      await this.mongoDbService.syncDocument('purchase_order_deliveries', delivery);

      // Send notifications
      await this.notifyPurchaseOrderReceived(updatedOrder, delivery);

      // Publish event
      await this.rabbitMQService.publish({
        type: 'purchase_order_received',
        data: { order: updatedOrder, delivery, receivedBy },
      });

      return { order: updatedOrder, delivery };
    } catch (error) {
      this.logger.error(`Failed to receive purchase order: ${error.message}`, error.stack);
      throw error;
    }
  }

  private async generateDeliveryNumber(): Promise<string> {
    const count = await this.prisma.purchaseOrderDelivery.count();
    const year = new Date().getFullYear();
    return `DEL-${year}-${String(count + 1).padStart(6, '0')}`;
  }

  private async notifyPurchaseOrderReceived(order: any, delivery: any): Promise<void> {
    // Notify requester
    await this.websocketService.sendToUser(order.requester.email, 'purchase_order_received', {
      message: `Purchase order ${order.orderNumber} has been received`,
      orderId: order.id,
      orderNumber: order.orderNumber,
      deliveryNumber: delivery.deliveryNumber,
    });

    // Notify admins
    await this.websocketService.sendToRole('admin', 'purchase_order_received', {
      message: `Purchase order ${order.orderNumber} received`,
      order: {
        id: order.id,
        orderNumber: order.orderNumber,
        supplier: order.supplier.companyName,
        deliveryNumber: delivery.deliveryNumber,
      },
    });
  }
}
