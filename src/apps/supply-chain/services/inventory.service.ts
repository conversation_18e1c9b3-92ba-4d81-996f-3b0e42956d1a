import { Injectable, Logger, NotFoundException, BadRequestException, ConflictException } from '@nestjs/common';
import { PrismaService } from '@core/prisma/prisma.service';
import { DecimalService } from '@shared/decimal/decimal.service';
import { WebsocketService } from '@infra/websocket/websocket.service';
import { RabbitMQService } from '@infra/rabbitmq/rabbitmq.service';
import { MongoDbService } from '@infra/mongodb/mongodb.service';
import { 
  CreateInventoryCategoryDto, 
  UpdateInventoryCategoryDto,
  CreateInventoryItemDto,
  UpdateInventoryItemDto,
  CreateInventoryTransactionDto,
  AdjustInventoryDto,
  InventoryItemType,
  InventoryStatus,
  InventoryTransactionType,
  StockAlertType
} from '../dto/inventory.dto';

@Injectable()
export class InventoryService {
  private readonly logger = new Logger(InventoryService.name);

  constructor(
    private readonly prisma: PrismaService,
    private readonly decimalService: DecimalService,
    private readonly websocketService: WebsocketService,
    private readonly rabbitMQService: RabbitMQService,
    private readonly mongoDbService: MongoDbService,
  ) {}

  // ===== INVENTORY CATEGORIES =====

  async createCategory(createCategoryDto: CreateInventoryCategoryDto): Promise<any> {
    try {
      // Check if category name already exists
      const existingCategory = await this.prisma.inventoryCategory.findUnique({
        where: { name: createCategoryDto.name },
      });

      if (existingCategory) {
        throw new ConflictException(`Category with name ${createCategoryDto.name} already exists`);
      }

      // Validate parent category if provided
      if (createCategoryDto.parentId) {
        const parentCategory = await this.prisma.inventoryCategory.findUnique({
          where: { id: createCategoryDto.parentId },
        });

        if (!parentCategory) {
          throw new NotFoundException(`Parent category with ID ${createCategoryDto.parentId} not found`);
        }
      }

      const category = await this.prisma.inventoryCategory.create({
        data: createCategoryDto,
        include: {
          parent: true,
          children: true,
          inventoryItems: true,
        },
      });

      // Sync to MongoDB
      await this.mongoDbService.syncDocument('inventory_categories', category);

      // Publish event
      await this.rabbitMQService.publish({
        type: 'inventory_category_created',
        data: { category },
      });

      return category;
    } catch (error) {
      this.logger.error(`Failed to create inventory category: ${error.message}`, error.stack);
      throw error;
    }
  }

  async getCategories(): Promise<any[]> {
    try {
      const categories = await this.prisma.inventoryCategory.findMany({
        include: {
          parent: true,
          children: true,
          inventoryItems: {
            select: { id: true, itemName: true, currentStock: true, status: true },
          },
        },
        orderBy: { name: 'asc' },
      });

      return categories;
    } catch (error) {
      this.logger.error(`Failed to get inventory categories: ${error.message}`, error.stack);
      throw error;
    }
  }

  async updateCategory(id: string, updateCategoryDto: UpdateInventoryCategoryDto): Promise<any> {
    try {
      const existingCategory = await this.prisma.inventoryCategory.findUnique({
        where: { id },
      });

      if (!existingCategory) {
        throw new NotFoundException(`Category with ID ${id} not found`);
      }

      // Check name uniqueness if name is being updated
      if (updateCategoryDto.name && updateCategoryDto.name !== existingCategory.name) {
        const nameExists = await this.prisma.inventoryCategory.findUnique({
          where: { name: updateCategoryDto.name },
        });

        if (nameExists) {
          throw new ConflictException(`Category with name ${updateCategoryDto.name} already exists`);
        }
      }

      // Validate parent category if being updated
      if (updateCategoryDto.parentId) {
        const parentCategory = await this.prisma.inventoryCategory.findUnique({
          where: { id: updateCategoryDto.parentId },
        });

        if (!parentCategory) {
          throw new NotFoundException(`Parent category with ID ${updateCategoryDto.parentId} not found`);
        }

        // Prevent circular reference
        if (updateCategoryDto.parentId === id) {
          throw new BadRequestException('Category cannot be its own parent');
        }
      }

      const category = await this.prisma.inventoryCategory.update({
        where: { id },
        data: updateCategoryDto,
        include: {
          parent: true,
          children: true,
          inventoryItems: true,
        },
      });

      // Sync to MongoDB
      await this.mongoDbService.syncDocument('inventory_categories', category);

      // Publish event
      await this.rabbitMQService.publish({
        type: 'inventory_category_updated',
        data: { category, previousData: existingCategory },
      });

      return category;
    } catch (error) {
      this.logger.error(`Failed to update inventory category: ${error.message}`, error.stack);
      throw error;
    }
  }

  // ===== INVENTORY ITEMS =====

  async createInventoryItem(createItemDto: CreateInventoryItemDto, createdBy?: string): Promise<any> {
    try {
      // Check if item code already exists
      const existingItem = await this.prisma.inventoryItem.findUnique({
        where: { itemCode: createItemDto.itemCode },
      });

      if (existingItem) {
        throw new ConflictException(`Item with code ${createItemDto.itemCode} already exists`);
      }

      // Validate category exists
      const category = await this.prisma.inventoryCategory.findUnique({
        where: { id: createItemDto.categoryId },
      });

      if (!category) {
        throw new NotFoundException(`Category with ID ${createItemDto.categoryId} not found`);
      }

      // Validate supplier if provided
      if (createItemDto.supplierId) {
        const supplier = await this.prisma.supplier.findUnique({
          where: { id: createItemDto.supplierId },
        });

        if (!supplier) {
          throw new NotFoundException(`Supplier with ID ${createItemDto.supplierId} not found`);
        }
      }

      // Calculate available stock
      const availableStock = createItemDto.currentStock;

      const item = await this.prisma.inventoryItem.create({
        data: {
          ...createItemDto,
          availableStock,
          averageCost: createItemDto.unitCost,
          lastPurchasePrice: createItemDto.unitCost,
          status: InventoryStatus.ACTIVE,
          createdBy,
        },
        include: {
          category: true,
          supplier: true,
          supplierCatalog: true,
        },
      });

      // Create initial inventory transaction
      await this.createInventoryTransaction({
        inventoryItemId: item.id,
        transactionType: InventoryTransactionType.ADJUSTMENT,
        quantity: createItemDto.currentStock,
        unitCost: createItemDto.unitCost,
        reference: 'INITIAL_STOCK',
        referenceType: 'INITIAL_SETUP',
        notes: 'Initial stock setup',
      }, createdBy);

      // Check for stock alerts
      await this.checkStockAlerts(item.id);

      // Sync to MongoDB
      await this.mongoDbService.syncDocument('inventory_items', item);

      // Publish event
      await this.rabbitMQService.publish({
        type: 'inventory_item_created',
        data: { item },
      });

      return item;
    } catch (error) {
      this.logger.error(`Failed to create inventory item: ${error.message}`, error.stack);
      throw error;
    }
  }

  async getInventoryItems(
    categoryId?: string,
    itemType?: InventoryItemType,
    status?: InventoryStatus,
    lowStock?: boolean,
    search?: string,
    page = 1,
    limit = 20,
  ): Promise<{ items: any[]; total: number; page: number; totalPages: number }> {
    try {
      const where: any = {};
      
      if (categoryId) where.categoryId = categoryId;
      if (itemType) where.itemType = itemType;
      if (status) where.status = status;
      if (lowStock) {
        where.currentStock = { lte: this.prisma.inventoryItem.fields.minimumStock };
      }
      if (search) {
        where.OR = [
          { itemName: { contains: search, mode: 'insensitive' } },
          { itemCode: { contains: search, mode: 'insensitive' } },
          { description: { contains: search, mode: 'insensitive' } },
        ];
      }

      const [items, total] = await Promise.all([
        this.prisma.inventoryItem.findMany({
          where,
          include: {
            category: true,
            supplier: true,
            supplierCatalog: true,
            stockAlerts: {
              where: { status: 'ACTIVE' },
              orderBy: { createdAt: 'desc' },
            },
          },
          orderBy: { itemName: 'asc' },
          skip: (page - 1) * limit,
          take: limit,
        }),
        this.prisma.inventoryItem.count({ where }),
      ]);

      return {
        items,
        total,
        page,
        totalPages: Math.ceil(total / limit),
      };
    } catch (error) {
      this.logger.error(`Failed to get inventory items: ${error.message}`, error.stack);
      throw error;
    }
  }

  async getInventoryItemById(id: string): Promise<any> {
    try {
      const item = await this.prisma.inventoryItem.findUnique({
        where: { id },
        include: {
          category: true,
          supplier: true,
          supplierCatalog: true,
          transactions: {
            orderBy: { transactionDate: 'desc' },
            take: 10,
          },
          stockAlerts: {
            where: { status: 'ACTIVE' },
            orderBy: { createdAt: 'desc' },
          },
          valuations: {
            orderBy: { valuationDate: 'desc' },
            take: 5,
          },
        },
      });

      if (!item) {
        throw new NotFoundException(`Inventory item with ID ${id} not found`);
      }

      return item;
    } catch (error) {
      this.logger.error(`Failed to get inventory item: ${error.message}`, error.stack);
      throw error;
    }
  }

  async updateInventoryItem(id: string, updateItemDto: UpdateInventoryItemDto, updatedBy?: string): Promise<any> {
    try {
      const existingItem = await this.prisma.inventoryItem.findUnique({
        where: { id },
      });

      if (!existingItem) {
        throw new NotFoundException(`Inventory item with ID ${id} not found`);
      }

      // Validate category if being updated
      if (updateItemDto.categoryId) {
        const category = await this.prisma.inventoryCategory.findUnique({
          where: { id: updateItemDto.categoryId },
        });

        if (!category) {
          throw new NotFoundException(`Category with ID ${updateItemDto.categoryId} not found`);
        }
      }

      const item = await this.prisma.inventoryItem.update({
        where: { id },
        data: {
          ...updateItemDto,
          updatedBy,
        },
        include: {
          category: true,
          supplier: true,
          supplierCatalog: true,
        },
      });

      // Check for stock alerts if minimum stock changed
      if (updateItemDto.minimumStock !== undefined) {
        await this.checkStockAlerts(item.id);
      }

      // Sync to MongoDB
      await this.mongoDbService.syncDocument('inventory_items', item);

      // Publish event
      await this.rabbitMQService.publish({
        type: 'inventory_item_updated',
        data: { item, previousData: existingItem },
      });

      return item;
    } catch (error) {
      this.logger.error(`Failed to update inventory item: ${error.message}`, error.stack);
      throw error;
    }
  }

  // ===== INVENTORY TRANSACTIONS =====

  async createInventoryTransaction(
    createTransactionDto: CreateInventoryTransactionDto, 
    createdBy?: string
  ): Promise<any> {
    try {
      const item = await this.prisma.inventoryItem.findUnique({
        where: { id: createTransactionDto.inventoryItemId },
      });

      if (!item) {
        throw new NotFoundException(`Inventory item with ID ${createTransactionDto.inventoryItemId} not found`);
      }

      const stockBefore = item.currentStock;
      const stockAfter = stockBefore + createTransactionDto.quantity;

      // Validate stock levels
      if (stockAfter < 0) {
        throw new BadRequestException('Insufficient stock for this transaction');
      }

      // Calculate total cost
      const unitCost = createTransactionDto.unitCost || item.unitCost;
      const totalCost = this.decimalService.create(unitCost)
        .mul(Math.abs(createTransactionDto.quantity))
        .toString();

      // Create transaction
      const transaction = await this.prisma.inventoryTransaction.create({
        data: {
          ...createTransactionDto,
          unitCost,
          totalCost,
          stockBefore,
          stockAfter,
          createdBy,
        },
        include: {
          inventoryItem: true,
        },
      });

      // Update item stock levels
      const reservedStock = item.reservedStock;
      const availableStock = stockAfter - reservedStock;

      await this.prisma.inventoryItem.update({
        where: { id: createTransactionDto.inventoryItemId },
        data: {
          currentStock: stockAfter,
          availableStock,
          lastPurchasePrice: createTransactionDto.transactionType === InventoryTransactionType.PURCHASE 
            ? unitCost 
            : item.lastPurchasePrice,
        },
      });

      // Update average cost for purchase transactions
      if (createTransactionDto.transactionType === InventoryTransactionType.PURCHASE) {
        await this.updateAverageCost(createTransactionDto.inventoryItemId);
      }

      // Check for stock alerts
      await this.checkStockAlerts(createTransactionDto.inventoryItemId);

      // Sync to MongoDB
      await this.mongoDbService.syncDocument('inventory_transactions', transaction);

      // Publish event
      await this.rabbitMQService.publish({
        type: 'inventory_transaction_created',
        data: { transaction },
      });

      return transaction;
    } catch (error) {
      this.logger.error(`Failed to create inventory transaction: ${error.message}`, error.stack);
      throw error;
    }
  }

  async adjustInventory(adjustInventoryDto: AdjustInventoryDto, adjustedBy?: string): Promise<any> {
    try {
      const item = await this.prisma.inventoryItem.findUnique({
        where: { id: adjustInventoryDto.inventoryItemId },
      });

      if (!item) {
        throw new NotFoundException(`Inventory item with ID ${adjustInventoryDto.inventoryItemId} not found`);
      }

      const currentStock = item.currentStock;
      const adjustmentQuantity = adjustInventoryDto.newQuantity - currentStock;

      if (adjustmentQuantity === 0) {
        throw new BadRequestException('No adjustment needed - new quantity equals current stock');
      }

      // Create adjustment transaction
      const transaction = await this.createInventoryTransaction({
        inventoryItemId: adjustInventoryDto.inventoryItemId,
        transactionType: InventoryTransactionType.ADJUSTMENT,
        quantity: adjustmentQuantity,
        reference: 'MANUAL_ADJUSTMENT',
        referenceType: 'STOCK_ADJUSTMENT',
        notes: `${adjustInventoryDto.reason}${adjustInventoryDto.notes ? ` - ${adjustInventoryDto.notes}` : ''}`,
      }, adjustedBy);

      return transaction;
    } catch (error) {
      this.logger.error(`Failed to adjust inventory: ${error.message}`, error.stack);
      throw error;
    }
  }

  // ===== STOCK ALERTS =====

  async checkStockAlerts(inventoryItemId: string): Promise<void> {
    try {
      const item = await this.prisma.inventoryItem.findUnique({
        where: { id: inventoryItemId },
      });

      if (!item) return;

      const alerts: any[] = [];

      // Check for low stock
      if (item.currentStock <= item.minimumStock && item.currentStock > 0) {
        alerts.push({
          inventoryItemId,
          alertType: StockAlertType.LOW_STOCK,
          threshold: item.minimumStock,
          currentStock: item.currentStock,
          message: `${item.itemName} is running low (${item.currentStock} remaining, minimum: ${item.minimumStock})`,
        });
      }

      // Check for out of stock
      if (item.currentStock === 0) {
        alerts.push({
          inventoryItemId,
          alertType: StockAlertType.OUT_OF_STOCK,
          threshold: 0,
          currentStock: item.currentStock,
          message: `${item.itemName} is out of stock`,
        });
      }

      // Check for overstock
      if (item.maximumStock && item.currentStock > item.maximumStock) {
        alerts.push({
          inventoryItemId,
          alertType: StockAlertType.OVERSTOCK,
          threshold: item.maximumStock,
          currentStock: item.currentStock,
          message: `${item.itemName} is overstocked (${item.currentStock} available, maximum: ${item.maximumStock})`,
        });
      }

      // Check for expiry warning (if item is perishable and expires within 30 days)
      if (item.isPerishable && item.expiryDate) {
        const thirtyDaysFromNow = new Date();
        thirtyDaysFromNow.setDate(thirtyDaysFromNow.getDate() + 30);

        if (item.expiryDate <= thirtyDaysFromNow) {
          alerts.push({
            inventoryItemId,
            alertType: StockAlertType.EXPIRY_WARNING,
            threshold: 30,
            currentStock: item.currentStock,
            message: `${item.itemName} expires on ${item.expiryDate.toDateString()}`,
          });
        }
      }

      // Create alerts
      for (const alertData of alerts) {
        // Check if similar alert already exists
        const existingAlert = await this.prisma.stockAlert.findFirst({
          where: {
            inventoryItemId,
            alertType: alertData.alertType,
            status: 'ACTIVE',
          },
        });

        if (!existingAlert) {
          const alert = await this.prisma.stockAlert.create({
            data: alertData,
          });

          // Send notifications
          await this.notifyStockAlert(alert, item);

          // Publish event
          await this.rabbitMQService.publish({
            type: 'stock_alert_created',
            data: { alert, item },
          });
        }
      }
    } catch (error) {
      this.logger.error(`Failed to check stock alerts: ${error.message}`, error.stack);
    }
  }

  // Private helper methods
  private async updateAverageCost(inventoryItemId: string): Promise<void> {
    try {
      // Calculate weighted average cost based on purchase transactions
      const purchaseTransactions = await this.prisma.inventoryTransaction.findMany({
        where: {
          inventoryItemId,
          transactionType: InventoryTransactionType.PURCHASE,
          unitCost: { not: null },
        },
        orderBy: { transactionDate: 'desc' },
        take: 10, // Consider last 10 purchases for average
      });

      if (purchaseTransactions.length === 0) return;

      let totalCost = this.decimalService.create('0');
      let totalQuantity = 0;

      purchaseTransactions.forEach(transaction => {
        const cost = this.decimalService.create(transaction.unitCost || '0')
          .mul(transaction.quantity);
        totalCost = totalCost.add(cost);
        totalQuantity += transaction.quantity;
      });

      const averageCost = totalQuantity > 0 
        ? totalCost.div(totalQuantity).toString()
        : '0.00';

      await this.prisma.inventoryItem.update({
        where: { id: inventoryItemId },
        data: { averageCost },
      });
    } catch (error) {
      this.logger.error(`Failed to update average cost: ${error.message}`, error.stack);
    }
  }

  private async notifyStockAlert(alert: any, item: any): Promise<void> {
    // Notify inventory managers and admins
    await this.websocketService.sendToRole('admin', 'stock_alert', {
      message: alert.message,
      alert: {
        id: alert.id,
        type: alert.alertType,
        item: {
          id: item.id,
          name: item.itemName,
          code: item.itemCode,
          currentStock: item.currentStock,
        },
      },
    });

    // Also notify users with inventory management role
    await this.websocketService.sendToRole('inventory_manager', 'stock_alert', {
      message: alert.message,
      alert: {
        id: alert.id,
        type: alert.alertType,
        item: {
          id: item.id,
          name: item.itemName,
          code: item.itemCode,
          currentStock: item.currentStock,
        },
      },
    });
  }
}
