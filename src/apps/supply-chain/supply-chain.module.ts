import { Module } from '@nestjs/common';
import { PrismaModule } from '@core/prisma/prisma.module';
import { DecimalModule } from '@shared/decimal/decimal.module';
import { WebsocketModule } from '@infra/websocket/websocket.module';
import { RabbitMQModule } from '@infra/rabbitmq/rabbitmq.module';
import { MongoDbModule } from '@infra/mongodb/mongodb.module';

// Services
import { SupplierService } from './services/supplier.service';
import { InventoryService } from './services/inventory.service';
import { PurchaseOrderService } from './services/purchase-order.service';
import { SupplyChainAnalyticsService } from './services/analytics.service';

// Controllers
import { SupplierController } from './controllers/supplier.controller';
import { InventoryController } from './controllers/inventory.controller';
import { PurchaseOrderController } from './controllers/purchase-order.controller';
import { SupplyChainAnalyticsController } from './controllers/analytics.controller';

@Module({
  imports: [
    PrismaModule,
    DecimalModule,
    WebsocketModule,
    RabbitMQModule,
    MongoDbModule,
  ],
  controllers: [
    SupplierController,
    InventoryController,
    PurchaseOrderController,
    SupplyChainAnalyticsController,
  ],
  providers: [
    SupplierService,
    InventoryService,
    PurchaseOrderService,
    SupplyChainAnalyticsService,
  ],
  exports: [
    SupplierService,
    InventoryService,
    PurchaseOrderService,
    SupplyChainAnalyticsService,
  ],
})
export class SupplyChainModule {}
