import { 
  <PERSON>, 
  Get, 
  Query, 
  UseGuards, 
  Param,
  UseInterceptors,
  ParseIntPipe,
  DefaultValuePipe,
} from '@nestjs/common';
import { CacheInterceptor } from '@nestjs/cache-manager';
import { 
  ApiTags, 
  ApiOperation, 
  ApiResponse, 
  ApiQuery, 
  ApiParam,
  ApiBearerAuth,
} from '@nestjs/swagger';
import { JwtAuthGuard } from '@core/auth/guards/jwt-auth.guard';
import { RolesGuard } from '@core/auth/guards/roles.guard';
import { Roles } from '@core/auth/decorators/roles.decorator';
import { DashboardService, DashboardSummary, SystemStatus } from './dashboard.service';
import { DashboardStatsService } from './dashboard-stats.service';
import { I18nService } from '@/i18n/i18n.service';

@ApiTags('admin-dashboard')
@ApiBearerAuth()
@Controller('admin/dashboard')
@UseGuards(JwtAuthGuard, RolesGuard)
@Roles('admin')
export class DashboardController {
  constructor(
    private readonly dashboardService: DashboardService,
    private readonly statsService: DashboardStatsService,
    private readonly i18nService: I18nService,
  ) {}

  @Get()
  @ApiOperation({ 
    summary: 'Get dashboard summary',
    description: 'Retrieve a complete summary of system statistics and recent activity for the admin dashboard',
  })
  @ApiQuery({ 
    name: 'cache', 
    required: false, 
    type: Number,
    description: 'Cache duration in minutes (0 to skip caching)', 
  })
  @ApiResponse({ 
    status: 200, 
    description: 'Dashboard summary data',
    schema: {
      type: 'object',
      properties: {
        users: {
          type: 'object',
          properties: {
            total: { type: 'number' },
            active: { type: 'number' },
            new: { type: 'number' },
          },
        },
        products: {
          type: 'object',
          properties: {
            total: { type: 'number' },
            lowStock: { type: 'number' },
            outOfStock: { type: 'number' },
          },
        },
        orders: {
          type: 'object',
          properties: {
            total: { type: 'number' },
            pending: { type: 'number' },
            processing: { type: 'number' },
            shipped: { type: 'number' },
            delivered: { type: 'number' },
            cancelled: { type: 'number' },
            revenue: {
              type: 'object',
              properties: {
                daily: { type: 'number' },
                weekly: { type: 'number' },
                monthly: { type: 'number' },
                annual: { type: 'number' },
              },
            },
          },
        },
        activity: {
          type: 'object',
          properties: {
            recentOrders: { type: 'array', items: { type: 'object' } },
            recentUsers: { type: 'array', items: { type: 'object' } },
            recentProducts: { type: 'array', items: { type: 'object' } },
          },
        },
        system: {
          type: 'object',
          properties: {
            status: { type: 'string' },
            version: { type: 'string' },
            lastUpdated: { type: 'string', format: 'date-time' },
            activeJobs: { type: 'number' },
          },
        },
      },
    },
  })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  @ApiResponse({ status: 403, description: 'Forbidden - requires admin role' })
  async getDashboard(
    @Query('cache', new DefaultValuePipe(5), ParseIntPipe) cacheMinutes: number
  ): Promise<DashboardSummary> {
    return this.dashboardService.getDashboardSummary(cacheMinutes);
  }

  @Get('system-status')
  @ApiOperation({ 
    summary: 'Get system status',
    description: 'Check the status of all system components including databases and services',
  })
  @ApiResponse({ 
    status: 200, 
    description: 'System status information',
    schema: {
      type: 'object',
      properties: {
        database: {
          type: 'object',
          properties: {
            postgres: { type: 'boolean' },
            mongodb: { type: 'boolean' },
          },
        },
        services: {
          type: 'object',
          properties: {
            redis: { type: 'boolean' },
            rabbitmq: { type: 'boolean' },
            storage: { type: 'boolean' },
          },
        },
        memory: {
          type: 'object',
          properties: {
            used: { type: 'number' },
            total: { type: 'number' },
            percentage: { type: 'number' },
          },
        },
        cpu: {
          type: 'object',
          properties: {
            usage: { type: 'number' },
            cores: { type: 'number' },
          },
        },
        uptime: { type: 'number' },
      },
    },
  })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  @ApiResponse({ status: 403, description: 'Forbidden - requires admin role' })
  async getSystemStatus(): Promise<SystemStatus> {
    return this.dashboardService.getSystemStatus();
  }

  @Get('sales-trends/:period')
  @UseInterceptors(CacheInterceptor)
  @ApiOperation({ 
    summary: 'Get sales trends',
    description: 'Retrieve sales trend data for different time periods',
  })
  @ApiParam({ 
    name: 'period', 
    description: 'Time period for trends (daily, weekly, monthly, yearly)',
    enum: ['daily', 'weekly', 'monthly', 'yearly'],
    required: true,
  })
  @ApiQuery({ 
    name: 'limit', 
    required: false, 
    type: Number,
    description: 'Number of data points to return',
  })
  @ApiResponse({ 
    status: 200, 
    description: 'Sales trend data for the specified period',
    schema: {
      type: 'array',
      items: {
        type: 'object',
        properties: {
          period: { type: 'string' },
          orderCount: { type: 'number' },
          revenue: { type: 'number' },
          avgOrderValue: { type: 'number' },
        },
      },
    },
  })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  @ApiResponse({ status: 403, description: 'Forbidden - requires admin role' })
  async getSalesTrends(
    @Param('period') period: 'daily' | 'weekly' | 'monthly' | 'yearly',
    @Query('limit', new DefaultValuePipe(12), ParseIntPipe) limit: number
  ): Promise<any[]> {
    return this.statsService.getSalesTrends(period, limit);
  }

  @Get('chart-data/:chartType')
  @UseInterceptors(CacheInterceptor)
  @ApiOperation({ 
    summary: 'Get chart data',
    description: 'Retrieve data for various dashboard charts and graphs',
  })
  @ApiParam({ 
    name: 'chartType', 
    description: 'Type of chart data to retrieve',
    enum: ['user-growth', 'revenue-distribution', 'order-status', 'product-categories', 'top-products', 'stock-levels'],
    required: true,
  })
  @ApiResponse({ 
    status: 200, 
    description: 'Chart data for the specified chart type',
    schema: {
      type: 'object',
      properties: {
        labels: {
          type: 'array',
          items: { type: 'string' },
        },
        datasets: {
          type: 'array',
          items: {
            type: 'object',
            properties: {
              label: { type: 'string' },
              data: {
                type: 'array',
                items: { type: 'number' },
              },
            },
          },
        },
      },
    },
  })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  @ApiResponse({ status: 403, description: 'Forbidden - requires admin role' })
  async getChartData(@Param('chartType') chartType: string): Promise<any> {
    // For demonstration, return mock chart data based on chart type
    switch (chartType) {
      case 'user-growth':
        return {
          labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun'],
          datasets: [{
            label: 'User Growth',
            data: [65, 78, 90, 105, 112, 120],
          }],
        };
        
      case 'revenue-distribution':
        return {
          labels: ['Products', 'Services', 'Subscriptions'],
          datasets: [{
            label: 'Revenue Distribution',
            data: [40, 35, 25],
          }],
        };
        
      case 'order-status':
        return {
          labels: ['Pending', 'Processing', 'Shipped', 'Delivered', 'Cancelled'],
          datasets: [{
            label: 'Order Status',
            data: [15, 10, 25, 45, 5],
          }],
        };
        
      case 'product-categories':
        return {
          labels: ['Electronics', 'Clothing', 'Food', 'Books', 'Other'],
          datasets: [{
            label: 'Product Categories',
            data: [30, 25, 15, 20, 10],
          }],
        };
        
      case 'top-products':
        return {
          labels: ['Product A', 'Product B', 'Product C', 'Product D', 'Product E'],
          datasets: [{
            label: 'Sales',
            data: [120, 98, 85, 72, 65],
          }],
        };
        
      case 'stock-levels':
        return {
          labels: ['In Stock', 'Low Stock', 'Out of Stock'],
          datasets: [{
            label: 'Stock Levels',
            data: [70, 20, 10],
          }],
        };
        
      default:
        return {
          labels: [],
          datasets: [],
        };
    }
  }

  @Get('localized-stats')
  @ApiOperation({ 
    summary: 'Get localized dashboard statistics',
    description: 'Retrieve dashboard statistics with localized labels',
  })
  @ApiQuery({ 
    name: 'lang', 
    required: false, 
    type: String,
    description: 'Language code (e.g., en, es, ru)',
  })
  @ApiResponse({ 
    status: 200, 
    description: 'Localized dashboard statistics',
  })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  @ApiResponse({ status: 403, description: 'Forbidden - requires admin role' })
  async getLocalizedStats(@Query('lang') lang?: string): Promise<any> {
    // Get regular dashboard data
    const dashboardData = await this.dashboardService.getDashboardSummary();
    
    // Translate labels based on the requested language
    const orderStatusLabels = {
      pending: this.i18nService.translate('order.status.pending', {}, lang),
      processing: this.i18nService.translate('order.status.processing', {}, lang),
      shipped: this.i18nService.translate('order.status.shipped', {}, lang),
      delivered: this.i18nService.translate('order.status.delivered', {}, lang),
      cancelled: this.i18nService.translate('order.status.cancelled', {}, lang),
    };
    
    // Add localized data to the response
    return {
      data: dashboardData,
      localized: {
        orderStatusLabels,
        userTitle: this.i18nService.translate('user.profile', {}, lang),
        productTitle: this.i18nService.translate('product.name', {}, lang),
        orderTitle: this.i18nService.translate('order.number', {}, lang),
        // Add other translated labels as needed
      },
    };
  }
}
