import { Injectable, Logger } from '@nestjs/common';
import { PrismaService } from '@core/prisma/prisma.service';
import { MongoDbService } from '@infra/mongodb/mongodb.service';
import { RedisService } from '@infra/redis/redis.service';
import { DashboardStatsService } from './dashboard-stats.service';

export interface DashboardSummary {
  users: {
    total: number;
    active: number;
    new: number;
  };
  products: {
    total: number;
    lowStock: number;
    outOfStock: number;
  };
  orders: {
    total: number;
    pending: number;
    processing: number;
    shipped: number;
    delivered: number;
    cancelled: number;
    revenue: {
      daily: number;
      weekly: number;
      monthly: number;
      annual: number;
    };
  };
  activity: {
    recentOrders: any[];
    recentUsers: any[];
    recentProducts: any[];
  };
  system: {
    status: string;
    version: string;
    lastUpdated: Date;
    activeJobs: number;
  };
}

export interface SystemStatus {
  database: {
    postgres: boolean;
    mongodb: boolean;
  };
  services: {
    redis: boolean;
    rabbitmq: boolean;
    storage: boolean;
  };
  memory: {
    used: number;
    total: number;
    percentage: number;
  };
  cpu: {
    usage: number;
    cores: number;
  };
  uptime: number;
}

@Injectable()
export class DashboardService {
  private readonly logger = new Logger(DashboardService.name);
  
  constructor(
    private readonly prismaService: PrismaService,
    private readonly mongoDbService: MongoDbService,
    private readonly redisService: RedisService,
    private readonly statsService: DashboardStatsService,
  ) {}

  /**
   * Get dashboard summary with key metrics and recent activity
   * 
   * @param cacheMinutes Minutes to cache results (0 to skip caching)
   * @returns Dashboard summary data
   */
  async getDashboardSummary(cacheMinutes = 5): Promise<DashboardSummary> {
    try {
      // Try to get from cache first
      if (cacheMinutes > 0) {
        const cached = await this.redisService.get('dashboard:summary');
        if (cached) {
          return JSON.parse(cached);
        }
      }

      // Get data from MongoDB for faster reads
      const [
        userStats,
        productStats,
        orderStats,
        recentActivity,
      ] = await Promise.all([
        this.statsService.getUserStats(),
        this.statsService.getProductStats(),
        this.statsService.getOrderStats(),
        this.getRecentActivity(),
      ]);

      // Get system info
      const systemInfo = await this.getSystemInfo();

      // Construct the dashboard summary
      const summary: DashboardSummary = {
        users: userStats,
        products: productStats,
        orders: orderStats,
        activity: recentActivity,
        system: systemInfo,
      };

      // Cache the result if caching is enabled
      if (cacheMinutes > 0) {
        await this.redisService.set(
          'dashboard:summary', 
          JSON.stringify(summary),
          cacheMinutes * 60
        );
      }

      return summary;
    } catch (error) {
      this.logger.error(`Failed to get dashboard summary: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Get system status including database and service connectivity
   * 
   * @returns System status information
   */
  async getSystemStatus(): Promise<SystemStatus> {
    try {
      // Check PostgreSQL connectivity
      const postgresConnected = await this.checkPostgresConnection();
      
      // Check MongoDB connectivity
      const mongoConnected = await this.checkMongoConnection();
      
      // Check Redis connectivity
      const redisConnected = await this.checkRedisConnection();
      
      // Check RabbitMQ connectivity (mock for now)
      const rabbitmqConnected = true;
      
      // Check S3 Storage connectivity (mock for now)
      const storageConnected = true;
      
      // Get system resources info
      const memory = this.getMemoryUsage();
      const cpu = this.getCpuUsage();
      const uptime = process.uptime();

      return {
        database: {
          postgres: postgresConnected,
          mongodb: mongoConnected,
        },
        services: {
          redis: redisConnected,
          rabbitmq: rabbitmqConnected,
          storage: storageConnected,
        },
        memory,
        cpu,
        uptime,
      };
    } catch (error) {
      this.logger.error(`Failed to get system status: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Get recent activity across the system
   * 
   * @param limit Number of recent items to fetch
   * @returns Recent activity data
   */
  private async getRecentActivity(limit: number = 5): Promise<{ 
    recentOrders: any[];
    recentUsers: any[];
    recentProducts: any[];
  }> {
    try {
      // Get data from MongoDB for faster reads
      const [recentOrders, recentUsers, recentProducts] = await Promise.all([
        this.mongoDbService.find('orders', {}, { 
          sort: { createdAt: -1 }, 
          limit,
          projection: { 
            id: 1, 
            orderNumber: 1, 
            customerName: 1, 
            total: 1, 
            status: 1, 
            createdAt: 1 
          } 
        }),
        this.mongoDbService.find('users', {}, { 
          sort: { createdAt: -1 }, 
          limit,
          projection: { 
            id: 1, 
            email: 1, 
            firstName: 1, 
            lastName: 1, 
            role: 1, 
            createdAt: 1 
          } 
        }),
        this.mongoDbService.find('products', {}, { 
          sort: { createdAt: -1 }, 
          limit,
          projection: { 
            id: 1, 
            name: 1, 
            price: 1, 
            stockQuantity: 1, 
            createdAt: 1 
          } 
        }),
      ]);

      return {
        recentOrders,
        recentUsers,
        recentProducts,
      };
    } catch (error) {
      this.logger.error(`Failed to get recent activity: ${error.message}`, error.stack);
      return {
        recentOrders: [],
        recentUsers: [],
        recentProducts: [],
      };
    }
  }

  /**
   * Get system information
   * 
   * @returns System information
   */
  private async getSystemInfo(): Promise<any> {
    return {
      status: 'operational',
      version: '1.0.0',
      lastUpdated: new Date(),
      activeJobs: await this.getActiveJobsCount(),
    };
  }

  /**
   * Get count of active background jobs
   * 
   * @returns Count of active jobs
   */
  private async getActiveJobsCount(): Promise<number> {
    try {
      const activeJobs = await this.redisService.get('system:active-jobs');
      return activeJobs ? parseInt(activeJobs, 10) : 0;
    } catch (error) {
      return 0;
    }
  }

  /**
   * Check PostgreSQL connection
   * 
   * @returns Connection status
   */
  private async checkPostgresConnection(): Promise<boolean> {
    try {
      await this.prismaService.$queryRaw`SELECT 1`;
      return true;
    } catch (error) {
      this.logger.error(`PostgreSQL connection check failed: ${error.message}`);
      return false;
    }
  }

  /**
   * Check MongoDB connection
   * 
   * @returns Connection status
   */
  private async checkMongoConnection(): Promise<boolean> {
    try {
      await this.mongoDbService.findOne('system', { key: 'heartbeat' });
      return true;
    } catch (error) {
      this.logger.error(`MongoDB connection check failed: ${error.message}`);
      return false;
    }
  }

  /**
   * Check Redis connection
   * 
   * @returns Connection status
   */
  private async checkRedisConnection(): Promise<boolean> {
    try {
      await this.redisService.set('system:ping', 'pong', 5);
      const result = await this.redisService.get('system:ping');
      return result === 'pong';
    } catch (error) {
      this.logger.error(`Redis connection check failed: ${error.message}`);
      return false;
    }
  }

  /**
   * Get memory usage statistics
   * 
   * @returns Memory usage data
   */
  private getMemoryUsage(): { used: number; total: number; percentage: number } {
    const memUsage = process.memoryUsage();
    const used = Math.round(memUsage.heapUsed / 1024 / 1024);
    const total = Math.round(memUsage.heapTotal / 1024 / 1024);
    const percentage = Math.round((used / total) * 100);
    
    return { used, total, percentage };
  }

  /**
   * Get CPU usage statistics
   * 
   * @returns CPU usage data
   */
  private getCpuUsage(): { usage: number; cores: number } {
    // This is a simplified mock implementation
    const cores = require('os').cpus().length;
    const usage = Math.round(Math.random() * 30); // Mock value between 0-30%
    
    return { usage, cores };
  }
}
