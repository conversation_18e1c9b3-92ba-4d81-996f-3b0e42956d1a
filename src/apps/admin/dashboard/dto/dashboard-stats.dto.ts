import { ApiProperty } from '@nestjs/swagger';
import { <PERSON><PERSON><PERSON>y, IsN<PERSON>ber, IsObject, IsString } from 'class-validator';
import Decimal from 'decimal.js';

class RevenueStatsDto {
  @ApiProperty({
    description: 'Total revenue for the period',
    example: '15998.75',
    type: String,
  })
  total: Decimal;

  @ApiProperty({
    description: 'Revenue for the current month',
    example: '4500.25',
    type: String,
  })
  currentMonth: Decimal;

  @ApiProperty({
    description: 'Revenue for the previous month',
    example: '3800.50',
    type: String,
  })
  previousMonth: Decimal;

  @ApiProperty({
    description: 'Percentage change from previous month',
    example: 18.42,
  })
  @IsNumber()
  percentageChange: number;
}

class OrderStatsDto {
  @ApiProperty({
    description: 'Total number of orders',
    example: 250,
  })
  @IsNumber()
  total: number;

  @ApiProperty({
    description: 'Number of orders for the current month',
    example: 45,
  })
  @IsNumber()
  currentMonth: number;

  @ApiProperty({
    description: 'Number of orders for the previous month',
    example: 38,
  })
  @IsNumber()
  previousMonth: number;

  @ApiProperty({
    description: 'Percentage change from previous month',
    example: 18.42,
  })
  @IsNumber()
  percentageChange: number;

  @ApiProperty({
    description: 'Orders by status',
    example: {
      PENDING: 10,
      PROCESSING: 15,
      SHIPPED: 8,
      DELIVERED: 12,
      CANCELLED: 0,
    },
  })
  @IsObject()
  byStatus: Record<string, number>;
}

class UserStatsDto {
  @ApiProperty({
    description: 'Total number of users',
    example: 500,
  })
  @IsNumber()
  total: number;

  @ApiProperty({
    description: 'Number of new users for the current month',
    example: 25,
  })
  @IsNumber()
  newCurrentMonth: number;

  @ApiProperty({
    description: 'Number of new users for the previous month',
    example: 20,
  })
  @IsNumber()
  newPreviousMonth: number;

  @ApiProperty({
    description: 'Percentage change from previous month',
    example: 25.0,
  })
  @IsNumber()
  percentageChange: number;

  @ApiProperty({
    description: 'Active users in the last 30 days',
    example: 350,
  })
  @IsNumber()
  activeUsers: number;
}

class ProductStatsDto {
  @ApiProperty({
    description: 'Total number of products',
    example: 120,
  })
  @IsNumber()
  total: number;

  @ApiProperty({
    description: 'Number of products with low stock',
    example: 8,
  })
  @IsNumber()
  lowStock: number;

  @ApiProperty({
    description: 'Number of products out of stock',
    example: 3,
  })
  @IsNumber()
  outOfStock: number;

  @ApiProperty({
    description: 'Top selling products',
    example: [
      { id: '123e4567-e89b-12d3-a456-426614174000', name: 'Premium Cotton Shirt', sales: 45 },
      { id: '123e4567-e89b-12d3-a456-426614174001', name: 'Designer Jeans', sales: 38 },
      { id: '123e4567-e89b-12d3-a456-426614174002', name: 'Casual Sneakers', sales: 30 },
    ],
  })
  @IsArray()
  topSelling: Array<{ id: string; name: string; sales: number }>;
}

export class DashboardStatsDto {
  @ApiProperty({
    description: 'Revenue statistics',
    type: RevenueStatsDto,
  })
  @IsObject()
  revenue: RevenueStatsDto;

  @ApiProperty({
    description: 'Order statistics',
    type: OrderStatsDto,
  })
  @IsObject()
  orders: OrderStatsDto;

  @ApiProperty({
    description: 'User statistics',
    type: UserStatsDto,
  })
  @IsObject()
  users: UserStatsDto;

  @ApiProperty({
    description: 'Product statistics',
    type: ProductStatsDto,
  })
  @IsObject()
  products: ProductStatsDto;

  @ApiProperty({
    description: 'Time period for the dashboard stats',
    example: 'Last 30 days',
  })
  @IsString()
  period: string;
}
