import { Modu<PERSON> } from '@nestjs/common';
import { DashboardController } from './dashboard.controller';
import { DashboardService } from './dashboard.service';
import { DashboardStatsService } from './dashboard-stats.service';
import { CoreModule } from '@core/core.module';
import { InfraModule } from '@infra/infra.module';
import { SharedModule } from '@shared/shared.module';
import { PrismaService } from '@core/prisma/prisma.service';
import { MongoDbService } from '@infra/mongodb/mongodb.service';
import { RedisService } from '@infra/redis/redis.service';

@Module({
  imports: [
    CoreModule,
    InfraModule,
    SharedModule,
  ],
  controllers: [
    DashboardController,
  ],
  providers: [
    DashboardService,
    DashboardStatsService,
    PrismaService,
    MongoDbService,
    RedisService,
  ],
  exports: [
    DashboardService,
    DashboardStatsService,
  ],
})
export class DashboardModule {}
