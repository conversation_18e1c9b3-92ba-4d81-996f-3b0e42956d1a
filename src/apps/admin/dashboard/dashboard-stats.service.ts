import { Injectable, Logger } from '@nestjs/common';
import { PrismaService } from '@core/prisma/prisma.service';
import { MongoDbService } from '@infra/mongodb/mongodb.service';
import { RedisService } from '@infra/redis/redis.service';
import { OrderStatus } from '@prisma/client';
import * as dayjs from 'dayjs';
import * as weekOfYear from 'dayjs/plugin/weekOfYear';

// Extend dayjs with the weekOfYear plugin
dayjs.extend(weekOfYear);

@Injectable()
export class DashboardStatsService {
  private readonly logger = new Logger(DashboardStatsService.name);
  
  constructor(
    private readonly prismaService: PrismaService,
    private readonly mongoDbService: MongoDbService,
    private readonly redisService: RedisService,
  ) {}

  /**
   * Get user statistics
   * 
   * @returns User statistics
   */
  async getUserStats(): Promise<{ total: number; active: number; new: number }> {
    try {
      // Check if cached
      const cacheKey = 'stats:users';
      const cached = await this.redisService.get(cacheKey);
      if (cached) {
        return JSON.parse(cached);
      }

      // Prefer MongoDB for faster reads
      let total = 0;
      let active = 0;
      let newUsers = 0;

      try {
        // Get total users
        total = await this.mongoDbService.count('users', {});
        
        // Get active users (logged in within last 7 days)
        const sevenDaysAgo = new Date();
        sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 7);
        active = await this.mongoDbService.count('users', {
          lastLoginAt: { $gte: sevenDaysAgo }
        });
        
        // Get new users (registered within last 30 days)
        const thirtyDaysAgo = new Date();
        thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
        newUsers = await this.mongoDbService.count('users', {
          createdAt: { $gte: thirtyDaysAgo }
        });
      } catch (error) {
        // Fallback to PostgreSQL
        this.logger.warn('Falling back to PostgreSQL for user stats');
        
        // Get total users
        total = await this.prismaService.user.count();
        
        // Get active users (logged in within last 7 days)
        const sevenDaysAgo = new Date();
        sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 7);
        active = await this.prismaService.user.count({
          where: {
            lastLoginAt: {
              gte: sevenDaysAgo
            }
          }
        });
        
        // Get new users (registered within last 30 days)
        const thirtyDaysAgo = new Date();
        thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
        newUsers = await this.prismaService.user.count({
          where: {
            createdAt: {
              gte: thirtyDaysAgo
            }
          }
        });
      }

      const result = { total, active, new: newUsers };

      // Cache the result for 5 minutes
      await this.redisService.set(cacheKey, JSON.stringify(result), 5 * 60);

      return result;
    } catch (error) {
      this.logger.error(`Failed to get user stats: ${error.message}`, error.stack);
      return { total: 0, active: 0, new: 0 };
    }
  }

  /**
   * Get product statistics
   * 
   * @returns Product statistics
   */
  async getProductStats(): Promise<{ total: number; lowStock: number; outOfStock: number }> {
    try {
      // Check if cached
      const cacheKey = 'stats:products';
      const cached = await this.redisService.get(cacheKey);
      if (cached) {
        return JSON.parse(cached);
      }

      // Prefer MongoDB for faster reads
      let total = 0;
      let lowStock = 0;
      let outOfStock = 0;

      try {
        // Get total products
        total = await this.mongoDbService.count('products', {});

        // Get low stock products (stock <= 10)
        lowStock = await this.mongoDbService.count('products', {
          stockQuantity: { $gt: 0, $lte: 10 }
        });

        // Get out of stock products
        outOfStock = await this.mongoDbService.count('products', {
          stockQuantity: { $lte: 0 }
        });

        if (total < 0 || lowStock < 0 || outOfStock < 0) {
          throw new Error('Invalid count values received from MongoDB');
        }
      } catch (error) {
        // Fallback to PostgreSQL
        this.logger.warn('Falling back to PostgreSQL for product stats');

        // Get total products
        total = await this.prismaService.product.count();

        // Get low stock products (stock <= 10)
        lowStock = await this.prismaService.product.count({
          where: {
            quantity: {
              gt: 0,
              lte: 10
            }
          }
        });

        // Get out of stock products
        outOfStock = await this.prismaService.product.count({
          where: {
            quantity: {
              lte: 0
            }
          }
        });
      }

      const result = { total, lowStock, outOfStock };

      // Cache the result for 5 minutes
      await this.redisService.set(cacheKey, JSON.stringify(result), 5 * 60);

      return result;
    } catch (error) {
      this.logger.error(`Failed to get product stats: ${error.message}`, error.stack);
      return { total: 0, lowStock: 0, outOfStock: 0 };
    }
  }

  /**
   * Get order statistics and revenue data
   * 
   * @returns Order statistics
   */
  async getOrderStats(): Promise<{
    total: number;
    pending: number;
    processing: number;
    shipped: number;
    delivered: number;
    cancelled: number;
    revenue: {
      daily: number;
      weekly: number;
      monthly: number;
      annual: number;
    };
  }> {
    try {
      // Check if cached
      const cacheKey = 'stats:orders';
      const cached = await this.redisService.get(cacheKey);
      if (cached) {
        return JSON.parse(cached);
      }

      // Prepare date ranges for different time periods

      // Get current date for calculations
      const now = new Date();
      const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
      const oneWeekAgo = new Date(today);
      oneWeekAgo.setDate(today.getDate() - 7);
      const oneMonthAgo = new Date(today);
      oneMonthAgo.setMonth(today.getMonth() - 1);
      const oneYearAgo = new Date(today);
      oneYearAgo.setFullYear(today.getFullYear() - 1);

      // Prefer MongoDB for faster reads
      let total = 0;
      let pending = 0;
      let processing = 0;
      let shipped = 0;
      let delivered = 0;
      let cancelled = 0;
      let dailyRevenue = 0;
      let weeklyRevenue = 0;
      let monthlyRevenue = 0;
      let annualRevenue = 0;

      try {
        // Get order counts by status
        total = await this.mongoDbService.count('orders', {});
        pending = await this.mongoDbService.count('orders', { status: 'pending' });
        processing = await this.mongoDbService.count('orders', { status: 'processing' });
        shipped = await this.mongoDbService.count('orders', { status: 'shipped' });
        delivered = await this.mongoDbService.count('orders', { status: 'delivered' });
        cancelled = await this.mongoDbService.count('orders', { status: 'cancelled' });

        // Calculate revenue from completed orders
        const dailyOrders = await this.mongoDbService.find('orders', {
          status: 'delivered',
          createdAt: { $gte: today }
        });

        const weeklyOrders = await this.mongoDbService.find('orders', {
          status: 'delivered',
          createdAt: { $gte: oneWeekAgo }
        });

        const monthlyOrders = await this.mongoDbService.find('orders', {
          status: 'delivered',
          createdAt: { $gte: oneMonthAgo }
        });

        const annualOrders = await this.mongoDbService.find('orders', {
          status: 'delivered',
          createdAt: { $gte: oneYearAgo }
        });

        // Sum up revenue
        dailyRevenue = dailyOrders.reduce((sum, order) => sum + (order.total || 0), 0);
        weeklyRevenue = weeklyOrders.reduce((sum, order) => sum + (order.total || 0), 0);
        monthlyRevenue = monthlyOrders.reduce((sum, order) => sum + (order.total || 0), 0);
        annualRevenue = annualOrders.reduce((sum, order) => sum + (order.total || 0), 0);
      } catch (error) {
        // Fallback to PostgreSQL
        this.logger.warn('Falling back to PostgreSQL for order stats');

        // Get order counts by status
        pending = await this.prismaService.order.count({ where: { status: OrderStatus.NEW } });
        processing = await this.prismaService.order.count({ where: { status: OrderStatus.IN_PROGRESS } });
        shipped = await this.prismaService.order.count({ where: { status: OrderStatus.COMPLETE } });
        delivered = await this.prismaService.order.count({ where: { status: OrderStatus.DELIVERED } });
        cancelled = await this.prismaService.order.count({ where: { status: OrderStatus.CANCELLED } });

        // Calculate revenue
        try {
          // Define type for SQL query results
          type SumResult = {
            sum_total: string | null;
          };

          // Get daily revenue using raw SQL query
          const dailyResult = await this.prismaService.$queryRaw<SumResult[]>`
            SELECT SUM(CAST(total AS DECIMAL)) as sum_total 
            FROM "Order" 
            WHERE status = 'DELIVERED' AND "createdAt" >= ${today}
          `;

          dailyRevenue = dailyResult[0]?.sum_total ? parseFloat(dailyResult[0].sum_total) : 0;

          // Get weekly revenue
          const weeklyResult = await this.prismaService.$queryRaw<SumResult[]>`
            SELECT SUM(CAST(total AS DECIMAL)) as sum_total 
            FROM "Order" 
            WHERE status = 'DELIVERED' AND "createdAt" >= ${oneWeekAgo}
          `;

          weeklyRevenue = weeklyResult[0]?.sum_total ? parseFloat(weeklyResult[0].sum_total) : 0;

          // Get monthly revenue
          const monthlyResult = await this.prismaService.$queryRaw<SumResult[]>`
            SELECT SUM(CAST(total AS DECIMAL)) as sum_total 
            FROM "Order" 
            WHERE status = 'DELIVERED' AND "createdAt" >= ${oneMonthAgo}
          `;

          monthlyRevenue = monthlyResult[0]?.sum_total ? parseFloat(monthlyResult[0].sum_total) : 0;

          // Get annual revenue
          const annualResult = await this.prismaService.$queryRaw<SumResult[]>`
            SELECT SUM(CAST(total AS DECIMAL)) as sum_total 
            FROM "Order" 
            WHERE status = 'DELIVERED' AND "createdAt" >= ${oneYearAgo}
          `;

          annualRevenue = annualResult[0]?.sum_total ? parseFloat(annualResult[0].sum_total) : 0;
        } catch (error) {
          this.logger.error(`Failed to calculate revenue: ${error.message}`, error.stack);
        }
      }

      const result = {
        total,
        pending,
        processing,
        shipped,
        delivered,
        cancelled,
        revenue: {
          daily: dailyRevenue,
          weekly: weeklyRevenue,
          monthly: monthlyRevenue,
          annual: annualRevenue
        }
      };

      // Cache the result for 15 minutes
      await this.redisService.set(cacheKey, JSON.stringify(result), 15 * 60);

      return result;
    } catch (error) {
      this.logger.error(`Failed to get order stats: ${error.message}`, error.stack);
      return {
        total: 0,
        pending: 0,
        processing: 0,
        shipped: 0,
        delivered: 0,
        cancelled: 0,
        revenue: {
          daily: 0,
          weekly: 0,
          monthly: 0,
          annual: 0
        }
      };
    }
  }

  /**
   * Get sales trends over time periods
   * 
   * @param period Time period ('daily', 'weekly', 'monthly', 'yearly')
   * @param limit Number of data points to return
   * @returns Sales trend data
   */
  async getSalesTrends(period: 'daily' | 'weekly' | 'monthly' | 'yearly', limit: number = 12): Promise<Array<{
    period: string;
    orderCount: number;
    revenue: number;
    avgOrderValue: number;
  }>> {
    try {
      const cacheKey = `stats:sales:${period}:${limit}`;
      const cached = await this.redisService.get(cacheKey);
      if (cached) {
        return JSON.parse(cached);
      }

      // Define time ranges and format patterns based on period
      let groupBy: any;
      let dateFormat: string;
      let startDate: Date;

      const now = new Date();

      switch (period) {
        case 'daily':
          groupBy = { $dateToString: { format: '%Y-%m-%d', date: '$createdAt' } };
          dateFormat = 'YYYY-MM-DD';
          startDate = new Date(now);
          startDate.setDate(now.getDate() - limit);
          break;
        case 'weekly':
          groupBy = {
            $concat: [
              { $toString: { $year: '$createdAt' } },
              '-',
              { $toString: { $week: '$createdAt' } }
            ]
          };
          dateFormat = 'YYYY-[W]WW';
          startDate = new Date(now);
          startDate.setDate(now.getDate() - (limit * 7));
          break;
        case 'monthly':
          groupBy = {
            $concat: [
              { $toString: { $year: '$createdAt' } },
              '-',
              { $toString: { $month: '$createdAt' } }
            ]
          };
          dateFormat = 'YYYY-MM';
          startDate = new Date(now);
          startDate.setMonth(now.getMonth() - limit);
          break;
        case 'yearly':
          groupBy = { $year: '$createdAt' };
          dateFormat = 'YYYY';
          startDate = new Date(now);
          startDate.setFullYear(now.getFullYear() - limit);
          break;
        default:
          throw new Error('Invalid period specified');
      }

      // Try MongoDB first for aggregation (faster for analytics)
      let salesTrends: any[] = [];

      try {
        // Get sales data aggregated by period
        const salesData = await this.mongoDbService.aggregate('orders', [
          {
            $match: {
              status: 'delivered',
              createdAt: { $gte: startDate }
            }
          },
          {
            $group: {
              _id: groupBy,
              count: { $sum: 1 },
              revenue: { $sum: '$total' },
              avgOrderValue: { $avg: '$total' }
            }
          },
          {
            $sort: { _id: 1 }
          }
        ]);

        if (!Array.isArray(salesData)) {
          throw new Error('Invalid response format from MongoDB aggregation');
        }

        salesTrends = salesData.map(item => ({
          period: String(item._id),
          orderCount: Number(item.count) || 0,
          revenue: Number(item.revenue) || 0,
          avgOrderValue: Number(parseFloat((item.avgOrderValue || 0).toFixed(2)))
        }));
      } catch (error) {
        // Fallback to PostgreSQL
        this.logger.warn('Falling back to PostgreSQL for sales trends');

        // This would require implementing the equivalent PostgreSQL aggregation
        // Simplified version for now
        if (period === 'daily') {
          const result = await this.prismaService.$queryRaw`
            SELECT 
              DATE(created_at) as date,
              COUNT(*) as count,
              SUM(total) as revenue,
              AVG(total) as avg_order
            FROM "order" 
            WHERE status = 'DELIVERED' AND "createdAt" >= ${startDate}
            GROUP BY DATE("createdAt")
            ORDER BY date ASC
          `;

          salesTrends = (result as any[]).map(row => ({
            period: row.date,
            orderCount: parseInt(row.count, 10),
            revenue: parseFloat(row.revenue),
            avgOrderValue: parseFloat(parseFloat(row.avg_order).toFixed(2))
          }));
        }
      }

      // Fill in any missing periods with zero values
      const filledTrends = this.fillMissingPeriods(salesTrends, period, startDate, now, limit);

      // Cache the result for 1 hour
      await this.redisService.set(cacheKey, JSON.stringify(filledTrends), 60 * 60);

      return filledTrends;
    } catch (error) {
      this.logger.error(`Failed to get sales trends: ${error.message}`, error.stack);
      return [];
    }
  }

  /**
   * Fill in missing periods in the sales trend data
   * 
   * @param data Existing data
   * @param period Time period
   * @param startDate Start date
   * @param endDate End date
   * @param limit Maximum number of periods
   * @returns Complete data with zero values for missing periods
   */
  private fillMissingPeriods(
    data: Array<{ period: string; orderCount: number; revenue: number; avgOrderValue: number }>,
    period: string,
    startDate: Date,
    endDate: Date,
    limit: number
  ): Array<{ period: string; orderCount: number; revenue: number; avgOrderValue: number }> {
    // Create a map of existing data by period
    const dataMap = new Map<string, { orderCount: number; revenue: number; avgOrderValue: number }>();
    data.forEach(item => {
      dataMap.set(item.period, {
        orderCount: item.orderCount,
        revenue: item.revenue,
        avgOrderValue: item.avgOrderValue
      });
    });

    // Generate all expected periods
    const allPeriods: string[] = [];
    const result: Array<{ period: string; orderCount: number; revenue: number; avgOrderValue: number }> = [];

    switch (period) {
      case 'daily': {
        // Generate all days between start and end date
        let current = dayjs(startDate);
        const end = dayjs(endDate);

        while (current.isBefore(end) || current.isSame(end, 'day')) {
          allPeriods.push(current.format('YYYY-MM-DD'));
          current = current.add(1, 'day');
        }
        break;
      }
      case 'weekly': {
        // Generate all weeks between start and end date
        let current = dayjs(startDate);
        const end = dayjs(endDate);

        while (current.isBefore(end) || current.isSame(end, 'week')) {
          allPeriods.push(`${current.year()}-${current.week()}`);
          current = current.add(1, 'week');
        }
        break;
      }
      case 'monthly': {
        // Generate all months between start and end date
        let current = dayjs(startDate);
        const end = dayjs(endDate);

        while (current.isBefore(end) || current.isSame(end, 'month')) {
          allPeriods.push(current.format('YYYY-MM'));
          current = current.add(1, 'month');
        }
        break;
      }
      case 'yearly': {
        // Generate all years between start and end date
        let current = dayjs(startDate);
        const end = dayjs(endDate);

        while (current.isBefore(end) || current.isSame(end, 'year')) {
          allPeriods.push(current.format('YYYY'));
          current = current.add(1, 'year');
        }
        break;
      }
    }

    // Take only the last 'limit' periods
    const limitedPeriods = allPeriods.slice(-limit);

    // Create the result array with zero values for missing periods
    limitedPeriods.forEach(periodKey => {
      const existingData = dataMap.get(periodKey);
      if (existingData) {
        result.push({
          period: periodKey,
          ...existingData
        });
      } else {
        result.push({
          period: periodKey,
          orderCount: 0,
          revenue: 0,
          avgOrderValue: 0
        });
      }
    });

    return result;
  }
}
