import { Module } from '@nestjs/common';
import { UserService } from './services/user.service';
import { UserController } from './controllers/user.controller';
import { UserResolver } from './resolvers/user.resolver';
import { CoreModule } from '@core/core.module';
import { MongoDbModule } from '@infra/mongodb/mongodb.module';
import { WebsocketModule } from '@infra/websocket/websocket.module';
import { RabbitMQModule } from '@infra/rabbitmq/rabbitmq.module';

@Module({
  imports: [
    CoreModule,
    MongoDbModule,
    WebsocketModule,
    RabbitMQModule,
  ],
  providers: [UserService, UserResolver],
  controllers: [UserController],
  exports: [UserService],
})
export class UserModule {}
