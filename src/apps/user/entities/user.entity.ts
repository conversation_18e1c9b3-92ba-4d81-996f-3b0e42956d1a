import { ObjectType, Field, ID } from '@nestjs/graphql';

@ObjectType()
export class User {
  @Field(() => ID)
  id: string;

  @Field()
  email: string;

  @Field()
  firstName: string;

  @Field()
  lastName: string;

  @Field(() => [String])
  roles: string[];

  @Field({ nullable: true })
  phone?: string;

  @Field(() => Date)
  createdAt: Date;

  @Field(() => Date)
  updatedAt: Date;

  // Password is excluded from GraphQL schema for security
  password?: string;
}

@ObjectType()
export class UserPreference {
  @Field(() => ID)
  id: string;

  @Field(() => ID)
  userId: string;

  @Field(() => User)
  user?: User;

  @Field({ nullable: true })
  theme?: string;

  @Field({ nullable: true })
  language?: string;

  @Field({ nullable: true })
  timezone?: string;

  @Field(() => Date)
  createdAt: Date;

  @Field(() => Date)
  updatedAt: Date;
}
