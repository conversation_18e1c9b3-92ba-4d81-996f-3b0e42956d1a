import { InputType, Field } from '@nestjs/graphql';
import { IsEmail, IsNotEmpty, IsString, Min<PERSON>ength, <PERSON>Optional, IsArray } from 'class-validator';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

@InputType()
export class CreateUserDto {
  @ApiProperty({
    example: '<EMAIL>',
    description: 'User email address',
  })
  @Field()
  @IsEmail({}, { message: 'Please provide a valid email address' })
  @IsNotEmpty({ message: 'Email is required' })
  email: string;

  @ApiProperty({
    example: 'John',
    description: 'User first name',
  })
  @Field()
  @IsString()
  @IsNotEmpty({ message: 'First name is required' })
  firstName: string;

  @ApiProperty({
    example: 'Doe',
    description: 'User last name',
  })
  @Field()
  @IsString()
  @IsNotEmpty({ message: 'Last name is required' })
  lastName: string;

  @ApiProperty({
    example: 'Password123!',
    description: 'User password (min 8 characters)',
    minLength: 8,
  })
  @Field()
  @IsString()
  @MinLength(8, { message: 'Password must be at least 8 characters long' })
  @IsNotEmpty({ message: 'Password is required' })
  password: string;

  @ApiPropertyOptional({
    example: ['user', 'admin'],
    description: 'User roles',
    isArray: true,
  })
  @Field(() => [String], { nullable: true })
  @IsArray()
  @IsOptional()
  roles?: string[];

  @ApiPropertyOptional({
    example: '+1234567890',
    description: 'User phone number',
  })
  @Field({ nullable: true })
  @IsString()
  @IsOptional()
  phone?: string;
}
