import { Injectable, NotFoundException, ConflictException } from '@nestjs/common';
import { PrismaService } from '@core/prisma/prisma.service';
import { CreateUserDto } from '../dto/create-user.dto';
import { UpdateUserDto } from '../dto/update-user.dto';
import * as bcrypt from 'bcrypt';
import { MongoDbService } from '@infra/mongodb/mongodb.service';
import { WebsocketService } from '@infra/websocket/websocket.service';
import { RabbitMQService } from '@infra/rabbitmq/rabbitmq.service';
import { StandardListParams } from '@shared/utils/query-params.util';

@Injectable()
export class UserService {
  constructor(
    private readonly prisma: PrismaService,
    private readonly mongoDbService: MongoDbService,
    private readonly websocketService: WebsocketService,
    private readonly rabbitMQService: RabbitMQService,
  ) {}

  async create(createUserDto: CreateUserDto) {
    // Check if user already exists
    const existingUser = await this.prisma.user.findUnique({
      where: { email: createUserDto.email },
    });

    if (existingUser) {
      throw new ConflictException('User with this email already exists');
    }

    // Hash password
    const hashedPassword = await bcrypt.hash(createUserDto.password, 10);

    // Set default roles if not provided
    const roles = createUserDto.roles || ['user'];

    // Create user in PostgreSQL (source of truth)
    const newUser = await this.prisma.user.create({
      data: {
        email: createUserDto.email,
        firstName: createUserDto.firstName,
        lastName: createUserDto.lastName,
        password: hashedPassword,
        roles,
        phone: createUserDto.phone,
      },
    });

    // Sync to MongoDB for fast reads
    await this.syncUserToMongo(newUser);

    // Notify about new user creation
    this.notifyUserCreation(newUser);

    // Exclude password from returned user
    const { password, ...userWithoutPassword } = newUser;
    return userWithoutPassword;
  }

  async findAll(params?: StandardListParams) {
    // Read from MongoDB for better performance
    const filter = params?.where || {};
    const options: Record<string, any> = {};
    
    // Apply pagination
    if (params?.page && params?.limit) {
      options.skip = (params.page - 1) * params.limit;
      options.limit = params.limit;
    } else if (params?.limit) {
      options.limit = params.limit;
    }
    
    // Apply sorting
    if (params?.order) {
      options.sort = {};
      for (const [field, direction] of Object.entries(params.order)) {
        options.sort[field] = direction === 'asc' ? 1 : -1;
      }
    }

    // Apply search if provided
    if (params?.search) {
      // Text search across multiple fields
      filter.$or = [
        { firstName: { $regex: params.search, $options: 'i' } },
        { lastName: { $regex: params.search, $options: 'i' } },
        { email: { $regex: params.search, $options: 'i' } }
      ];
    }
    
    // Get total count for pagination metadata
    const totalCount = await this.mongoDbService.count('users', filter);
    
    // Get users with filters and options
    const users = await this.mongoDbService.find('users', filter, options);
    
    // Generate pagination metadata
    const page = params?.page || 1;
    const limit = params?.limit || users.length;
    const totalPages = Math.ceil(totalCount / limit);
    
    return {
      data: users,
      pagination: {
        page,
        limit,
        totalPages,
        totalCount,
        hasNextPage: page < totalPages,
        hasPreviousPage: page > 1,
      }
    };
  }

  async findOne(id: string) {
    // Read from MongoDB for better performance
    const user = await this.mongoDbService.findOne('users', { id });
    
    if (!user) {
      throw new NotFoundException(`User with ID ${id} not found`);
    }
    
    return user;
  }

  async findByEmail(email: string) {
    // For authentication, read from PostgreSQL to ensure we have the latest data
    const user = await this.prisma.user.findUnique({
      where: { email },
    });
    
    if (!user) {
      throw new NotFoundException(`User with email ${email} not found`);
    }
    
    return user;
  }

  async update(id: string, updateUserDto: UpdateUserDto) {
    // Check if user exists
    const user = await this.prisma.user.findUnique({
      where: { id },
    });
    
    if (!user) {
      throw new NotFoundException(`User with ID ${id} not found`);
    }
    
    // Prepare update data
    const updateData: any = { ...updateUserDto };
    
    // Hash password if provided
    if (updateUserDto.password) {
      updateData.password = await bcrypt.hash(updateUserDto.password, 10);
    }
    
    // Update user in PostgreSQL
    const updatedUser = await this.prisma.user.update({
      where: { id },
      data: updateData,
    });
    
    // Sync to MongoDB for fast reads
    await this.syncUserToMongo(updatedUser);
    
    // Notify about user update
    this.notifyUserUpdate(updatedUser);
    
    // Exclude password from returned user
    const { password, ...userWithoutPassword } = updatedUser;
    return userWithoutPassword;
  }

  async remove(id: string) {
    // Check if user exists
    const user = await this.prisma.user.findUnique({
      where: { id },
    });
    
    if (!user) {
      throw new NotFoundException(`User with ID ${id} not found`);
    }
    
    // Delete user from PostgreSQL
    await this.prisma.user.delete({
      where: { id },
    });
    
    // Delete from MongoDB
    await this.mongoDbService.deleteDocument('users', id);
    
    // Notify about user deletion
    this.notifyUserDeletion(id);
    
    return { id };
  }

  // Private helper methods
  private async syncUserToMongo(user: any) {
    // Remove password before syncing to MongoDB
    const { password, ...userWithoutPassword } = user;
    await this.mongoDbService.syncDocument('users', userWithoutPassword);
  }

  private async notifyUserCreation(user: any) {
    // Remove password for security
    const { password, ...userWithoutPassword } = user;

    // Send message to RabbitMQ for event processing
    await this.rabbitMQService.publish({
      type: 'user_created',
      data: userWithoutPassword,
    });

    // Notify admin users via WebSocket
    this.websocketService.sendToRole('admin', 'user_created', {
      message: `New user registered: ${user.email}`,
      user: userWithoutPassword,
    });
  }

  private async notifyUserUpdate(user: any) {
    // Remove password for security
    const { password, ...userWithoutPassword } = user;

    // Send message to RabbitMQ for event processing
    await this.rabbitMQService.publish({
      type: 'user_updated',
      data: userWithoutPassword,
    });

    // Notify the user about their account update
    this.websocketService.sendToUser(user.id, 'profile_updated', {
      message: 'Your profile has been updated',
      user: userWithoutPassword,
    });
  }

  private async notifyUserDeletion(userId: string) {
    // Send message to RabbitMQ for event processing
    await this.rabbitMQService.publish({
      type: 'user_deleted',
      data: { id: userId },
    });

    // Notify admin users via WebSocket
    this.websocketService.sendToRole('admin', 'user_deleted', {
      message: `User with ID ${userId} has been deleted`,
      userId,
    });
  }
}
