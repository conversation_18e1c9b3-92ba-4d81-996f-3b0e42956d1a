import { Controller, Get, Post, Body, Patch, Param, Delete, UseGuards, Query, UseInterceptors } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth, ApiParam, ApiBody, ApiQuery } from '@nestjs/swagger';
import { OrderService } from '../services/order.service';
import { CreateOrderDto } from '../dto/create-order.dto';
import { UpdateOrderDto } from '../dto/update-order.dto';
import { OrderDto } from '../dto/order.dto';
import { JwtAuthGuard } from '@core/auth/guards/jwt-auth.guard';
import { RolesGuard } from '@core/auth/guards/roles.guard';
import { Roles } from '@core/auth/decorators/roles.decorator';
import { RequirePermissions, Resources, ResourceActions, createPermission } from '@shared/decorators/permissions.decorator';
import { PermissionsGuard } from '@shared/guards/permissions.guard';
import { CacheInterceptor } from '@nestjs/cache-manager';
import { StandardListParams, parseQueryParams } from '@shared/utils/query-params.util';

@ApiTags('orders')
@ApiBearerAuth()
@Controller('orders')
export class OrderController {
  constructor(private readonly orderService: OrderService) {}

  @Post()
  @UseGuards(JwtAuthGuard)
  @ApiOperation({ summary: 'Create a new order' })
  @ApiResponse({ status: 201, description: 'Order successfully created.', type: OrderDto })
  @ApiResponse({ status: 400, description: 'Bad Request.' })
  @ApiResponse({ status: 401, description: 'Unauthorized.' })
  @ApiBody({ type: CreateOrderDto })
  async create(@Body() createOrderDto: CreateOrderDto): Promise<OrderDto> {
    return this.orderService.create(createOrderDto);
  }

  @Get()
  @UseGuards(JwtAuthGuard, PermissionsGuard)
  @RequirePermissions(createPermission(Resources.ORDERS, ResourceActions.LIST))
  @UseInterceptors(CacheInterceptor)
  @ApiOperation({ summary: 'Get all orders' })
  @ApiResponse({ status: 200, description: 'Return all orders with pagination.', type: [OrderDto] })
  @ApiResponse({ status: 401, description: 'Unauthorized.' })
  @ApiResponse({ status: 403, description: 'Forbidden. Insufficient permissions.' })
  @ApiQuery({ name: 'page', required: false, description: 'Page number (starts from 1)' })
  @ApiQuery({ name: 'limit', required: false, description: 'Items per page (max 100)' })
  @ApiQuery({ name: 'where', required: false, description: 'JSON filtering conditions' })
  @ApiQuery({ name: 'search', required: false, description: 'Text search parameter' })
  @ApiQuery({ name: 'order', required: false, description: 'Sort order specification (field and direction)' })
  async findAll(@Query() query: Record<string, any>): Promise<{ data: OrderDto[]; total: number; page: number; limit: number }> {
    const params: StandardListParams = parseQueryParams(query);
    return this.orderService.findAll(params);
  }

  @Get(':id')
  @UseGuards(JwtAuthGuard)
  @ApiOperation({ summary: 'Get an order by ID' })
  @ApiParam({ name: 'id', description: 'Order ID' })
  @ApiResponse({ status: 200, description: 'Return the order.', type: OrderDto })
  @ApiResponse({ status: 401, description: 'Unauthorized.' })
  @ApiResponse({ status: 404, description: 'Order not found.' })
  async findOne(@Param('id') id: string): Promise<OrderDto> {
    return this.orderService.findOne(id);
  }

  @Get('customer/:customerId')
  @UseGuards(JwtAuthGuard)
  @ApiOperation({ summary: 'Get orders by customer ID' })
  @ApiParam({ name: 'customerId', description: 'Customer ID' })
  @ApiResponse({ status: 200, description: 'Return orders for the customer.', type: [OrderDto] })
  @ApiResponse({ status: 401, description: 'Unauthorized.' })
  async findByCustomer(@Param('customerId') customerId: string): Promise<OrderDto[]> {
    return this.orderService.findByCustomer(customerId);
  }

  @Patch(':id')
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles('admin', 'sales')
  @ApiOperation({ summary: 'Update an order' })
  @ApiParam({ name: 'id', description: 'Order ID' })
  @ApiBody({ type: UpdateOrderDto })
  @ApiResponse({ status: 200, description: 'Order successfully updated.', type: OrderDto })
  @ApiResponse({ status: 400, description: 'Bad Request.' })
  @ApiResponse({ status: 401, description: 'Unauthorized.' })
  @ApiResponse({ status: 403, description: 'Forbidden. Requires admin or sales role.' })
  @ApiResponse({ status: 404, description: 'Order not found.' })
  async update(@Param('id') id: string, @Body() updateOrderDto: UpdateOrderDto): Promise<OrderDto> {
    return this.orderService.update(id, updateOrderDto);
  }

  @Patch(':id/cancel')
  @UseGuards(JwtAuthGuard)
  @ApiOperation({ summary: 'Cancel an order' })
  @ApiParam({ name: 'id', description: 'Order ID' })
  @ApiResponse({ status: 200, description: 'Order successfully cancelled.', type: OrderDto })
  @ApiResponse({ status: 400, description: 'Bad Request. Order cannot be cancelled in current state.' })
  @ApiResponse({ status: 401, description: 'Unauthorized.' })
  @ApiResponse({ status: 404, description: 'Order not found.' })
  async cancel(@Param('id') id: string): Promise<OrderDto> {
    return this.orderService.cancel(id);
  }
}
