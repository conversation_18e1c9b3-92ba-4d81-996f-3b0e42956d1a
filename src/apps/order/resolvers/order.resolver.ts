import { Resolver, Query, Mutation, Args, ID } from '@nestjs/graphql';
import { UseGuards } from '@nestjs/common';
import { OrderService } from '../services/order.service';
import { Order } from '../entities/order.entity';
import { CreateOrderDto } from '../dto/create-order.dto';
import { UpdateOrderDto } from '../dto/update-order.dto';
import { JwtAuthGuard } from '../../../core/auth/guards/jwt-auth.guard';
import { RolesGuard } from '../../../core/auth/guards/roles.guard';
import { Roles } from '../../../core/auth/decorators/roles.decorator';
import { CurrentUser } from '../../../graphql/decorators/gql-auth.decorator';

@Resolver(() => Order)
export class OrderResolver {
  constructor(private readonly orderService: OrderService) {}

  @Mutation(() => Order)
  @UseGuards(JwtAuthGuard)
  createOrder(@Args('createOrderInput') createOrderDto: CreateOrderDto) {
    return this.orderService.create(createOrderDto);
  }

  @Query(() => [Order], { name: 'orders' })
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles('admin', 'sales')
  findAllOrders() {
    return this.orderService.findAll();
  }

  @Query(() => Order, { name: 'order' })
  @UseGuards(JwtAuthGuard)
  findOneOrder(@Args('id', { type: () => ID }) id: string) {
    return this.orderService.findOne(id);
  }

  @Query(() => [Order], { name: 'ordersByCustomer' })
  @UseGuards(JwtAuthGuard)
  findOrdersByCustomer(
    @Args('customerId', { type: () => ID }) customerId: string,
    @CurrentUser() user: any,
  ) {
    // Check if user is requesting their own orders or has admin/sales role
    if (user.id !== customerId && !user.roles.some(role => ['admin', 'sales'].includes(role))) {
      throw new Error('Unauthorized to access these orders');
    }
    return this.orderService.findByCustomer(customerId);
  }

  @Query(() => [Order], { name: 'myOrders' })
  @UseGuards(JwtAuthGuard)
  findMyOrders(@CurrentUser() user: any) {
    return this.orderService.findByCustomer(user.id);
  }

  @Mutation(() => Order)
  @UseGuards(JwtAuthGuard, RolesGuard)
  @Roles('admin', 'sales')
  updateOrder(
    @Args('id', { type: () => ID }) id: string,
    @Args('updateOrderInput') updateOrderDto: UpdateOrderDto,
  ) {
    return this.orderService.update(id, updateOrderDto);
  }

  @Mutation(() => Order)
  @UseGuards(JwtAuthGuard)
  cancelOrder(
    @Args('id', { type: () => ID }) id: string,
    @CurrentUser() user: any,
  ) {
    // First check if the user is the owner of this order or has admin/sales role
    // This would normally be handled with detailed logic checking the order's customer ID
    // For now, we'll assume the service will handle this validation
    return this.orderService.cancel(id);
  }
}
