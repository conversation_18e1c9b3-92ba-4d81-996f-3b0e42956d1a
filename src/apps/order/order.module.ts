import { Module } from '@nestjs/common';
import { OrderService } from './services/order.service';
import { OrderController } from './controllers/order.controller';
import { OrderResolver } from './resolvers/order.resolver';
import { CoreModule } from '@core/core.module';
import { SharedModule } from '@shared/shared.module';
import { ProductModule } from '@apps/product/product.module';
import { MongoDbModule } from '@infra/mongodb/mongodb.module';
import { WebsocketModule } from '@infra/websocket/websocket.module';
import { RabbitMQModule } from '@infra/rabbitmq/rabbitmq.module';

@Module({
  imports: [
    CoreModule, 
    SharedModule, 
    ProductModule,
    MongoDbModule,
    WebsocketModule,
    RabbitMQModule,
  ],
  providers: [OrderService, OrderResolver],
  controllers: [OrderController],
  exports: [OrderService],
})
export class OrderModule {}
