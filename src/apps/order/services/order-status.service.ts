import { Injectable, BadRequestException, Logger } from '@nestjs/common';
import { PrismaService } from '@core/prisma/prisma.service';
import { MongoDbService } from '@infra/mongodb/mongodb.service';
import { WebsocketService } from '@infra/websocket/websocket.service';
import { RabbitMQService } from '@infra/rabbitmq/rabbitmq.service';
import { DetailedOrderStatus, OrderStatus } from '@prisma/client';

@Injectable()
export class OrderStatusService {
  private readonly logger = new Logger(OrderStatusService.name);

  // Define valid status transitions
  private static readonly VALID_TRANSITIONS: Record<DetailedOrderStatus, DetailedOrderStatus[]> = {
    [DetailedOrderStatus.NEW]: [
      DetailedOrderStatus.ORDER_RECEIVED,
      DetailedOrderStatus.CANCELLED
    ],
    [DetailedOrderStatus.ORDER_RECEIVED]: [
      DetailedOrderStatus.MEASUREMENTS_CONFIRMED,
      DetailedOrderStatus.CANCELLED
    ],
    [DetailedOrderStatus.MEASUREMENTS_CONFIRMED]: [
      DetailedOrderStatus.MATERIALS_ACQUIRED,
      DetailedOrderStatus.CANCELLED
    ],
    [DetailedOrderStatus.MATERIALS_ACQUIRED]: [
      DetailedOrderStatus.CUTTING_STARTED,
      DetailedOrderStatus.CANCELLED
    ],
    [DetailedOrderStatus.CUTTING_STARTED]: [
      DetailedOrderStatus.STITCHING_IN_PROGRESS,
      DetailedOrderStatus.CANCELLED
    ],
    [DetailedOrderStatus.STITCHING_IN_PROGRESS]: [
      DetailedOrderStatus.FINISHING_TOUCHES,
      DetailedOrderStatus.CANCELLED
    ],
    [DetailedOrderStatus.FINISHING_TOUCHES]: [
      DetailedOrderStatus.QUALITY_CHECK,
      DetailedOrderStatus.CANCELLED
    ],
    [DetailedOrderStatus.QUALITY_CHECK]: [
      DetailedOrderStatus.READY_FOR_DELIVERY,
      DetailedOrderStatus.STITCHING_IN_PROGRESS, // Back to stitching if quality issues
      DetailedOrderStatus.CANCELLED
    ],
    [DetailedOrderStatus.READY_FOR_DELIVERY]: [
      DetailedOrderStatus.DELIVERED,
      DetailedOrderStatus.CANCELLED
    ],
    [DetailedOrderStatus.DELIVERED]: [
      DetailedOrderStatus.COMPLETED,
      DetailedOrderStatus.RETURNED
    ],
    [DetailedOrderStatus.COMPLETED]: [],
    [DetailedOrderStatus.CANCELLED]: [],
    [DetailedOrderStatus.RETURNED]: [
      DetailedOrderStatus.STITCHING_IN_PROGRESS, // For rework
      DetailedOrderStatus.CANCELLED
    ],
  };

  constructor(
    private readonly prisma: PrismaService,
    private readonly mongoDbService: MongoDbService,
    private readonly websocketService: WebsocketService,
    private readonly rabbitMQService: RabbitMQService,
  ) {}

  async updateOrderStatus(
    orderId: string,
    newStatus: DetailedOrderStatus,
    notes?: string,
    updatedBy?: string
  ): Promise<any> {
    try {
      // Get current order
      const order = await this.prisma.order.findUnique({
        where: { id: orderId },
        include: { customer: true, tailor: true },
      });

      if (!order) {
        throw new BadRequestException(`Order with ID ${orderId} not found`);
      }

      // Validate status transition
      if (!this.isValidTransition(order.detailedStatus, newStatus)) {
        throw new BadRequestException(
          `Invalid status transition from ${order.detailedStatus} to ${newStatus}`
        );
      }

      // Update order with new status and timestamp
      const timestampField = this.getTimestampField(newStatus);
      const updatedOrder = await this.prisma.order.update({
        where: { id: orderId },
        data: {
          detailedStatus: newStatus,
          ...timestampField,
          // Update basic status for backward compatibility
          status: this.mapToBasicStatus(newStatus),
        },
        include: { customer: true, tailor: true },
      });

      // Record status history
      await this.recordStatusHistory(orderId, newStatus, notes, updatedBy);

      // Sync to MongoDB
      await this.mongoDbService.syncDocument('orders', updatedOrder);

      // Send notifications
      await this.notifyStatusChange(updatedOrder, newStatus);

      // Publish event for other services
      await this.rabbitMQService.publish({
        type: 'order_status_updated',
        data: {
          orderId,
          oldStatus: order.detailedStatus,
          newStatus,
          updatedBy,
          timestamp: new Date(),
        },
      });

      return updatedOrder;
    } catch (error) {
      this.logger.error(`Failed to update order status: ${error.message}`, error.stack);
      throw error;
    }
  }

  async getStatusHistory(orderId: string): Promise<any[]> {
    return this.prisma.orderStatusHistory.findMany({
      where: { orderId },
      include: { updater: { select: { id: true, firstName: true, lastName: true, email: true } } },
      orderBy: { timestamp: 'desc' },
    });
  }

  async getOrderTimeline(orderId: string): Promise<any> {
    const order = await this.prisma.order.findUnique({
      where: { id: orderId },
      include: { statusHistory: { include: { updater: true }, orderBy: { timestamp: 'asc' } } },
    });

    if (!order) {
      throw new BadRequestException(`Order with ID ${orderId} not found`);
    }

    const timeline = {
      orderId,
      currentStatus: order.detailedStatus,
      estimatedCompletion: order.estimatedCompletion,
      actualCompletion: order.actualCompletion,
      stages: this.buildTimelineStages(order),
      history: order.statusHistory,
    };

    return timeline;
  }

  private isValidTransition(from: DetailedOrderStatus, to: DetailedOrderStatus): boolean {
    return OrderStatusService.VALID_TRANSITIONS[from]?.includes(to) ?? false;
  }

  private getTimestampField(status: DetailedOrderStatus): Record<string, Date> {
    const now = new Date();
    switch (status) {
      case DetailedOrderStatus.MEASUREMENTS_CONFIRMED:
        return { measurementsConfirmedAt: now };
      case DetailedOrderStatus.MATERIALS_ACQUIRED:
        return { materialsAcquiredAt: now };
      case DetailedOrderStatus.CUTTING_STARTED:
        return { cuttingStartedAt: now };
      case DetailedOrderStatus.STITCHING_IN_PROGRESS:
        return { stitchingStartedAt: now };
      case DetailedOrderStatus.FINISHING_TOUCHES:
        return { finishingStartedAt: now };
      case DetailedOrderStatus.QUALITY_CHECK:
        return { qualityCheckAt: now };
      case DetailedOrderStatus.READY_FOR_DELIVERY:
        return { readyForDeliveryAt: now };
      case DetailedOrderStatus.DELIVERED:
        return { deliveredAt: now };
      case DetailedOrderStatus.COMPLETED:
        return { actualCompletion: now };
      default:
        return {};
    }
  }

  private mapToBasicStatus(detailedStatus: DetailedOrderStatus): OrderStatus {
    switch (detailedStatus) {
      case DetailedOrderStatus.NEW:
      case DetailedOrderStatus.ORDER_RECEIVED:
        return OrderStatus.NEW;
      case DetailedOrderStatus.MEASUREMENTS_CONFIRMED:
        return OrderStatus.APPROVED;
      case DetailedOrderStatus.MATERIALS_ACQUIRED:
      case DetailedOrderStatus.CUTTING_STARTED:
      case DetailedOrderStatus.STITCHING_IN_PROGRESS:
      case DetailedOrderStatus.FINISHING_TOUCHES:
      case DetailedOrderStatus.QUALITY_CHECK:
        return OrderStatus.IN_PROGRESS;
      case DetailedOrderStatus.READY_FOR_DELIVERY:
        return OrderStatus.COMPLETE;
      case DetailedOrderStatus.DELIVERED:
        return OrderStatus.DELIVERED;
      case DetailedOrderStatus.COMPLETED:
        return OrderStatus.DELIVERED;
      case DetailedOrderStatus.CANCELLED:
        return OrderStatus.CANCELLED;
      case DetailedOrderStatus.RETURNED:
        return OrderStatus.RETURNED;
      default:
        return OrderStatus.NEW;
    }
  }

  private async recordStatusHistory(
    orderId: string,
    status: DetailedOrderStatus,
    notes?: string,
    updatedBy?: string
  ): Promise<void> {
    await this.prisma.orderStatusHistory.create({
      data: {
        orderId,
        status,
        notes,
        updatedBy: updatedBy || 'system',
      },
    });
  }

  private async notifyStatusChange(order: any, newStatus: DetailedOrderStatus): Promise<void> {
    const message = this.getStatusMessage(newStatus);

    // Notify customer
    await this.websocketService.sendToUser(order.customerId, 'order_status_update', {
      orderId: order.id,
      status: newStatus,
      message,
      estimatedCompletion: order.estimatedCompletion,
      timeline: await this.getOrderTimeline(order.id),
    });

    // Notify tailor if applicable
    if (order.tailorId) {
      await this.websocketService.sendToUser(order.tailorId, 'order_status_update', {
        orderId: order.id,
        status: newStatus,
        message: `Order ${order.id} status updated to ${newStatus}`,
      });
    }

    // Create database notification
    await this.prisma.notification.create({
      data: {
        userId: order.customerId,
        title: 'Order Status Update',
        body: message,
        type: 'order_update',
        data: {
          orderId: order.id,
          status: newStatus,
        },
      },
    });
  }

  private getStatusMessage(status: DetailedOrderStatus): string {
    const messages = {
      [DetailedOrderStatus.ORDER_RECEIVED]: 'Your order has been received and is being reviewed.',
      [DetailedOrderStatus.MEASUREMENTS_CONFIRMED]: 'Your measurements have been confirmed.',
      [DetailedOrderStatus.MATERIALS_ACQUIRED]: 'Materials for your order have been acquired.',
      [DetailedOrderStatus.CUTTING_STARTED]: 'Cutting process has started for your garment.',
      [DetailedOrderStatus.STITCHING_IN_PROGRESS]: 'Your garment is being stitched.',
      [DetailedOrderStatus.FINISHING_TOUCHES]: 'Final touches are being added to your garment.',
      [DetailedOrderStatus.QUALITY_CHECK]: 'Your garment is undergoing quality inspection.',
      [DetailedOrderStatus.READY_FOR_DELIVERY]: 'Your order is ready for pickup/delivery!',
      [DetailedOrderStatus.DELIVERED]: 'Your order has been delivered.',
      [DetailedOrderStatus.COMPLETED]: 'Your order is complete. Thank you!',
      [DetailedOrderStatus.CANCELLED]: 'Your order has been cancelled.',
      [DetailedOrderStatus.RETURNED]: 'Your order has been returned for rework.',
    };

    return messages[status] || 'Order status updated.';
  }

  private buildTimelineStages(order: any): any[] {
    const stages = [
      { status: DetailedOrderStatus.NEW, name: 'Order Placed', timestamp: order.createdAt },
      { status: DetailedOrderStatus.ORDER_RECEIVED, name: 'Order Received', timestamp: null },
      { status: DetailedOrderStatus.MEASUREMENTS_CONFIRMED, name: 'Measurements Confirmed', timestamp: order.measurementsConfirmedAt },
      { status: DetailedOrderStatus.MATERIALS_ACQUIRED, name: 'Materials Acquired', timestamp: order.materialsAcquiredAt },
      { status: DetailedOrderStatus.CUTTING_STARTED, name: 'Cutting Started', timestamp: order.cuttingStartedAt },
      { status: DetailedOrderStatus.STITCHING_IN_PROGRESS, name: 'Stitching in Progress', timestamp: order.stitchingStartedAt },
      { status: DetailedOrderStatus.FINISHING_TOUCHES, name: 'Finishing Touches', timestamp: order.finishingStartedAt },
      { status: DetailedOrderStatus.QUALITY_CHECK, name: 'Quality Check', timestamp: order.qualityCheckAt },
      { status: DetailedOrderStatus.READY_FOR_DELIVERY, name: 'Ready for Delivery', timestamp: order.readyForDeliveryAt },
      { status: DetailedOrderStatus.DELIVERED, name: 'Delivered', timestamp: order.deliveredAt },
      { status: DetailedOrderStatus.COMPLETED, name: 'Completed', timestamp: order.actualCompletion },
    ];

    return stages.map(stage => ({
      ...stage,
      isCompleted: this.isStageCompleted(stage.status, order.detailedStatus),
      isCurrent: stage.status === order.detailedStatus,
    }));
  }

  private isStageCompleted(stageStatus: DetailedOrderStatus, currentStatus: DetailedOrderStatus): boolean {
    const statusOrder = [
      DetailedOrderStatus.NEW,
      DetailedOrderStatus.ORDER_RECEIVED,
      DetailedOrderStatus.MEASUREMENTS_CONFIRMED,
      DetailedOrderStatus.MATERIALS_ACQUIRED,
      DetailedOrderStatus.CUTTING_STARTED,
      DetailedOrderStatus.STITCHING_IN_PROGRESS,
      DetailedOrderStatus.FINISHING_TOUCHES,
      DetailedOrderStatus.QUALITY_CHECK,
      DetailedOrderStatus.READY_FOR_DELIVERY,
      DetailedOrderStatus.DELIVERED,
      DetailedOrderStatus.COMPLETED,
    ];

    // Handle special cases for cancelled and returned orders
    if (currentStatus === DetailedOrderStatus.CANCELLED || currentStatus === DetailedOrderStatus.RETURNED) {
      return false;
    }

    const stageIndex = statusOrder.indexOf(stageStatus as any);
    const currentIndex = statusOrder.indexOf(currentStatus as any);

    return stageIndex !== -1 && currentIndex !== -1 && stageIndex <= currentIndex;
  }
}
