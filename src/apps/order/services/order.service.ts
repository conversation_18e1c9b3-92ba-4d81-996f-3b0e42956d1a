import { Injectable, NotFoundException, BadRequestException } from '@nestjs/common';
import { PrismaService } from '@core/prisma/prisma.service';
import { CreateOrderDto, CreateOrderItemDto } from '../dto/create-order.dto';
import { UpdateOrderDto } from '../dto/update-order.dto';
import { OrderDto, OrderItemDto } from '../dto/order.dto';
import { OrderStatus, PaymentStatus } from '../entities/order.entity';
import { MongoDbService } from '@infra/mongodb/mongodb.service';
import { RabbitMQService } from '@infra/rabbitmq/rabbitmq.service';
import { WebsocketService } from '@infra/websocket/websocket.service';
import { DecimalService } from '@shared/decimal/decimal.service';
import { ProductService } from '@apps/product/services/product.service';
import { StandardListParams, toMongoDbOptions, getPaginationMetadata } from '@shared/utils/query-params.util';
import { Decimal } from 'decimal.js';

@Injectable()
export class OrderService {
  constructor(
    private readonly prisma: PrismaService,
    private readonly mongoDbService: MongoDbService,
    private readonly rabbitMQService: RabbitMQService,
    private readonly websocketService: WebsocketService,
    private readonly decimalService: DecimalService,
    private readonly productService: ProductService,
  ) {}

  async create(createOrderDto: CreateOrderDto): Promise<OrderDto> {
    try {
      // Get products for all order items to calculate prices and check availability
      const orderItems = await Promise.all(
        createOrderDto.items.map(async (item) => {
          const product = await this.productService.findOne(item.productId);
          
          if (product.stockQuantity < item.quantity) {
            throw new BadRequestException(
              `Insufficient stock for product: ${product.name}. Available: ${product.stockQuantity}, Requested: ${item.quantity}`
            );
          }

          const unitPrice = this.decimalService.create(product.price) as Decimal;
          let itemTotal = this.decimalService.multiply(unitPrice.toString(), item.quantity) as Decimal;
          
          // Apply item-level discount if present
          let itemDiscount: Decimal | null = null;
          if (item.discount) {
            itemDiscount = this.decimalService.create(item.discount.toString()) as Decimal;
            itemTotal = this.decimalService.subtract(itemTotal.toString(), itemDiscount.toString()) as Decimal;
          }
          
          return {
            productId: product.id,
            productName: product.name,
            unitPrice: unitPrice.toString(),
            quantity: item.quantity,
            totalPrice: itemTotal.toString(),
            discount: itemDiscount?.toString(),
          };
        })
      );
      
      // Calculate order totals
      const subtotal = orderItems.reduce(
        (sum, item) => this.decimalService.add(sum.toString(), item.totalPrice) as Decimal,
        this.decimalService.create(0) as Decimal
      );
      
      // Apply order-level discount if present
      let discount: Decimal | null = null;
      if (createOrderDto.discount) {
        discount = this.decimalService.create(createOrderDto.discount.toString()) as Decimal;
      }
      
      // Calculate shipping cost
      const shipping = createOrderDto.shipping 
        ? this.decimalService.create(createOrderDto.shipping.toString()) as Decimal
        : this.decimalService.create(0) as Decimal;
      
      // Calculate tax (for example, 10% of subtotal)
      const taxRate = this.decimalService.create(0.1) as Decimal;
      const tax = this.decimalService.multiply(subtotal.toString(), taxRate.toString()) as Decimal;
      
      // Calculate final total
      let total = this.decimalService.add(subtotal.toString(), tax.toString()) as Decimal;
      total = this.decimalService.add(total.toString(), shipping.toString()) as Decimal;
      
      if (discount) {
        total = this.decimalService.subtract(total.toString(), discount.toString()) as Decimal;
      }
      
      // Create the order in PostgreSQL (source of truth)
      const order = await this.prisma.order.create({
        data: {
          customerId: createOrderDto.customerId,
          customerName: createOrderDto.customerName,
          customerEmail: createOrderDto.customerEmail,
          status: createOrderDto.status || OrderStatus.NEW,
          paymentStatus: createOrderDto.paymentStatus || PaymentStatus.PENDING,
          paymentMethod: createOrderDto.paymentMethod,
          subtotal: subtotal.toString(),
          tax: tax.toString(),
          shipping: shipping.toString(),
          discount: discount?.toString(),
          total: total.toString(),
          notes: createOrderDto.notes,
          shippingAddress: createOrderDto.shippingAddress,
          billingAddress: createOrderDto.billingAddress || createOrderDto.shippingAddress,
          items: {
            create: orderItems.map(item => ({
              productId: item.productId,
              productName: item.productName,
              unitPrice: item.unitPrice,
              quantity: item.quantity,
              totalPrice: item.totalPrice,
              discount: item.discount,
            })),
          },
        },
        include: {
          items: true,
        },
      });
      
      // Update product quantities (reduce stock)
      await Promise.all(
        createOrderDto.items.map(async (item) => {
          await this.productService.updateStock(item.productId, -item.quantity);
        })
      );
      
      // Sync to MongoDB for fast reads
      await this.syncOrderToMongo(order);
      
      // Notify about new order creation
      await this.notifyOrderCreation(order);
      
      // Map to OrderDto
      return this.mapToOrderDto(order);
    } catch (error) {
      if (error instanceof BadRequestException || error instanceof NotFoundException) {
        throw error;
      }
      throw new BadRequestException(`Failed to create order: ${error.message}`);
    }
  }

  async findAll(params: StandardListParams = {}): Promise<{ data: OrderDto[]; total: number; page: number; limit: number }> {
    // Create query based on filters
    const query: Record<string, any> = {};
    
    // Apply where conditions if provided
    if (params.where) {
      Object.assign(query, params.where);
    }
    
    // Apply text search if provided
    if (params.search) {
      query.$or = [
        { id: { $regex: params.search, $options: 'i' } },
        { customerName: { $regex: params.search, $options: 'i' } },
        { customerEmail: { $regex: params.search, $options: 'i' } },
        { status: { $regex: params.search, $options: 'i' } },
        { paymentStatus: { $regex: params.search, $options: 'i' } },
      ];
    }
    
    // Get total count of matching documents
    const totalCount = await this.mongoDbService.count('orders', query);
    
    // Apply sorting, pagination and get orders
    const options = toMongoDbOptions(params);
    const orders = await this.mongoDbService.find('orders', query, options);
    
    // Map orders to DTOs
    const orderDtos = orders.map(order => this.mapToOrderDto(order));
    
    const pagination = getPaginationMetadata(params, totalCount);
    
    // Return data with pagination metadata
    return {
      data: orderDtos,
      total: pagination.totalCount,
      page: pagination.page,
      limit: pagination.limit
    };
  }

  async findOne(id: string): Promise<OrderDto> {
    // Read from MongoDB for better performance
    const order = await this.mongoDbService.findOne('orders', { id });
    
    if (!order) {
      throw new NotFoundException(`Order with ID ${id} not found`);
    }
    
    // Map to OrderDto
    return this.mapToOrderDto(order);
  }

  async findByCustomer(customerId: string): Promise<OrderDto[]> {
    // Read from MongoDB for better performance
    const orders = await this.mongoDbService.find('orders', { customerId });
    // Map to OrderDto array
    return orders.map(order => this.mapToOrderDto(order));
  }

  async update(id: string, updateOrderDto: UpdateOrderDto): Promise<OrderDto> {
    // Check if order exists
    const order = await this.prisma.order.findUnique({
      where: { id },
      include: { items: true },
    });
    
    if (!order) {
      throw new NotFoundException(`Order with ID ${id} not found`);
    }
    
    // Update order status
    const updatedOrder = await this.prisma.order.update({
      where: { id },
      data: {
        status: updateOrderDto.status,
        paymentStatus: updateOrderDto.paymentStatus,
        trackingNumber: updateOrderDto.trackingNumber,
        notes: updateOrderDto.notes,
        shippingAddress: updateOrderDto.shippingAddress,
        billingAddress: updateOrderDto.billingAddress,
        // Track shipping and delivery dates based on status changes
        shippedAt: updateOrderDto.status === OrderStatus.IN_PROGRESS ? new Date() : order.shippedAt,
        deliveredAt: updateOrderDto.status === OrderStatus.DELIVERED ? new Date() : order.deliveredAt,
      },
      include: {
        items: true,
      },
    });
    
    // Sync to MongoDB for fast reads
    await this.syncOrderToMongo(updatedOrder);
    
    // Notify about order update
    await this.notifyOrderUpdate(updatedOrder);
    
    // Map to OrderDto
    return this.mapToOrderDto(updatedOrder);
  }

  async cancel(id: string): Promise<OrderDto> {
    // Check if order exists
    const order = await this.prisma.order.findUnique({
      where: { id },
      include: { items: true },
    });
    
    if (!order) {
      throw new NotFoundException(`Order with ID ${id} not found`);
    }
    
    // Only allow cancellation of pending or processing orders
    const allowedStatuses = ['PENDING', 'PROCESSING'];
    if (!allowedStatuses.includes(order.status)) {
      throw new BadRequestException(`Cannot cancel order with status: ${order.status}`);
    }
    
    // Update order status to cancelled
    const cancelledOrder = await this.prisma.order.update({
      where: { id },
      data: {
        status: OrderStatus.CANCELLED,
      },
      include: {
        items: true,
      },
    });
    
    // Restore product stock quantities
    await Promise.all(
      order.items.map(async (item) => {
        await this.productService.updateStock(item.productId, item.quantity);
      })
    );
    
    // Sync to MongoDB for fast reads
    await this.syncOrderToMongo(cancelledOrder);
    
    // Notify about order cancellation
    await this.notifyOrderCancellation(cancelledOrder);
    
    // Map to OrderDto
    return this.mapToOrderDto(cancelledOrder);
  }

  // Private helper methods
  private async syncOrderToMongo(order: any) {
    await this.mongoDbService.syncDocument('orders', order);
  }

  private async notifyOrderCreation(order: any) {
    // Send message to RabbitMQ for event processing
    await this.rabbitMQService.publish({
      type: 'order_created',
      data: order,
    });

    // Notify admin users via WebSocket
    this.websocketService.sendToRole('admin', 'order_created', {
      message: `New order created: #${order.id}`,
      order,
    });

    // Notify customer about their order
    this.websocketService.sendToUser(order.customerId, 'order_confirmation', {
      message: `Your order #${order.id} has been placed successfully`,
      order,
    });

    // Update dashboard
    this.websocketService.sendDashboardUpdate({
      type: 'order_count',
      data: await this.mongoDbService.count('orders', {}),
    });
  }

  private async notifyOrderUpdate(order: any) {
    // Send message to RabbitMQ for event processing
    await this.rabbitMQService.publish({
      type: 'order_updated',
      data: order,
    });

    // Notify admin users via WebSocket
    this.websocketService.sendToRole('admin', 'order_updated', {
      message: `Order #${order.id} has been updated: ${order.status}`,
      order,
    });

    // Notify customer about their order update
    this.websocketService.sendToUser(order.customerId, 'order_status_change', {
      message: `Your order #${order.id} status has been updated to: ${order.status}`,
      order,
    });
  }

  private async notifyOrderCancellation(order: any) {
    // Send message to RabbitMQ for event processing
    await this.rabbitMQService.publish({
      type: 'order_cancelled',
      data: order,
    });

    // Notify admin users via WebSocket
    this.websocketService.sendToRole('admin', 'order_cancelled', {
      message: `Order #${order.id} has been cancelled`,
      order,
    });

    // Notify customer about their order cancellation
    this.websocketService.sendToUser(order.customerId, 'order_cancelled', {
      message: `Your order #${order.id} has been cancelled`,
      order,
    });
  }
  
  /**
   * Maps a database order object to an OrderDto
   */
  private mapToOrderDto(order: any): OrderDto {
    const orderDto = new OrderDto();
    orderDto.id = order.id;
    orderDto.userId = order.customerId || order.userId; // Handle both field names
    orderDto.status = order.status;
    orderDto.paymentStatus = order.paymentStatus;
    orderDto.paymentMethod = order.paymentMethod || '';
    orderDto.totalAmount = Number(order.total);
    orderDto.shippingAddress = order.shippingAddress || '';
    orderDto.billingAddress = order.billingAddress || '';
    orderDto.createdAt = new Date(order.createdAt);
    orderDto.updatedAt = new Date(order.updatedAt);
    
    if (order.notes) {
      orderDto.notes = order.notes;
    }
    
    // Map order items if they exist
    if (order.items && Array.isArray(order.items)) {
      orderDto.items = order.items.map(item => {
        const orderItemDto = new OrderItemDto();
        orderItemDto.id = item.id;
        orderItemDto.productId = item.productId;
        orderItemDto.productName = item.productName;
        orderItemDto.quantity = item.quantity;
        orderItemDto.unitPrice = Number(item.unitPrice);
        orderItemDto.totalPrice = Number(item.totalPrice);
        if (item.discount) {
          orderItemDto.discount = Number(item.discount);
        }
        return orderItemDto;
      });
    } else {
      orderDto.items = [];
    }
    
    return orderDto;
  }
}
