import { ObjectType, Field, ID, registerEnumType } from '@nestjs/graphql';
import { User } from '../../user/entities/user.entity';
import { Product } from '../../product/entities/product.entity';

export enum OrderStatus {
  NEW = 'NEW',
  APPROVED = 'APPROVED',
  IN_PROGRESS = 'IN_PROGRESS',
  COMPLETE = 'COMPLETE',
  DELIVERED = 'DELIVERED',
  CANCELLED = 'CANCELLED',
  RETURNED = 'RETURNED',
}

export enum DetailedOrderStatus {
  // Initial stages
  NEW = 'NEW',
  ORDER_RECEIVED = 'ORDER_RECEIVED',

  // Preparation stages
  MEASUREMENTS_CONFIRMED = 'MEASUREMENTS_CONFIRMED',
  MATERIALS_ACQUIRED = 'MATERIALS_ACQUIRED',

  // Production stages
  CUTTING_STARTED = 'CUTTING_STARTED',
  STITCHING_IN_PROGRESS = 'STITCHING_IN_PROGRESS',
  FINISHING_TOUCHES = 'FINISHING_TOUCHES',

  // Quality and completion
  QUALITY_CHECK = 'QUALITY_CHECK',
  READY_FOR_DELIVERY = 'READY_FOR_DELIVERY',

  // Final stages
  DELIVERED = 'DELIVERED',
  COMPLETED = 'COMPLETED',

  // Exception cases
  CANCELLED = 'CANCELLED',
  RETURNED = 'RETURNED',
}

registerEnumType(OrderStatus, {
  name: 'OrderStatus',
  description: 'The status of an order',
});

export enum PaymentStatus {
  PENDING = 'PENDING',
  PAID = 'PAID',
  FAILED = 'FAILED',
  REFUNDED = 'REFUNDED',
}

registerEnumType(PaymentStatus, {
  name: 'PaymentStatus',
  description: 'The payment status of an order',
});

@ObjectType()
export class OrderItem {
  @Field(() => ID)
  id: string;

  @Field(() => ID)
  orderId: string;

  @Field(() => ID)
  productId: string;

  @Field(() => Product, { nullable: true })
  product?: Product;

  @Field()
  productName: string;

  @Field(() => String)
  unitPrice: string;

  @Field(() => Number)
  quantity: number;

  @Field(() => String)
  totalPrice: string;

  @Field(() => String, { nullable: true })
  discount?: string;

  @Field(() => Date)
  createdAt: Date;

  @Field(() => Date)
  updatedAt: Date;
}

@ObjectType()
export class Order {
  @Field(() => ID)
  id: string;

  @Field(() => ID)
  customerId: string;

  @Field(() => User, { nullable: true })
  customer?: User;

  @Field(() => String, { nullable: true })
  customerName?: string;
  
  @Field(() => String, { nullable: true })
  customerEmail?: string;

  @Field(() => OrderStatus)
  status: OrderStatus;

  @Field(() => PaymentStatus)
  paymentStatus: PaymentStatus;

  @Field(() => String, { nullable: true })
  paymentMethod?: string;

  @Field(() => String)
  subtotal: string;

  @Field(() => String)
  tax: string;

  @Field(() => String)
  shipping: string;

  @Field(() => String, { nullable: true })
  discount?: string;

  @Field(() => String)
  total: string;

  @Field(() => [OrderItem], { nullable: true })
  items?: OrderItem[];

  @Field(() => String, { nullable: true })
  notes?: string;

  @Field(() => String, { nullable: true })
  shippingAddress?: string;

  @Field(() => String, { nullable: true })
  billingAddress?: string;

  @Field(() => String, { nullable: true })
  trackingNumber?: string;

  @Field(() => Date)
  createdAt: Date;

  @Field(() => Date)
  updatedAt: Date;

  @Field(() => Date, { nullable: true })
  shippedAt?: Date;

  @Field(() => Date, { nullable: true })
  deliveredAt?: Date;
}
