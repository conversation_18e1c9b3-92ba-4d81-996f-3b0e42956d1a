import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsEnum, IsOptional, IsString, MaxLength } from 'class-validator';

export enum DetailedOrderStatus {
  // Initial stages
  NEW = 'NEW',
  ORDER_RECEIVED = 'ORDER_RECEIVED',
  
  // Preparation stages
  MEASUREMENTS_CONFIRMED = 'MEASUREMENTS_CONFIRMED',
  MATERIALS_ACQUIRED = 'MATERIALS_ACQUIRED',
  
  // Production stages
  CUTTING_STARTED = 'CUTTING_STARTED',
  STITCHING_IN_PROGRESS = 'STITCHING_IN_PROGRESS',
  FINISHING_TOUCHES = 'FINISHING_TOUCHES',
  
  // Quality and completion
  QUALITY_CHECK = 'QUALITY_CHECK',
  READY_FOR_DELIVERY = 'READY_FOR_DELIVERY',
  
  // Final stages
  DELIVERED = 'DELIVERED',
  COMPLETED = 'COMPLETED',
  
  // Exception cases
  CANCELLED = 'CANCELLED',
  RETURNED = 'RETURNED',
}

export class UpdateOrderStatusDto {
  @ApiProperty({
    description: 'New detailed status for the order',
    enum: DetailedOrderStatus,
    example: DetailedOrderStatus.STITCHING_IN_PROGRESS,
  })
  @IsEnum(DetailedOrderStatus)
  status: DetailedOrderStatus;

  @ApiPropertyOptional({
    description: 'Optional notes about the status update',
    example: 'Started stitching the main body of the garment',
    maxLength: 500,
  })
  @IsString()
  @IsOptional()
  @MaxLength(500)
  notes?: string;
}

export class OrderStatusHistoryDto {
  @ApiProperty({
    description: 'Unique identifier of the status history entry',
    example: '123e4567-e89b-12d3-a456-************',
    format: 'uuid',
  })
  id: string;

  @ApiProperty({
    description: 'Order ID this status update belongs to',
    example: '123e4567-e89b-12d3-a456-************',
    format: 'uuid',
  })
  orderId: string;

  @ApiProperty({
    description: 'Status that was set',
    enum: DetailedOrderStatus,
    example: DetailedOrderStatus.STITCHING_IN_PROGRESS,
  })
  status: DetailedOrderStatus;

  @ApiPropertyOptional({
    description: 'Notes about the status update',
    example: 'Started stitching the main body of the garment',
  })
  notes?: string;

  @ApiProperty({
    description: 'User who made the status update',
    example: {
      id: '123e4567-e89b-12d3-a456-************',
      firstName: 'John',
      lastName: 'Tailor',
      email: '<EMAIL>'
    },
  })
  updater: {
    id: string;
    firstName: string;
    lastName: string;
    email: string;
  };

  @ApiProperty({
    description: 'Timestamp when the status was updated',
    example: '2023-01-15T14:30:00Z',
    format: 'date-time',
  })
  timestamp: Date;
}

export class OrderTimelineStageDto {
  @ApiProperty({
    description: 'Status of this timeline stage',
    enum: DetailedOrderStatus,
    example: DetailedOrderStatus.STITCHING_IN_PROGRESS,
  })
  status: DetailedOrderStatus;

  @ApiProperty({
    description: 'Display name of the stage',
    example: 'Stitching in Progress',
  })
  name: string;

  @ApiPropertyOptional({
    description: 'Timestamp when this stage was completed',
    example: '2023-01-15T14:30:00Z',
    format: 'date-time',
  })
  timestamp?: Date;

  @ApiProperty({
    description: 'Whether this stage has been completed',
    example: true,
  })
  isCompleted: boolean;

  @ApiProperty({
    description: 'Whether this is the current stage',
    example: false,
  })
  isCurrent: boolean;
}

export class OrderTimelineDto {
  @ApiProperty({
    description: 'Order ID',
    example: '123e4567-e89b-12d3-a456-************',
    format: 'uuid',
  })
  orderId: string;

  @ApiProperty({
    description: 'Current detailed status of the order',
    enum: DetailedOrderStatus,
    example: DetailedOrderStatus.STITCHING_IN_PROGRESS,
  })
  currentStatus: DetailedOrderStatus;

  @ApiPropertyOptional({
    description: 'Estimated completion date',
    example: '2023-02-15T18:00:00Z',
    format: 'date-time',
  })
  estimatedCompletion?: Date;

  @ApiPropertyOptional({
    description: 'Actual completion date',
    example: '2023-02-14T16:30:00Z',
    format: 'date-time',
  })
  actualCompletion?: Date;

  @ApiProperty({
    description: 'Timeline stages with completion status',
    type: [OrderTimelineStageDto],
  })
  stages: OrderTimelineStageDto[];

  @ApiProperty({
    description: 'Complete status history',
    type: [OrderStatusHistoryDto],
  })
  history: OrderStatusHistoryDto[];
}

export class BulkStatusUpdateDto {
  @ApiProperty({
    description: 'Array of order IDs to update',
    example: ['123e4567-e89b-12d3-a456-************', '123e4567-e89b-12d3-a456-************'],
    type: [String],
  })
  orderIds: string[];

  @ApiProperty({
    description: 'New status to apply to all orders',
    enum: DetailedOrderStatus,
    example: DetailedOrderStatus.MATERIALS_ACQUIRED,
  })
  @IsEnum(DetailedOrderStatus)
  status: DetailedOrderStatus;

  @ApiPropertyOptional({
    description: 'Optional notes for the bulk update',
    example: 'Bulk update: Materials acquired for batch production',
    maxLength: 500,
  })
  @IsString()
  @IsOptional()
  @MaxLength(500)
  notes?: string;
}

export class StatusTransitionValidationDto {
  @ApiProperty({
    description: 'Current status of the order',
    enum: DetailedOrderStatus,
    example: DetailedOrderStatus.CUTTING_STARTED,
  })
  currentStatus: DetailedOrderStatus;

  @ApiProperty({
    description: 'Available next statuses',
    enum: DetailedOrderStatus,
    isArray: true,
    example: [DetailedOrderStatus.STITCHING_IN_PROGRESS, DetailedOrderStatus.CANCELLED],
  })
  availableTransitions: DetailedOrderStatus[];

  @ApiProperty({
    description: 'Whether the transition is valid',
    example: true,
  })
  isValid: boolean;

  @ApiPropertyOptional({
    description: 'Reason if transition is not valid',
    example: 'Cannot move to DELIVERED from CUTTING_STARTED. Must complete intermediate stages.',
  })
  reason?: string;
}
