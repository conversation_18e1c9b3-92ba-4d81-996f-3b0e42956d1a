import { ApiProperty } from '@nestjs/swagger';
import { IsDate, IsEnum, IsNumber, IsOptional, IsString, IsUUID, ValidateNested } from 'class-validator';
import { Type } from 'class-transformer';
import { OrderStatus, PaymentStatus } from '../entities/order.entity';

export class OrderItemDto {
  @ApiProperty({
    description: 'Unique identifier of the order item',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @IsUUID()
  id: string;

  @ApiProperty({
    description: 'Product ID associated with this order item',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @IsUUID()
  productId: string;

  @ApiProperty({
    description: 'Quantity of the product ordered',
    example: 2,
  })
  @IsNumber()
  quantity: number;

  @ApiProperty({
    description: 'Unit price at the time of order',
    example: 59.99,
  })
  @IsNumber()
  unitPrice: number;

  @ApiProperty({
    description: 'Total price for this item (quantity * unitPrice)',
    example: 119.98,
  })
  @IsNumber()
  totalPrice: number;
  
  @ApiProperty({
    description: 'Product name',
    example: 'Ergonomic Chair',
  })
  @IsString()
  productName: string;
  
  @ApiProperty({
    description: 'Discount amount applied to this item',
    example: 10.00,
    required: false
  })
  @IsNumber()
  @IsOptional()
  discount?: number;
}

export class OrderDto {
  @ApiProperty({
    description: 'Unique identifier of the order',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @IsUUID()
  id: string;

  @ApiProperty({
    description: 'User ID who placed the order',
    example: '123e4567-e89b-12d3-a456-************',
  })
  @IsUUID()
  userId: string;

  @ApiProperty({
    description: 'Current status of the order',
    enum: OrderStatus,
    example: OrderStatus.IN_PROGRESS,
  })
  @IsEnum(OrderStatus)
  status: OrderStatus;
  
  @ApiProperty({
    description: 'Payment status of the order',
    enum: PaymentStatus,
    example: PaymentStatus.PAID,
  })
  @IsEnum(PaymentStatus)
  paymentStatus: PaymentStatus;
  
  @ApiProperty({
    description: 'Payment method used for the order',
    example: 'CREDIT_CARD',
  })
  @IsString()
  paymentMethod: string;

  @ApiProperty({
    description: 'Total amount of the order',
    example: 159.98,
  })
  @IsNumber()
  totalAmount: number;

  @ApiProperty({
    description: 'Items included in this order',
    type: [OrderItemDto],
  })
  @ValidateNested({ each: true })
  @Type(() => OrderItemDto)
  items: OrderItemDto[];

  @ApiProperty({
    description: 'Shipping address',
    example: '123 Main St, Anytown, AN 12345',
  })
  @IsString()
  shippingAddress: string;

  @ApiProperty({
    description: 'Billing address',
    example: '123 Main St, Anytown, AN 12345',
  })
  @IsString()
  billingAddress: string;

  @ApiProperty({
    description: 'Date when the order was created',
    example: '2023-01-15T08:30:00Z',
  })
  @IsDate()
  createdAt: Date;

  @ApiProperty({
    description: 'Date when the order was last updated',
    example: '2023-02-20T10:15:00Z',
  })
  @IsDate()
  updatedAt: Date;

  @ApiProperty({
    description: 'Optional notes for the order',
    example: 'Please leave package at the door',
    required: false,
  })
  @IsString()
  @IsOptional()
  notes?: string;
}
