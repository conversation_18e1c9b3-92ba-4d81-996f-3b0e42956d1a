import { InputType, Field, PartialType, ID } from '@nestjs/graphql';
import { IsOptional, IsEnum, IsString } from 'class-validator';
import { CreateOrderDto } from './create-order.dto';
import { OrderStatus, PaymentStatus } from '../entities/order.entity';

@InputType()
export class UpdateOrderDto extends PartialType(CreateOrderDto) {
  @Field(() => OrderStatus, { nullable: true })
  @IsEnum(OrderStatus)
  @IsOptional()
  status?: OrderStatus;

  @Field(() => PaymentStatus, { nullable: true })
  @IsEnum(PaymentStatus)
  @IsOptional()
  paymentStatus?: PaymentStatus;

  @Field(() => String, { nullable: true })
  @IsString()
  @IsOptional()
  trackingNumber?: string;
}
