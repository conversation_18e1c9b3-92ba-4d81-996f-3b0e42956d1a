import { InputType, Field, ID } from '@nestjs/graphql';
import { Type } from 'class-transformer';
import { IsNotEmpty, IsString, IsNumber, IsOptional, IsEnum, IsUUID, ValidateNested, ArrayMinSize, Min } from 'class-validator';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { OrderStatus, PaymentStatus } from '../entities/order.entity';

@InputType()
export class CreateOrderItemDto {
  @ApiProperty({
    description: 'Unique identifier of the product to order',
    example: '123e4567-e89b-12d3-a456-************',
    format: 'uuid',
  })
  @Field(() => ID)
  @IsUUID()
  @IsNotEmpty({ message: 'Product ID is required' })
  productId: string;

  @ApiProperty({
    description: 'Quantity of the product to order',
    example: 2,
    minimum: 1,
    type: 'integer',
  })
  @Field(() => Number)
  @IsNumber()
  @Min(1, { message: 'Quantity must be at least 1' })
  @IsNotEmpty({ message: 'Quantity is required' })
  quantity: number;

  @ApiPropertyOptional({
    description: 'Discount amount for this item in decimal format',
    example: '5.00',
    pattern: '^\\d+(\\.\\d{1,2})?$',
  })
  @Field(() => String, { nullable: true })
  @IsOptional()
  discount?: string;
}

@InputType()
export class CreateOrderDto {
  @ApiProperty({
    description: 'Unique identifier of the customer placing the order',
    example: '123e4567-e89b-12d3-a456-************',
    format: 'uuid',
  })
  @Field(() => ID)
  @IsUUID()
  @IsNotEmpty({ message: 'Customer ID is required' })
  customerId: string;

  @ApiPropertyOptional({
    description: 'Customer name for the order',
    example: 'John Doe',
    maxLength: 255,
  })
  @Field(() => String, { nullable: true })
  @IsString()
  @IsOptional()
  customerName?: string;

  @ApiPropertyOptional({
    description: 'Customer email for order notifications',
    example: '<EMAIL>',
    format: 'email',
  })
  @Field(() => String, { nullable: true })
  @IsString()
  @IsOptional()
  customerEmail?: string;

  @ApiPropertyOptional({
    description: 'Current status of the order',
    enum: OrderStatus,
    example: OrderStatus.NEW,
    default: OrderStatus.NEW,
  })
  @Field(() => OrderStatus, { defaultValue: OrderStatus.NEW })
  @IsEnum(OrderStatus)
  @IsOptional()
  status?: OrderStatus;

  @ApiPropertyOptional({
    description: 'Payment status of the order',
    enum: PaymentStatus,
    example: PaymentStatus.PENDING,
    default: PaymentStatus.PENDING,
  })
  @Field(() => PaymentStatus, { defaultValue: PaymentStatus.PENDING })
  @IsEnum(PaymentStatus)
  @IsOptional()
  paymentStatus?: PaymentStatus;

  @ApiPropertyOptional({
    description: 'Payment method used for the order',
    example: 'credit_card',
    enum: ['credit_card', 'debit_card', 'paypal', 'bank_transfer', 'cash'],
  })
  @Field(() => String, { nullable: true })
  @IsString()
  @IsOptional()
  paymentMethod?: string;

  @ApiProperty({
    description: 'List of items in the order',
    type: [CreateOrderItemDto],
    minItems: 1,
  })
  @Field(() => [CreateOrderItemDto])
  @ValidateNested({ each: true })
  @ArrayMinSize(1, { message: 'At least one item is required' })
  @Type(() => CreateOrderItemDto)
  items: CreateOrderItemDto[];

  @ApiPropertyOptional({
    description: 'Total discount amount for the order in decimal format',
    example: '10.00',
    pattern: '^\\d+(\\.\\d{1,2})?$',
  })
  @Field(() => String, { nullable: true })
  @IsOptional()
  discount?: string;

  @ApiPropertyOptional({
    description: 'Shipping cost in decimal format',
    example: '5.99',
    pattern: '^\\d+(\\.\\d{1,2})?$',
  })
  @Field(() => String, { nullable: true })
  @IsOptional()
  shipping?: string;

  @ApiPropertyOptional({
    description: 'Additional notes or special instructions for the order',
    example: 'Please handle with care',
    maxLength: 1000,
  })
  @Field(() => String, { nullable: true })
  @IsString()
  @IsOptional()
  notes?: string;

  @ApiProperty({
    description: 'Complete shipping address for the order',
    example: '123 Main St, Apt 4B, New York, NY 10001, USA',
    maxLength: 500,
  })
  @Field(() => String)
  @IsString()
  @IsNotEmpty({ message: 'Shipping address is required' })
  shippingAddress: string;

  @ApiPropertyOptional({
    description: 'Billing address if different from shipping address',
    example: '456 Business Ave, Suite 100, New York, NY 10002, USA',
    maxLength: 500,
  })
  @Field(() => String, { nullable: true })
  @IsString()
  @IsOptional()
  billingAddress?: string;
}
