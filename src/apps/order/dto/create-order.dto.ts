import { InputType, Field, ID } from '@nestjs/graphql';
import { Type } from 'class-transformer';
import { IsNotEmpty, IsString, IsNumber, IsOptional, IsEnum, IsUUID, ValidateNested, ArrayMinSize, Min } from 'class-validator';
import { OrderStatus, PaymentStatus } from '../entities/order.entity';

@InputType()
export class CreateOrderItemDto {
  @Field(() => ID)
  @IsUUID()
  @IsNotEmpty({ message: 'Product ID is required' })
  productId: string;

  @Field(() => Number)
  @IsNumber()
  @Min(1, { message: 'Quantity must be at least 1' })
  @IsNotEmpty({ message: 'Quantity is required' })
  quantity: number;

  @Field(() => String, { nullable: true })
  @IsOptional()
  discount?: string;
}

@InputType()
export class CreateOrderDto {
  @Field(() => ID)
  @IsUUID()
  @IsNotEmpty({ message: 'Customer ID is required' })
  customerId: string;

  @Field(() => String, { nullable: true })
  @IsString()
  @IsOptional()
  customerName?: string;

  @Field(() => String, { nullable: true })
  @IsString()
  @IsOptional()
  customerEmail?: string;

  @Field(() => OrderStatus, { defaultValue: OrderStatus.NEW })
  @IsEnum(OrderStatus)
  @IsOptional()
  status?: OrderStatus;

  @Field(() => PaymentStatus, { defaultValue: PaymentStatus.PENDING })
  @IsEnum(PaymentStatus)
  @IsOptional()
  paymentStatus?: PaymentStatus;

  @Field(() => String, { nullable: true })
  @IsString()
  @IsOptional()
  paymentMethod?: string;

  @Field(() => [CreateOrderItemDto])
  @ValidateNested({ each: true })
  @ArrayMinSize(1, { message: 'At least one item is required' })
  @Type(() => CreateOrderItemDto)
  items: CreateOrderItemDto[];

  @Field(() => String, { nullable: true })
  @IsOptional()
  discount?: string;

  @Field(() => String, { nullable: true })
  @IsOptional()
  shipping?: string;

  @Field(() => String, { nullable: true })
  @IsString()
  @IsOptional()
  notes?: string;

  @Field(() => String)
  @IsString()
  @IsNotEmpty({ message: 'Shipping address is required' })
  shippingAddress: string;

  @Field(() => String, { nullable: true })
  @IsString()
  @IsOptional()
  billingAddress?: string;
}
