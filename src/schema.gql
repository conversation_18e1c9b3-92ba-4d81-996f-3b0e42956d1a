# ------------------------------------------------------
# THIS FILE WAS AUTOMATICALLY GENERATED (DO NOT MODIFY)
# ------------------------------------------------------

input CreateInvoiceDto {
  customerId: ID
  discount: String
  dueDate: Date!
  invoiceNumber: String!
  issueDate: Date!
  orderId: ID
  status: String! = "PENDING"
  subtotal: String!
  tax: String!
  total: String!
}

input CreateMeasurementDto {
  armLength: Float
  calf: Float
  chest: Float
  footSize: Float
  height: Float
  hips: Float
  legLength: Float
  measurementUnit: String = "cm"
  neck: Float
  notes: String
  shoulderWidth: Float
  thigh: Float
  userId: String!
  waist: Float
  weight: Float
}

input CreateMessageDto {
  content: String!
  orderId: ID
  receiverId: ID!
  senderId: ID!
}

input CreateOrderDto {
  billingAddress: String
  customerEmail: String
  customerId: ID!
  customerName: String
  discount: String
  items: [CreateOrderItemDto!]!
  notes: String
  paymentMethod: String
  paymentStatus: PaymentStatus! = PENDING
  shipping: String
  shippingAddress: String!
  status: OrderStatus! = NEW
}

input CreateOrderItemDto {
  discount: String
  productId: ID!
  quantity: Float!
}

input CreatePortfolioItemDto {
  category: String
  description: String
  imageUrl: String!
  tags: [String!]
  title: String!
  userId: String!
}

input CreateProductDto {
  category: String
  description: String!
  discountedPrice: String
  images: [String!]
  isActive: Boolean! = true
  name: String!
  price: String!
  quantity: Float!
  sku: String!
  tags: [String!]
}

input CreateTailorProfileDto {
  bio: String
  certificates: [String!]
  experienceYears: Int
  isVerified: Boolean = false
  specialties: [String!]
  userId: String!
}

input CreateTrainingDto {
  accessLevel: String = "free"
  contentType: String!
  contentUrl: String!
  description: String!
  duration: Int
  instructorId: String!
  price: String
  title: String!
}

input CreateTrainingEnrollmentDto {
  progress: Int = 0
  trainingId: String!
  userId: String!
}

input CreateTransactionDto {
  amount: String!
  currency: String! = "USD"
  customerId: ID
  description: String!
  invoiceId: ID
  notes: String
  orderId: ID
  paymentMethod: String
  referenceNumber: String
  status: TransactionStatus! = PENDING
  transactionDate: Date
  type: TransactionType!
}

input CreateUserDto {
  email: String!
  firstName: String!
  lastName: String!
  password: String!
  phone: String
  roles: [String!]
}

type DailyRevenue {
  amount: String!
  date: String!
}

"""Date custom scalar type"""
scalar Date

"""
A date-time string at UTC, such as 2019-12-03T09:54:33Z, compliant with the date-time format.
"""
scalar DateTime

type Invoice {
  createdAt: Date!
  customerId: ID
  discount: String
  dueDate: Date!
  id: ID!
  invoiceNumber: String!
  issueDate: Date!
  orderId: ID
  paidDate: Date
  status: String
  subtotal: String!
  tax: String!
  total: String!
  updatedAt: Date!
}

"""JSON custom scalar type"""
scalar JSON

type Measurement {
  armLength: Float
  calf: Float
  chest: Float
  createdAt: Date!
  footSize: Float
  height: Float
  hips: Float
  id: ID!
  legLength: Float
  measurementUnit: String!
  neck: Float
  notes: String
  shoulderWidth: Float
  thigh: Float
  updatedAt: Date!
  user: User
  userId: ID!
  waist: Float
  weight: Float
}

type Message {
  content: String!
  createdAt: Date!
  id: ID!
  isRead: Boolean!
  order: Order
  orderId: ID
  receiver: User!
  receiverId: ID!
  sender: User!
  senderId: ID!
  updatedAt: Date!
}

type Mutation {
  cancelOrder(id: ID!): Order!
  createInvoice(createInvoiceInput: CreateInvoiceDto!): Invoice!
  createMeasurement(createMeasurementDto: CreateMeasurementDto!): Measurement!
  createMessage(createMessageDto: CreateMessageDto!): Message!
  createOrder(createOrderInput: CreateOrderDto!): Order!
  createPortfolioItem(createPortfolioItemDto: CreatePortfolioItemDto!): PortfolioItem!
  createProduct(createProductInput: CreateProductDto!): Product!
  createTailorProfile(createTailorProfileDto: CreateTailorProfileDto!): TailorProfile!
  createTraining(createTrainingDto: CreateTrainingDto!): Training!
  createTrainingEnrollment(createTrainingEnrollmentDto: CreateTrainingEnrollmentDto!): TrainingEnrollment!
  createTransaction(createTransactionInput: CreateTransactionDto!): Transaction!
  createUser(createUserInput: CreateUserDto!): User!
  markInvoiceAsPaid(id: ID!, paymentDate: Date, paymentReference: String): Invoice!
  markMessageAsRead(id: ID!): Message!
  removeMeasurement(id: ID!): Measurement!
  removeMessage(id: ID!): Message!
  removePortfolioItem(id: ID!): PortfolioItem!
  removeProduct(id: ID!): Product!
  removeTailorProfile(id: ID!): TailorProfile!
  removeTraining(id: ID!): Training!
  removeTrainingEnrollment(id: ID!): TrainingEnrollment!
  removeUser(id: ID!): User!
  updateMeasurement(id: ID!, updateMeasurementDto: UpdateMeasurementDto!): Measurement!
  updateMessage(id: ID!, updateMessageDto: UpdateMessageDto!): Message!
  updateMyProfile(updateUserInput: UpdateUserDto!): User!
  updateOrder(id: ID!, updateOrderInput: UpdateOrderDto!): Order!
  updatePortfolioItem(id: ID!, updatePortfolioItemDto: UpdatePortfolioItemDto!): PortfolioItem!
  updateProduct(id: ID!, updateProductInput: UpdateProductDto!): Product!
  updateProductStock(id: ID!, quantity: Float!): Product!
  updateTailorProfile(id: ID!, updateTailorProfileDto: UpdateTailorProfileDto!): TailorProfile!
  updateTraining(id: ID!, updateTrainingDto: UpdateTrainingDto!): Training!
  updateTrainingEnrollment(id: ID!, updateTrainingEnrollmentDto: UpdateTrainingEnrollmentDto!): TrainingEnrollment!
  updateTransaction(id: ID!, updateTransactionInput: UpdateTransactionDto!): Transaction!
  updateTransactionStatus(id: ID!, status: String!): Transaction!
  updateUser(id: ID!, updateUserInput: UpdateUserDto!): User!
}

type Order {
  billingAddress: String
  createdAt: Date!
  customer: User
  customerEmail: String
  customerId: ID!
  customerName: String
  deliveredAt: Date
  discount: String
  id: ID!
  items: [OrderItem!]
  notes: String
  paymentMethod: String
  paymentStatus: PaymentStatus!
  shippedAt: Date
  shipping: String!
  shippingAddress: String
  status: OrderStatus!
  subtotal: String!
  tax: String!
  total: String!
  trackingNumber: String
  updatedAt: Date!
}

type OrderItem {
  createdAt: Date!
  discount: String
  id: ID!
  orderId: ID!
  product: Product
  productId: ID!
  productName: String!
  quantity: Float!
  totalPrice: String!
  unitPrice: String!
  updatedAt: Date!
}

"""The status of an order"""
enum OrderStatus {
  APPROVED
  CANCELLED
  COMPLETE
  DELIVERED
  IN_PROGRESS
  NEW
  RETURNED
}

type PaginatedMeasurementResponse {
  data: [Measurement!]!
  pagination: PaginationMetaDto!
}

type PaginatedMessageResponse {
  data: [Message!]!
  pagination: PaginationMetaDto!
}

type PaginatedPortfolioItemResponse {
  data: [PortfolioItem!]!
  pagination: PaginationMetaDto!
}

type PaginatedTailorProfileResponse {
  data: [TailorProfile!]!
  pagination: PaginationMetaDto!
}

type PaginatedTrainingEnrollmentResponse {
  data: [TrainingEnrollment!]!
  pagination: PaginationMetaDto!
}

type PaginatedTrainingResponse {
  data: [Training!]!
  pagination: PaginationMetaDto!
}

type PaginationMetaDto {
  hasNextPage: Boolean!
  hasPreviousPage: Boolean!
  limit: Int!
  page: Int!
  totalCount: Int!
  totalPages: Int!
}

"""The payment status of an order"""
enum PaymentStatus {
  FAILED
  PAID
  PENDING
  REFUNDED
}

type PortfolioItem {
  category: String
  createdAt: Date!
  description: String
  id: ID!
  imageUrl: String!
  tags: [String!]
  title: String!
  updatedAt: Date!
  user: User
  userId: ID!
}

type Product {
  category: String
  createdAt: Date!
  description: String!
  discountedPrice: String
  id: ID!
  images: [String!]
  isActive: Boolean!
  name: String!
  price: String!
  quantity: Float!
  sku: String!
  tags: [String!]
  updatedAt: Date!
}

type Query {
  conversationMessages(limit: Int, order: JSON, page: Int, search: String, userId1: ID!, userId2: ID!, where: JSON): PaginatedMessageResponse!
  invoice(id: ID!): Invoice!
  invoices: [Invoice!]!
  me: User!
  measurement(id: ID!): Measurement!
  measurements(limit: Int, order: JSON, page: Int, search: String, where: JSON): PaginatedMeasurementResponse!
  measurementsByUserId(userId: ID!): [Measurement!]!
  message(id: ID!): Message!
  messages(limit: Int, order: JSON, page: Int, search: String, where: JSON): PaginatedMessageResponse!
  messagesByOrderId(limit: Int, order: JSON, orderId: ID!, page: Int, search: String, where: JSON): PaginatedMessageResponse!
  myOrders: [Order!]!
  order(id: ID!): Order!
  orders: [Order!]!
  ordersByCustomer(customerId: ID!): [Order!]!
  portfolioItem(id: ID!): PortfolioItem!
  portfolioItems(limit: Int, order: JSON, page: Int, search: String, where: JSON): PaginatedPortfolioItemResponse!
  portfolioItemsByUserId(limit: Int, order: JSON, page: Int, search: String, userId: ID!, where: JSON): PaginatedPortfolioItemResponse!
  product(id: ID!): Product!
  products: [Product!]!
  productsByCategory(category: String!): [Product!]!
  revenueReport(endDate: Date!, startDate: Date!): RevenueReport!
  tailorProfile(id: ID!): TailorProfile!
  tailorProfileByUserId(userId: ID!): TailorProfile!
  tailorProfiles(limit: Int, order: JSON, page: Int, search: String, where: JSON): PaginatedTailorProfileResponse!
  training(id: ID!): Training!
  trainingEnrollment(id: ID!): TrainingEnrollment!
  trainingEnrollments(limit: Int, order: JSON, page: Int, search: String, where: JSON): PaginatedTrainingEnrollmentResponse!
  trainingEnrollmentsByTrainingId(limit: Int, order: JSON, page: Int, search: String, trainingId: ID!, where: JSON): PaginatedTrainingEnrollmentResponse!
  trainingEnrollmentsByUserId(limit: Int, order: JSON, page: Int, search: String, userId: ID!, where: JSON): PaginatedTrainingEnrollmentResponse!
  trainings(limit: Int, order: JSON, page: Int, search: String, where: JSON): PaginatedTrainingResponse!
  trainingsByInstructorId(instructorId: ID!, limit: Int, order: JSON, page: Int, search: String, where: JSON): PaginatedTrainingResponse!
  transaction(id: ID!): Transaction!
  transactions: [Transaction!]!
  user(id: ID!): User!
  userMessages(limit: Int, order: JSON, page: Int, search: String, userId: ID!, where: JSON): PaginatedMessageResponse!
  users: [User!]!
}

type RevenueReport {
  endDate: DateTime!
  revenueByDay: [DailyRevenue!]!
  startDate: DateTime!
  totalRevenue: String!
  transactionCount: Float!
}

type TailorProfile {
  bio: String
  certificates: [String!]
  createdAt: Date!
  experienceYears: Int
  id: ID!
  isVerified: Boolean!
  rating: Float
  reviewCount: Int
  specialties: [String!]
  updatedAt: Date!
  user: User
  userId: ID!
}

type Training {
  accessLevel: String!
  contentType: String!
  contentUrl: String!
  createdAt: Date!
  description: String!
  duration: Int
  id: ID!
  instructor: User
  instructorId: ID!
  price: String
  title: String!
  updatedAt: Date!
}

type TrainingEnrollment {
  completedAt: Date
  enrolledAt: Date!
  id: ID!
  progress: Int!
  training: Training
  trainingId: ID!
  user: User
  userId: ID!
}

type Transaction {
  amount: String!
  createdAt: Date!
  currency: String
  customerId: ID
  description: String!
  id: ID!
  invoiceId: ID
  notes: String
  orderId: ID
  paymentMethod: String
  referenceNumber: String
  status: TransactionStatus!
  transactionDate: Date!
  type: TransactionType!
  updatedAt: Date!
}

"""Status of financial transactions"""
enum TransactionStatus {
  COMPLETED
  FAILED
  PENDING
  VOIDED
}

"""Types of financial transactions"""
enum TransactionType {
  EXPENSE
  OTHER
  PAYROLL
  PURCHASE
  REFUND
  SALE
  TAX
}

input UpdateMeasurementDto {
  armLength: Float
  calf: Float
  chest: Float
  footSize: Float
  height: Float
  hips: Float
  legLength: Float
  measurementUnit: String
  neck: Float
  notes: String
  shoulderWidth: Float
  thigh: Float
  userId: String
  waist: Float
  weight: Float
}

input UpdateMessageDto {
  content: String
  isRead: Boolean
  orderId: ID
  receiverId: ID
  senderId: ID
}

input UpdateOrderDto {
  billingAddress: String
  customerEmail: String
  customerId: ID
  customerName: String
  discount: String
  items: [CreateOrderItemDto!]
  notes: String
  paymentMethod: String
  paymentStatus: PaymentStatus
  shipping: String
  shippingAddress: String
  status: OrderStatus
  trackingNumber: String
}

input UpdatePortfolioItemDto {
  category: String
  description: String
  imageUrl: String
  tags: [String!]
  title: String
  userId: String
}

input UpdateProductDto {
  category: String
  description: String
  discountedPrice: String
  images: [String!]
  isActive: Boolean = true
  name: String
  price: String
  quantity: Float
  sku: String
  tags: [String!]
}

input UpdateTailorProfileDto {
  bio: String
  certificates: [String!]
  experienceYears: Int
  isVerified: Boolean
  specialties: [String!]
  userId: String
}

input UpdateTrainingDto {
  accessLevel: String
  contentType: String
  contentUrl: String
  description: String
  duration: Int
  instructorId: String
  price: String
  title: String
}

input UpdateTrainingEnrollmentDto {
  completedAt: Date
  progress: Int
  trainingId: String
  userId: String
}

input UpdateTransactionDto {
  amount: String
  currency: String = "USD"
  customerId: ID
  description: String
  invoiceId: ID
  notes: String
  orderId: ID
  paymentMethod: String
  referenceNumber: String
  status: TransactionStatus = PENDING
  transactionDate: Date
  type: TransactionType
}

input UpdateUserDto {
  email: String
  firstName: String
  lastName: String
  password: String
  phone: String
  roles: [String!]
}

type User {
  createdAt: Date!
  email: String!
  firstName: String!
  id: ID!
  lastName: String!
  phone: String
  roles: [String!]!
  updatedAt: Date!
}