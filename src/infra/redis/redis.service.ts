import { Injectable, On<PERSON><PERSON>ule<PERSON><PERSON>roy, OnModuleInit, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import Redis from 'ioredis';

@Injectable()
export class RedisService implements OnModuleInit, OnModuleDestroy {
  private redisClient: Redis;
  private readonly logger = new Logger(RedisService.name);

  constructor(private configService: ConfigService) {}

  async onModuleInit() {
    const redisUrl = this.configService.get<string>('REDIS_URL');
    
    if (!redisUrl) {
      this.logger.warn('REDIS_URL environment variable is not set. Using default Redis connection.');
      this.redisClient = new Redis(); // Connect to default localhost:6379
    } else {
      this.redisClient = new Redis(redisUrl);
    }
    
    this.redisClient.on('error', (error) => {
      this.logger.error(`Redis connection error: ${error.message}`, error.stack);
    });
    
    this.redisClient.on('connect', () => {
      this.logger.log('Successfully connected to Redis');
    });
  }

  async onModuleDestroy() {
    if (this.redisClient) {
      await this.redisClient.quit();
      this.logger.log('Redis connection closed');
    }
  }

  getClient(): Redis {
    return this.redisClient;
  }

  async set(key: string, value: any, ttl?: number): Promise<'OK'> {
    const stringValue = typeof value === 'object' ? JSON.stringify(value) : String(value);
    
    if (ttl) {
      return this.redisClient.set(key, stringValue, 'EX', ttl);
    }
    
    return this.redisClient.set(key, stringValue);
  }

  async get(key: string): Promise<any> {
    const value = await this.redisClient.get(key);
    
    if (value === null) {
      return null;
    }
    
    try {
      return JSON.parse(value);
    } catch (e) {
      return value;
    }
  }

  async del(key: string): Promise<number> {
    return this.redisClient.del(key);
  }

  async exists(key: string): Promise<number> {
    return this.redisClient.exists(key);
  }

  async ttl(key: string): Promise<number> {
    return this.redisClient.ttl(key);
  }

  async expire(key: string, seconds: number): Promise<number> {
    return this.redisClient.expire(key, seconds);
  }

  /**
   * Increment the value of a key by 1
   * @param key The key to increment
   * @returns The incremented value
   */
  async increment(key: string): Promise<number> {
    return this.redisClient.incr(key);
  }
  
  /**
   * Increment the value of a key by a specific amount
   * @param key The key to increment
   * @param amount The amount to increment by
   * @returns The incremented value
   */
  async incrBy(key: string, amount: number): Promise<number> {
    return this.redisClient.incrby(key, amount);
  }
}
