import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { FirebaseAuthService } from './firebase-auth.service';
import { FirebaseMessagingService } from './firebase-messaging.service';
import { FirebaseStorageService } from './firebase-storage.service';
import { FirebaseAdminService } from './firebase-admin.service';

@Module({
  imports: [ConfigModule],
  providers: [
    FirebaseAdminService,
    FirebaseAuthService,
    FirebaseMessagingService,
    FirebaseStorageService,
  ],
  exports: [
    FirebaseAdminService,
    FirebaseAuthService,
    FirebaseMessagingService,
    FirebaseStorageService,
  ],
})
export class FirebaseModule {}
