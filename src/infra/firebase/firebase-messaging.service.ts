import { Injectable, Logger } from '@nestjs/common';
import { FirebaseAdminService } from './firebase-admin.service';
import * as admin from 'firebase-admin';

export interface NotificationPayload {
  title: string;
  body: string;
  imageUrl?: string;
  icon?: string;
  clickAction?: string;
  data?: Record<string, string>;
}

@Injectable()
export class FirebaseMessagingService {
  private readonly logger = new Logger(FirebaseMessagingService.name);

  constructor(private readonly firebaseAdminService: FirebaseAdminService) {}

  /**
   * Send notification to a specific device
   * 
   * @param token FCM device token
   * @param notification Notification payload
   * @returns Message ID if successful
   */
  async sendToDevice(token: string, notification: NotificationPayload): Promise<string> {
    try {
      const messaging = this.firebaseAdminService.getMessaging();
      
      const message: admin.messaging.Message = {
        token,
        notification: {
          title: notification.title,
          body: notification.body,
          imageUrl: notification.imageUrl,
        },
        data: notification.data,
        android: {
          notification: {
            icon: notification.icon || 'ic_notification',
            clickAction: notification.clickAction,
          },
        },
        apns: {
          payload: {
            aps: {
              badge: 1,
              sound: 'default',
            },
          },
        },
      };

      const response = await messaging.send(message);
      this.logger.log(`Successfully sent notification to device: ${token.substring(0, 10)}...`);
      return response;
    } catch (error) {
      this.logger.error(`Failed to send notification to device: ${error.message}`, error.stack);
      if (error.code === 'messaging/invalid-registration-token' || 
          error.code === 'messaging/registration-token-not-registered') {
        throw new Error(`Invalid FCM token: ${token.substring(0, 10)}...`);
      }
      throw error;
    }
  }

  /**
   * Send notification to multiple devices
   * 
   * @param tokens Array of FCM device tokens
   * @param notification Notification payload
   * @returns Object containing successful and failed counts
   */
  async sendToDevices(tokens: string[], notification: NotificationPayload): Promise<{
    successCount: number;
    failureCount: number;
    failedTokens: string[];
  }> {
    try {
      if (!tokens || tokens.length === 0) {
        return { successCount: 0, failureCount: 0, failedTokens: [] };
      }

      const messaging = this.firebaseAdminService.getMessaging();
      
      // Create a standard message format
      const baseMessage = {
        notification: {
          title: notification.title,
          body: notification.body,
          imageUrl: notification.imageUrl,
        },
        data: notification.data,
        android: {
          notification: {
            icon: notification.icon || 'ic_notification',
            clickAction: notification.clickAction,
          },
        },
        apns: {
          payload: {
            aps: {
              badge: 1,
              sound: 'default',
            },
          },
        },
      };
      
      // Use send in batch approach as fallback for sendMulticast
      const sendPromises = tokens.map(token => 
        messaging.send({
          ...baseMessage,
          token,
        } as admin.messaging.Message).catch(error => {
          this.logger.warn(`Failed to send to token ${token.substring(0, 10)}...: ${error.message}`);
          return { success: false, error };
        })
      );
      
      const results = await Promise.all(sendPromises);
      
      // Process results
      const failedTokens: string[] = [];
      let successCount = 0;
      
      results.forEach((result, index) => {
        if (typeof result === 'object' && result && result.success === false) {
          failedTokens.push(tokens[index]);
        } else {
          successCount++;
        }
      });
      
      const failureCount = tokens.length - successCount;
      
      this.logger.log(`Sent notifications to ${successCount} devices with ${failureCount} failures`);
      
      return {
        successCount,
        failureCount,
        failedTokens,
      };
    } catch (error) {
      this.logger.error(`Failed to send multicast notification: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Send notification to a topic (subscribers)
   * 
   * @param topic Topic name
   * @param notification Notification payload
   * @returns Message ID if successful
   */
  async sendToTopic(topic: string, notification: NotificationPayload): Promise<string> {
    try {
      const messaging = this.firebaseAdminService.getMessaging();
      
      const message: admin.messaging.Message = {
        topic,
        notification: {
          title: notification.title,
          body: notification.body,
          imageUrl: notification.imageUrl,
        },
        data: notification.data,
        android: {
          notification: {
            icon: notification.icon || 'ic_notification',
            clickAction: notification.clickAction,
          },
        },
        apns: {
          payload: {
            aps: {
              badge: 1,
              sound: 'default',
            },
          },
        },
      };

      const response = await messaging.send(message);
      this.logger.log(`Successfully sent notification to topic: ${topic}`);
      return response;
    } catch (error) {
      this.logger.error(`Failed to send notification to topic: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Subscribe devices to a topic
   * 
   * @param tokens Array of FCM device tokens
   * @param topic Topic name
   * @returns Object containing success and error counts
   */
  async subscribeToTopic(tokens: string[], topic: string): Promise<{
    successCount: number;
    failureCount: number;
  }> {
    try {
      const messaging = this.firebaseAdminService.getMessaging();
      const response = await messaging.subscribeToTopic(tokens, topic);
      
      this.logger.log(`Subscribed ${response.successCount} devices to topic: ${topic}`);
      
      return {
        successCount: response.successCount,
        failureCount: response.failureCount,
      };
    } catch (error) {
      this.logger.error(`Failed to subscribe to topic: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Unsubscribe devices from a topic
   * 
   * @param tokens Array of FCM device tokens
   * @param topic Topic name
   * @returns Object containing success and error counts
   */
  async unsubscribeFromTopic(tokens: string[], topic: string): Promise<{
    successCount: number;
    failureCount: number;
  }> {
    try {
      const messaging = this.firebaseAdminService.getMessaging();
      const response = await messaging.unsubscribeFromTopic(tokens, topic);
      
      this.logger.log(`Unsubscribed ${response.successCount} devices from topic: ${topic}`);
      
      return {
        successCount: response.successCount,
        failureCount: response.failureCount,
      };
    } catch (error) {
      this.logger.error(`Failed to unsubscribe from topic: ${error.message}`, error.stack);
      throw error;
    }
  }
}
