import { Injectable, Logger, OnModuleInit } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import * as admin from 'firebase-admin';

@Injectable()
export class FirebaseAdminService implements OnModuleInit {
  private readonly logger = new Logger(FirebaseAdminService.name);
  private firebaseApp: admin.app.App;

  constructor(private readonly configService: ConfigService) {}

  onModuleInit() {
    try {
      // Initialize Firebase Admin SDK
      const serviceAccountPath = this.configService.get<string>('FIREBASE_SERVICE_ACCOUNT_PATH');
      const projectId = this.configService.get<string>('FIREBASE_PROJECT_ID');
      
      if (!projectId) {
        this.logger.warn('Firebase Project ID is not set. Firebase services will not be available.');
        return; // Don't initialize if projectId is missing
      }

      if (serviceAccountPath) {
        // If a path to service account file is provided
        const serviceAccount = require(serviceAccountPath);
        this.initializeApp(serviceAccount, projectId);
      } else {
        // Using environment variables for credentials
        this.initializeWithEnvCredentials(projectId);
      }
      
      this.logger.log('Firebase Admin SDK initialized successfully');
    } catch (error) {
      this.logger.error(`Failed to initialize Firebase Admin SDK: ${error.message}`, error.stack);
      // Don't throw error - allow application to start without Firebase if credentials are missing
    }
  }

  private initializeApp(serviceAccount: any, projectId: string) {
    this.firebaseApp = admin.initializeApp({
      credential: admin.credential.cert(serviceAccount),
      projectId,
      storageBucket: `${projectId}.appspot.com`,
    });
  }

  private initializeWithEnvCredentials(projectId: string) {
    const privateKey = this.configService.get<string>('FIREBASE_PRIVATE_KEY')
      ?.replace(/\\n/g, '\n');
      
    if (!privateKey) {
      throw new Error('Firebase private key is missing');
    }

    const serviceAccount = {
      projectId,
      clientEmail: this.configService.get<string>('FIREBASE_CLIENT_EMAIL'),
      privateKey,
    };

    this.initializeApp(serviceAccount, projectId);
  }

  getAuth(): admin.auth.Auth {
    if (!this.firebaseApp) {
      throw new Error('Firebase Admin SDK has not been initialized');
    }
    return this.firebaseApp.auth();
  }

  getMessaging(): admin.messaging.Messaging {
    if (!this.firebaseApp) {
      throw new Error('Firebase Admin SDK has not been initialized');
    }
    return this.firebaseApp.messaging();
  }

  getStorage(): admin.storage.Storage {
    if (!this.firebaseApp) {
      throw new Error('Firebase Admin SDK has not been initialized');
    }
    return this.firebaseApp.storage();
  }

  getFirestore(): admin.firestore.Firestore {
    if (!this.firebaseApp) {
      throw new Error('Firebase Admin SDK has not been initialized');
    }
    return this.firebaseApp.firestore();
  }
}
