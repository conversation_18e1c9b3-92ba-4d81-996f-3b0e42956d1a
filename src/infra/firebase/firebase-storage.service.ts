import { Injectable, Logger } from '@nestjs/common';
import { FirebaseAdminService } from './firebase-admin.service';
import * as admin from 'firebase-admin';
import { Readable } from 'stream';

@Injectable()
export class FirebaseStorageService {
  private readonly logger = new Logger(FirebaseStorageService.name);

  constructor(private readonly firebaseAdminService: FirebaseAdminService) {}

  /**
   * Upload file to Firebase Storage
   * 
   * @param filePath Path where the file will be stored in Firebase Storage
   * @param fileBuffer File content as Buffer
   * @param metadata File metadata
   * @returns URL to the uploaded file
   */
  async uploadFile(
    filePath: string,
    fileBuffer: Buffer,
    metadata: { 
      contentType: string;
      metadata?: Record<string, string>;
    },
  ): Promise<string> {
    try {
      const storage = this.firebaseAdminService.getStorage();
      const bucket = storage.bucket();
      const file = bucket.file(filePath);

      // Upload file
      await file.save(fileBuffer, {
        metadata: {
          contentType: metadata.contentType,
          metadata: metadata.metadata,
        },
      });

      // Make file publicly accessible
      await file.makePublic();

      // Get public URL
      const publicUrl = `https://storage.googleapis.com/${bucket.name}/${filePath}`;
      
      this.logger.log(`Successfully uploaded file to: ${filePath}`);
      return publicUrl;
    } catch (error) {
      this.logger.error(`Failed to upload file: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Upload file from Stream to Firebase Storage
   * 
   * @param filePath Path where the file will be stored in Firebase Storage
   * @param fileStream Readable stream containing the file
   * @param metadata File metadata
   * @returns URL to the uploaded file
   */
  async uploadFileFromStream(
    filePath: string,
    fileStream: Readable,
    metadata: { 
      contentType: string;
      metadata?: Record<string, string>;
    },
  ): Promise<string> {
    return new Promise((resolve, reject) => {
      try {
        const storage = this.firebaseAdminService.getStorage();
        const bucket = storage.bucket();
        const file = bucket.file(filePath);

        const writeStream = file.createWriteStream({
          metadata: {
            contentType: metadata.contentType,
            metadata: metadata.metadata,
          },
        });

        fileStream
          .pipe(writeStream)
          .on('error', (error) => {
            this.logger.error(`Error uploading file stream: ${error.message}`, error.stack);
            reject(error);
          })
          .on('finish', async () => {
            try {
              // Make file publicly accessible
              await file.makePublic();

              // Get public URL
              const publicUrl = `https://storage.googleapis.com/${bucket.name}/${filePath}`;
              
              this.logger.log(`Successfully uploaded file stream to: ${filePath}`);
              resolve(publicUrl);
            } catch (error) {
              this.logger.error(`Error making file public: ${error.message}`, error.stack);
              reject(error);
            }
          });
      } catch (error) {
        this.logger.error(`Failed to setup stream upload: ${error.message}`, error.stack);
        reject(error);
      }
    });
  }

  /**
   * Download file from Firebase Storage
   * 
   * @param filePath Path to the file in Firebase Storage
   * @returns File contents as Buffer
   */
  async downloadFile(filePath: string): Promise<Buffer> {
    try {
      const storage = this.firebaseAdminService.getStorage();
      const bucket = storage.bucket();
      const file = bucket.file(filePath);

      // Check if file exists
      const [exists] = await file.exists();
      if (!exists) {
        throw new Error(`File ${filePath} does not exist`);
      }

      // Download the file
      const [fileContents] = await file.download();
      
      this.logger.log(`Successfully downloaded file: ${filePath}`);
      return fileContents;
    } catch (error) {
      this.logger.error(`Failed to download file: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Get file metadata from Firebase Storage
   * 
   * @param filePath Path to the file in Firebase Storage
   * @returns File metadata
   */
  async getFileMetadata(filePath: string): Promise<Record<string, any>> {
    try {
      const storage = this.firebaseAdminService.getStorage();
      const bucket = storage.bucket();
      const file = bucket.file(filePath);

      // Check if file exists
      const [exists] = await file.exists();
      if (!exists) {
        throw new Error(`File ${filePath} does not exist`);
      }

      // Get metadata
      const [metadata] = await file.getMetadata();
      
      this.logger.log(`Successfully retrieved metadata for file: ${filePath}`);
      return metadata;
    } catch (error) {
      this.logger.error(`Failed to get file metadata: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Delete file from Firebase Storage
   * 
   * @param filePath Path to the file in Firebase Storage
   * @returns Void
   */
  async deleteFile(filePath: string): Promise<void> {
    try {
      const storage = this.firebaseAdminService.getStorage();
      const bucket = storage.bucket();
      const file = bucket.file(filePath);

      // Check if file exists
      const [exists] = await file.exists();
      if (!exists) {
        throw new Error(`File ${filePath} does not exist`);
      }

      // Delete file
      await file.delete();
      
      this.logger.log(`Successfully deleted file: ${filePath}`);
    } catch (error) {
      this.logger.error(`Failed to delete file: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Generate signed URL for temporary access to a file
   * 
   * @param filePath Path to the file in Firebase Storage
   * @param expiresIn URL expiration time in seconds (default: 15 minutes)
   * @returns Signed URL
   */
  async generateSignedUrl(filePath: string, expiresIn = 15 * 60): Promise<string> {
    try {
      const storage = this.firebaseAdminService.getStorage();
      const bucket = storage.bucket();
      const file = bucket.file(filePath);

      // Check if file exists
      const [exists] = await file.exists();
      if (!exists) {
        throw new Error(`File ${filePath} does not exist`);
      }

      // Generate signed URL
      const [url] = await file.getSignedUrl({
        action: 'read',
        expires: Date.now() + expiresIn * 1000,
      });
      
      this.logger.log(`Generated signed URL for file: ${filePath}`);
      return url;
    } catch (error) {
      this.logger.error(`Failed to generate signed URL: ${error.message}`, error.stack);
      throw error;
    }
  }
}
