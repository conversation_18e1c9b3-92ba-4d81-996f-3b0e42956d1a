import { Injectable, Logger } from '@nestjs/common';
import { FirebaseAdminService } from './firebase-admin.service';
import * as admin from 'firebase-admin';

@Injectable()
export class FirebaseAuthService {
  private readonly logger = new Logger(FirebaseAuthService.name);

  constructor(private readonly firebaseAdminService: FirebaseAdminService) {}

  /**
   * Verify Firebase ID token
   * 
   * @param idToken Firebase ID token from client
   * @returns Decoded token with user information
   */
  async verifyIdToken(idToken: string): Promise<admin.auth.DecodedIdToken> {
    try {
      const auth = this.firebaseAdminService.getAuth();
      const decodedToken = await auth.verifyIdToken(idToken);
      
      this.logger.log(`Successfully verified token for user: ${decodedToken.uid}`);
      return decodedToken;
    } catch (error) {
      this.logger.error(`Failed to verify ID token: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Get Firebase user by UID
   * 
   * @param uid Firebase user ID
   * @returns User record
   */
  async getUserById(uid: string): Promise<admin.auth.UserRecord> {
    try {
      const auth = this.firebaseAdminService.getAuth();
      const userRecord = await auth.getUser(uid);
      
      this.logger.log(`Successfully retrieved user: ${uid}`);
      return userRecord;
    } catch (error) {
      this.logger.error(`Failed to get user by ID: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Get Firebase user by email
   * 
   * @param email User email
   * @returns User record
   */
  async getUserByEmail(email: string): Promise<admin.auth.UserRecord> {
    try {
      const auth = this.firebaseAdminService.getAuth();
      const userRecord = await auth.getUserByEmail(email);
      
      this.logger.log(`Successfully retrieved user by email: ${email}`);
      return userRecord;
    } catch (error) {
      this.logger.error(`Failed to get user by email: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Create a new Firebase user
   * 
   * @param userData User data
   * @returns Created user record
   */
  async createUser(userData: {
    email: string;
    password?: string;
    displayName?: string;
    phoneNumber?: string;
    photoURL?: string;
    emailVerified?: boolean;
  }): Promise<admin.auth.UserRecord> {
    try {
      const auth = this.firebaseAdminService.getAuth();
      const userRecord = await auth.createUser(userData);
      
      this.logger.log(`Successfully created new user: ${userRecord.uid}`);
      return userRecord;
    } catch (error) {
      this.logger.error(`Failed to create user: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Update an existing Firebase user
   * 
   * @param uid User ID
   * @param userData User data to update
   * @returns Updated user record
   */
  async updateUser(uid: string, userData: {
    email?: string;
    password?: string;
    displayName?: string;
    phoneNumber?: string;
    photoURL?: string;
    emailVerified?: boolean;
    disabled?: boolean;
  }): Promise<admin.auth.UserRecord> {
    try {
      const auth = this.firebaseAdminService.getAuth();
      const userRecord = await auth.updateUser(uid, userData);
      
      this.logger.log(`Successfully updated user: ${uid}`);
      return userRecord;
    } catch (error) {
      this.logger.error(`Failed to update user: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Delete a Firebase user
   * 
   * @param uid User ID
   * @returns Void
   */
  async deleteUser(uid: string): Promise<void> {
    try {
      const auth = this.firebaseAdminService.getAuth();
      await auth.deleteUser(uid);
      
      this.logger.log(`Successfully deleted user: ${uid}`);
    } catch (error) {
      this.logger.error(`Failed to delete user: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Set custom claims for a user (e.g., for role-based access control)
   * 
   * @param uid User ID
   * @param claims Custom claims object
   * @returns Void
   */
  async setCustomUserClaims(uid: string, claims: Record<string, any>): Promise<void> {
    try {
      const auth = this.firebaseAdminService.getAuth();
      await auth.setCustomUserClaims(uid, claims);
      
      this.logger.log(`Successfully set custom claims for user: ${uid}`);
    } catch (error) {
      this.logger.error(`Failed to set custom claims: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Create a custom token for server-to-client authentication
   * 
   * @param uid User ID
   * @param claims Additional claims
   * @returns Custom token
   */
  async createCustomToken(uid: string, claims?: Record<string, any>): Promise<string> {
    try {
      const auth = this.firebaseAdminService.getAuth();
      const token = await auth.createCustomToken(uid, claims);
      
      this.logger.log(`Successfully created custom token for user: ${uid}`);
      return token;
    } catch (error) {
      this.logger.error(`Failed to create custom token: ${error.message}`, error.stack);
      throw error;
    }
  }
}
