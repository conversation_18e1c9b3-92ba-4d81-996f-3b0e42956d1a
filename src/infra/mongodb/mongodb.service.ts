import { Injectable, OnModuleInit, OnModuleDestroy } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { InjectConnection } from '@nestjs/mongoose';
import { Connection } from 'mongoose';
import { LoggerService } from '../../core/logger/logger.service';

@Injectable()
export class MongoDbService implements OnModuleInit, OnModuleDestroy {
  constructor(
    @InjectConnection() private readonly connection: Connection,
    private readonly configService: ConfigService,
    private readonly loggerService: LoggerService,
  ) {
    this.loggerService.setContext('MongoDbService');
  }

  async onModuleInit() {
    this.loggerService.log('MongoDB service initialized');
  }

  async onModuleDestroy() {
    this.loggerService.log('MongoDB service destroyed');
  }

  /**
   * Get a MongoDB collection by name
   */
  getCollection(name: string) {
    return this.connection.collection(name);
  }

  /**
   * Find documents in a collection
   */
  async find(collection: string, query: any = {}, options: any = {}) {
    try {
      const result = await this.getCollection(collection).find(query, options).toArray();
      return result;
    } catch (error) {
      this.loggerService.error(`Error finding documents in ${collection}: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Find one document in a collection
   */
  async findOne(collection: string, query: any = {}, options: any = {}) {
    try {
      return await this.getCollection(collection).findOne(query, options);
    } catch (error) {
      this.loggerService.error(`Error finding document in ${collection}: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Count documents in a collection
   */
  async count(collection: string, query: any = {}) {
    try {
      return await this.getCollection(collection).countDocuments(query);
    } catch (error) {
      this.loggerService.error(`Error counting documents in ${collection}: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Perform an aggregation pipeline
   */
  async aggregate(collection: string, pipeline: any[]) {
    try {
      return await this.getCollection(collection).aggregate(pipeline).toArray();
    } catch (error) {
      this.loggerService.error(`Error in aggregation on ${collection}: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Sync a document from PostgreSQL to MongoDB
   * This is used by the data sync service
   */
  async syncDocument(collection: string, document: any, idField: string = 'id') {
    try {
      const filter = { [idField]: document[idField] };
      
      const result = await this.getCollection(collection).updateOne(
        filter,
        { $set: document },
        { upsert: true }
      );
      
      this.loggerService.log(`Synced document to ${collection}: ${document[idField]}`);
      return result;
    } catch (error) {
      this.loggerService.error(`Error syncing document to ${collection}: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Delete a document from MongoDB
   * This is used by the data sync service when a document is deleted from PostgreSQL
   */
  async deleteDocument(collection: string, documentId: string | number, idField: string = 'id') {
    try {
      const filter = { [idField]: documentId };
      
      const result = await this.getCollection(collection).deleteOne(filter);
      
      this.loggerService.log(`Deleted document from ${collection}: ${documentId}`);
      return result;
    } catch (error) {
      this.loggerService.error(`Error deleting document from ${collection}: ${error.message}`, error.stack);
      throw error;
    }
  }
}
