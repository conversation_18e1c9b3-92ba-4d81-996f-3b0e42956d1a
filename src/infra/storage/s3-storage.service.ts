import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { 
  S3Client, 
  PutObjectCommand, 
  GetObjectCommand, 
  DeleteObjectCommand, 
  HeadObjectCommand, 
  ListObjectsV2Command,
  ObjectCannedACL
} from '@aws-sdk/client-s3';
import { getSignedUrl } from '@aws-sdk/s3-request-presigner';
import { StorageFile, StorageUploadOptions } from './storage.service';

@Injectable()
export class S3StorageService {
  private readonly logger = new Logger(S3StorageService.name);
  private s3Client: S3Client;
  private bucket: string;
  private region: string;
  private endpoint?: string;
  private baseUrl: string;

  constructor(private readonly configService: ConfigService) {
    this.region = this.configService.get<string>('STORAGE_REGION') || 'us-east-1';
    this.bucket = this.configService.get<string>('STORAGE_BUCKET') || '';
    this.endpoint = this.configService.get<string>('STORAGE_ENDPOINT') || undefined;
    
    if (!this.bucket) {
      this.logger.warn('Storage bucket name not configured');
    }

    const clientOptions: any = {
      region: this.region,
      credentials: {
        accessKeyId: this.configService.get<string>('STORAGE_ACCESS_KEY_ID') || '',
        secretAccessKey: this.configService.get<string>('STORAGE_SECRET_ACCESS_KEY') || '',
      },
    };

    // Support for S3-compatible services (DigitalOcean Spaces, MinIO, etc.)
    if (this.endpoint) {
      clientOptions.endpoint = this.endpoint;
      clientOptions.forcePathStyle = this.configService.get<boolean>('STORAGE_FORCE_PATH_STYLE') || false;
      
      // For DigitalOcean Spaces
      if (this.endpoint.includes('digitaloceanspaces')) {
        this.baseUrl = `https://${this.bucket}.${this.region}.digitaloceanspaces.com`;
      } else {
        // For other S3 compatible services or custom endpoints
        this.baseUrl = `${this.endpoint}/${this.bucket}`;
      }
    } else {
      // AWS S3 standard URL format
      this.baseUrl = `https://${this.bucket}.s3.${this.region}.amazonaws.com`;
    }

    this.s3Client = new S3Client(clientOptions);
    
    this.logger.log(`Initialized S3StorageService with bucket: ${this.bucket}, region: ${this.region}`);
    if (this.endpoint) {
      this.logger.log(`Using custom endpoint: ${this.endpoint}`);
    }
  }

  /**
   * Upload a file to S3 storage
   * 
   * @param file The file to upload
   * @param key The key (path) for the file in S3
   * @param options Upload options
   * @returns URL to the uploaded file
   */
  async upload(file: StorageFile, key: string, options?: StorageUploadOptions): Promise<string> {
    try {
      const contentType = options?.contentType || file.mimetype;
      const isPublic = options?.isPublic !== undefined ? options.isPublic : true;
      const filePath = options?.path ? `${options.path}/${key}` : key;
      
      const params = {
        Bucket: this.bucket,
        Key: filePath,
        Body: file.buffer,
        ContentType: contentType,
        Metadata: options?.metadata || {},
        ACL: isPublic ? ObjectCannedACL.public_read : ObjectCannedACL.private,
      };

      const command = new PutObjectCommand(params);
      const response = await this.s3Client.send(command);
      
      this.logger.log(`Successfully uploaded file to ${filePath}, ETag: ${response.ETag}`);
      
      return isPublic ? `${this.baseUrl}/${filePath}` : filePath;
    } catch (error) {
      this.logger.error(`Error uploading file to S3: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Generate a presigned URL for direct client uploads
   * 
   * @param key The key (path) for the file in S3
   * @param contentType The content type of the file
   * @param expiresIn Expiration time in seconds
   * @returns Object containing the presigned URL
   */
  async getPresignedUploadUrl(key: string, contentType: string, expiresIn = 15 * 60): Promise<{ url: string; fields?: Record<string, string> }> {
    try {
      const command = new PutObjectCommand({
        Bucket: this.bucket,
        Key: key,
        ContentType: contentType,
      });

      const url = await getSignedUrl(this.s3Client, command, { expiresIn });
      
      this.logger.log(`Generated presigned upload URL for ${key}`);
      
      return { url };
    } catch (error) {
      this.logger.error(`Error generating presigned upload URL: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Generate a presigned URL for downloading a file
   * 
   * @param key The key (path) of the file in S3
   * @param expiresIn Expiration time in seconds
   * @returns Presigned URL
   */
  async getPresignedDownloadUrl(key: string, expiresIn = 15 * 60): Promise<string> {
    try {
      const command = new GetObjectCommand({
        Bucket: this.bucket,
        Key: key,
      });

      const url = await getSignedUrl(this.s3Client, command, { expiresIn });
      
      this.logger.log(`Generated presigned download URL for ${key}`);
      
      return url;
    } catch (error) {
      this.logger.error(`Error generating presigned download URL: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Download a file from S3 storage
   * 
   * @param key The key (path) of the file in S3
   * @returns File contents as Buffer
   */
  async download(key: string): Promise<Buffer> {
    try {
      const command = new GetObjectCommand({
        Bucket: this.bucket,
        Key: key,
      });

      const response = await this.s3Client.send(command);
      
      // Convert stream to buffer
      const chunks: Buffer[] = [];
      // @ts-ignore - Body is actually a readable stream
      for await (const chunk of response.Body) {
        chunks.push(Buffer.from(chunk));
      }
      
      const buffer = Buffer.concat(chunks);
      
      this.logger.log(`Downloaded file ${key}, size: ${buffer.length} bytes`);
      
      return buffer;
    } catch (error) {
      this.logger.error(`Error downloading file from S3: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Delete a file from S3 storage
   * 
   * @param key The key (path) of the file in S3
   * @returns Success status
   */
  async delete(key: string): Promise<boolean> {
    try {
      const command = new DeleteObjectCommand({
        Bucket: this.bucket,
        Key: key,
      });

      await this.s3Client.send(command);
      
      this.logger.log(`Deleted file ${key}`);
      
      return true;
    } catch (error) {
      this.logger.error(`Error deleting file from S3: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * List files in S3 with a given prefix
   * 
   * @param prefix Directory or prefix path
   * @param maxKeys Maximum number of keys to return
   * @returns List of file keys
   */
  async list(prefix: string, maxKeys = 1000): Promise<string[]> {
    try {
      const command = new ListObjectsV2Command({
        Bucket: this.bucket,
        Prefix: prefix,
        MaxKeys: maxKeys,
      });

      const response = await this.s3Client.send(command);
      
      const keys = response.Contents?.map(item => item.Key) as string[] || [];
      
      this.logger.log(`Listed ${keys.length} files with prefix ${prefix}`);
      
      return keys;
    } catch (error) {
      this.logger.error(`Error listing files from S3: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Check if a file exists in S3 storage
   * 
   * @param key The key (path) of the file in S3
   * @returns True if the file exists
   */
  async exists(key: string): Promise<boolean> {
    try {
      const command = new HeadObjectCommand({
        Bucket: this.bucket,
        Key: key,
      });

      await this.s3Client.send(command);
      
      this.logger.log(`File ${key} exists`);
      
      return true;
    } catch (error) {
      if (error.name === 'NotFound') {
        this.logger.log(`File ${key} does not exist`);
        return false;
      }
      
      this.logger.error(`Error checking if file exists in S3: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Get file metadata from S3 storage
   * 
   * @param key The key (path) of the file in S3
   * @returns File metadata
   */
  async getMetadata(key: string): Promise<Record<string, any>> {
    try {
      const command = new HeadObjectCommand({
        Bucket: this.bucket,
        Key: key,
      });

      const response = await this.s3Client.send(command);
      
      const metadata = {
        contentType: response.ContentType,
        contentLength: response.ContentLength,
        eTag: response.ETag,
        lastModified: response.LastModified,
        metadata: response.Metadata,
      };
      
      this.logger.log(`Retrieved metadata for file ${key}`);
      
      return metadata;
    } catch (error) {
      this.logger.error(`Error getting file metadata from S3: ${error.message}`, error.stack);
      throw error;
    }
  }
}
