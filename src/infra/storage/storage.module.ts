import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { StorageService } from './storage.service';
import { S3StorageService } from './s3-storage.service';
import { StorageController } from './storage.controller';

@Module({
  imports: [ConfigModule],
  providers: [
    StorageService,
    S3StorageService,
  ],
  controllers: [StorageController],
  exports: [
    StorageService,
    S3StorageService,
  ],
})
export class StorageModule {}
