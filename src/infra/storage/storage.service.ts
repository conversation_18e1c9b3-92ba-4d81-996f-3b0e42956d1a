import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { S3StorageService } from './s3-storage.service';

export enum StorageProvider {
  S3 = 's3',
  DIGITAL_OCEAN = 'digital_ocean',
  MINIO = 'minio',
  FIREBASE = 'firebase',
  LOCAL = 'local',
}

export interface StorageFile {
  buffer: Buffer;
  mimetype: string;
  originalname: string;
  size: number;
}

export interface StorageUploadOptions {
  path?: string;
  isPublic?: boolean;
  metadata?: Record<string, string>;
  contentType?: string;
}

@Injectable()
export class StorageService {
  private readonly logger = new Logger(StorageService.name);
  private provider: StorageProvider;

  constructor(
    private readonly configService: ConfigService,
    private readonly s3StorageService: S3StorageService,
  ) {
    this.provider = this.configService.get<StorageProvider>('STORAGE_PROVIDER') || StorageProvider.S3;
    this.logger.log(`Initialized storage service with provider: ${this.provider}`);
  }

  /**
   * Upload a file to the configured storage provider
   * 
   * @param file The file to upload
   * @param key The key or path for storing the file
   * @param options Upload options
   * @returns URL to the uploaded file
   */
  async upload(file: StorageFile, key: string, options?: StorageUploadOptions): Promise<string> {
    this.logger.log(`Uploading file ${file.originalname} to ${key}`);
    
    switch (this.provider) {
      case StorageProvider.S3:
      case StorageProvider.DIGITAL_OCEAN:
      case StorageProvider.MINIO:
        return this.s3StorageService.upload(file, key, options);
      case StorageProvider.FIREBASE:
        // Firebase storage is handled separately via FirebaseStorageService
        throw new Error('Please use FirebaseStorageService directly for Firebase storage operations');
      case StorageProvider.LOCAL:
        throw new Error('Local storage provider not implemented');
      default:
        throw new Error(`Unsupported storage provider: ${this.provider}`);
    }
  }

  /**
   * Generate a presigned URL for direct client uploads
   * 
   * @param key The key or path for storing the file
   * @param contentType The file's content type
   * @param expiresIn Expiration time in seconds (default: 15 minutes)
   * @returns Presigned URL and form fields if required
   */
  async getPresignedUploadUrl(
    key: string, 
    contentType: string, 
    expiresIn = 15 * 60
  ): Promise<{ url: string; fields?: Record<string, string> }> {
    this.logger.log(`Generating presigned upload URL for ${key}`);
    
    switch (this.provider) {
      case StorageProvider.S3:
      case StorageProvider.DIGITAL_OCEAN:
      case StorageProvider.MINIO:
        return this.s3StorageService.getPresignedUploadUrl(key, contentType, expiresIn);
      default:
        throw new Error(`Presigned uploads not supported for provider: ${this.provider}`);
    }
  }

  /**
   * Generate a presigned URL for downloading/viewing a file
   * 
   * @param key The key or path of the file
   * @param expiresIn Expiration time in seconds (default: 15 minutes)
   * @returns Presigned URL
   */
  async getPresignedDownloadUrl(key: string, expiresIn = 15 * 60): Promise<string> {
    this.logger.log(`Generating presigned download URL for ${key}`);
    
    switch (this.provider) {
      case StorageProvider.S3:
      case StorageProvider.DIGITAL_OCEAN:
      case StorageProvider.MINIO:
        return this.s3StorageService.getPresignedDownloadUrl(key, expiresIn);
      default:
        throw new Error(`Presigned downloads not supported for provider: ${this.provider}`);
    }
  }

  /**
   * Download a file from storage
   * 
   * @param key The key or path of the file
   * @returns File contents as Buffer
   */
  async download(key: string): Promise<Buffer> {
    this.logger.log(`Downloading file ${key}`);
    
    switch (this.provider) {
      case StorageProvider.S3:
      case StorageProvider.DIGITAL_OCEAN:
      case StorageProvider.MINIO:
        return this.s3StorageService.download(key);
      case StorageProvider.FIREBASE:
        throw new Error('Please use FirebaseStorageService directly for Firebase storage operations');
      case StorageProvider.LOCAL:
        throw new Error('Local storage provider not implemented');
      default:
        throw new Error(`Unsupported storage provider: ${this.provider}`);
    }
  }

  /**
   * Delete a file from storage
   * 
   * @param key The key or path of the file
   * @returns Success status
   */
  async delete(key: string): Promise<boolean> {
    this.logger.log(`Deleting file ${key}`);
    
    switch (this.provider) {
      case StorageProvider.S3:
      case StorageProvider.DIGITAL_OCEAN:
      case StorageProvider.MINIO:
        return this.s3StorageService.delete(key);
      case StorageProvider.FIREBASE:
        throw new Error('Please use FirebaseStorageService directly for Firebase storage operations');
      case StorageProvider.LOCAL:
        throw new Error('Local storage provider not implemented');
      default:
        throw new Error(`Unsupported storage provider: ${this.provider}`);
    }
  }

  /**
   * List files in a directory/prefix
   * 
   * @param prefix Directory or prefix path
   * @param maxKeys Maximum number of keys to return
   * @returns List of file keys
   */
  async list(prefix: string, maxKeys = 1000): Promise<string[]> {
    this.logger.log(`Listing files with prefix ${prefix}`);
    
    switch (this.provider) {
      case StorageProvider.S3:
      case StorageProvider.DIGITAL_OCEAN:
      case StorageProvider.MINIO:
        return this.s3StorageService.list(prefix, maxKeys);
      case StorageProvider.FIREBASE:
        throw new Error('Please use FirebaseStorageService directly for Firebase storage operations');
      case StorageProvider.LOCAL:
        throw new Error('Local storage provider not implemented');
      default:
        throw new Error(`Unsupported storage provider: ${this.provider}`);
    }
  }

  /**
   * Check if a file exists in storage
   * 
   * @param key The key or path of the file
   * @returns True if the file exists
   */
  async exists(key: string): Promise<boolean> {
    this.logger.log(`Checking if file ${key} exists`);
    
    switch (this.provider) {
      case StorageProvider.S3:
      case StorageProvider.DIGITAL_OCEAN:
      case StorageProvider.MINIO:
        return this.s3StorageService.exists(key);
      case StorageProvider.FIREBASE:
        throw new Error('Please use FirebaseStorageService directly for Firebase storage operations');
      case StorageProvider.LOCAL:
        throw new Error('Local storage provider not implemented');
      default:
        throw new Error(`Unsupported storage provider: ${this.provider}`);
    }
  }

  /**
   * Get file metadata
   * 
   * @param key The key or path of the file
   * @returns File metadata
   */
  async getMetadata(key: string): Promise<Record<string, any>> {
    this.logger.log(`Getting metadata for file ${key}`);
    
    switch (this.provider) {
      case StorageProvider.S3:
      case StorageProvider.DIGITAL_OCEAN:
      case StorageProvider.MINIO:
        return this.s3StorageService.getMetadata(key);
      case StorageProvider.FIREBASE:
        throw new Error('Please use FirebaseStorageService directly for Firebase storage operations');
      case StorageProvider.LOCAL:
        throw new Error('Local storage provider not implemented');
      default:
        throw new Error(`Unsupported storage provider: ${this.provider}`);
    }
  }
}
