import { 
  Controller, 
  Post, 
  Get, 
  Delete, 
  Param, 
  Body, 
  UploadedFile, 
  UseInterceptors,
  BadRequestException,
  NotFoundException,
  Query,
  Res,
  UseGuards,
} from '@nestjs/common';
import { FileInterceptor } from '@nestjs/platform-express';
import { 
  ApiTags, 
  ApiConsumes, 
  ApiBody, 
  ApiOperation, 
  ApiResponse, 
  ApiParam,
  ApiBearerAuth,
  ApiQuery,
} from '@nestjs/swagger';
import { Response } from 'express';
import { JwtAuthGuard } from '@core/auth/guards/jwt-auth.guard';
import { RolesGuard } from '@core/auth/guards/roles.guard';
import { Roles } from '@core/auth/decorators/roles.decorator';
import { StorageService } from './storage.service';
import { MulterFile } from './multer-file.interface';

class UploadDto {
  filename: string;
  path?: string;
  isPublic?: boolean;
}

class PresignedUrlDto {
  key: string;
  contentType: string;
  expiresIn?: number;
}

@ApiTags('storage')
@ApiBearerAuth()
@Controller('storage')
@UseGuards(JwtAuthGuard)
export class StorageController {
  constructor(private readonly storageService: StorageService) {}

  @Post('upload')
  @UseInterceptors(FileInterceptor('file'))
  @ApiConsumes('multipart/form-data')
  @ApiOperation({ summary: 'Upload a file to storage' })
  @ApiBody({
    schema: {
      type: 'object',
      required: ['file'],
      properties: {
        file: {
          type: 'string',
          format: 'binary',
          description: 'File to upload',
        },
        path: {
          type: 'string',
          description: 'Path/folder in which to store the file',
        },
        isPublic: {
          type: 'boolean',
          description: 'Whether the file should be publicly accessible',
        },
      },
    },
  })
  @ApiResponse({ status: 201, description: 'File uploaded successfully.' })
  @ApiResponse({ status: 400, description: 'Invalid input.' })
  @ApiResponse({ status: 401, description: 'Unauthorized.' })
  async uploadFile(
    @UploadedFile() file: MulterFile,
    @Body() uploadDto: UploadDto,
  ) {
    if (!file) {
      throw new BadRequestException('No file uploaded');
    }

    try {
      const storageFile = {
        buffer: file.buffer,
        mimetype: file.mimetype,
        originalname: file.originalname,
        size: file.size,
      };

      const key = uploadDto.filename || file.originalname;
      const options = {
        path: uploadDto.path,
        isPublic: uploadDto.isPublic !== undefined ? uploadDto.isPublic : true,
      };

      const url = await this.storageService.upload(storageFile, key, options);

      return {
        url,
        key: options.path ? `${options.path}/${key}` : key,
        filename: file.originalname,
        mimetype: file.mimetype,
        size: file.size,
      };
    } catch (error) {
      throw new BadRequestException(`Failed to upload file: ${error.message}`);
    }
  }

  @Post('presigned-upload')
  @ApiOperation({ summary: 'Generate a presigned URL for direct client uploads' })
  @ApiBody({ type: PresignedUrlDto })
  @ApiResponse({ status: 201, description: 'Presigned URL generated successfully.' })
  @ApiResponse({ status: 400, description: 'Invalid input.' })
  @ApiResponse({ status: 401, description: 'Unauthorized.' })
  async getPresignedUploadUrl(@Body() dto: PresignedUrlDto) {
    try {
      const { key, contentType, expiresIn } = dto;
      const result = await this.storageService.getPresignedUploadUrl(
        key,
        contentType,
        expiresIn,
      );

      return result;
    } catch (error) {
      throw new BadRequestException(`Failed to generate presigned URL: ${error.message}`);
    }
  }

  @Get('files/:key')
  @ApiOperation({ summary: 'Download a file from storage' })
  @ApiParam({ name: 'key', description: 'File key/path in storage' })
  @ApiResponse({ status: 200, description: 'File retrieved successfully.' })
  @ApiResponse({ status: 404, description: 'File not found.' })
  @ApiResponse({ status: 401, description: 'Unauthorized.' })
  async downloadFile(@Param('key') key: string, @Res() res: Response) {
    try {
      // Check if file exists
      const exists = await this.storageService.exists(key);
      if (!exists) {
        throw new NotFoundException(`File ${key} not found`);
      }

      // Get file metadata
      const metadata = await this.storageService.getMetadata(key);
      
      // Download the file
      const buffer = await this.storageService.download(key);

      // Set appropriate headers
      res.setHeader('Content-Type', metadata.contentType || 'application/octet-stream');
      res.setHeader('Content-Disposition', `attachment; filename="${key.split('/').pop()}"`);
      
      // Send the file
      return res.send(buffer);
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      throw new BadRequestException(`Failed to download file: ${error.message}`);
    }
  }

  @Get('presigned-download/:key')
  @ApiOperation({ summary: 'Generate a presigned URL for downloading/viewing a file' })
  @ApiParam({ name: 'key', description: 'File key/path in storage' })
  @ApiQuery({ name: 'expiresIn', required: false, description: 'URL expiration time in seconds' })
  @ApiResponse({ status: 200, description: 'Presigned URL generated successfully.' })
  @ApiResponse({ status: 404, description: 'File not found.' })
  @ApiResponse({ status: 401, description: 'Unauthorized.' })
  async getPresignedDownloadUrl(
    @Param('key') key: string,
    @Query('expiresIn') expiresIn?: number,
  ) {
    try {
      // Check if file exists
      const exists = await this.storageService.exists(key);
      if (!exists) {
        throw new NotFoundException(`File ${key} not found`);
      }

      const url = await this.storageService.getPresignedDownloadUrl(key, expiresIn);

      return { url };
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      throw new BadRequestException(`Failed to generate presigned URL: ${error.message}`);
    }
  }

  @Get('files')
  @ApiOperation({ summary: 'List files in storage with a given prefix' })
  @ApiQuery({ name: 'prefix', required: true, description: 'Directory/prefix path' })
  @ApiQuery({ name: 'maxKeys', required: false, description: 'Maximum number of keys to return' })
  @ApiResponse({ status: 200, description: 'Files listed successfully.' })
  @ApiResponse({ status: 401, description: 'Unauthorized.' })
  @Roles('admin')
  @UseGuards(RolesGuard)
  async listFiles(
    @Query('prefix') prefix: string,
    @Query('maxKeys') maxKeys?: number,
  ) {
    try {
      const keys = await this.storageService.list(prefix, maxKeys);

      return { keys };
    } catch (error) {
      throw new BadRequestException(`Failed to list files: ${error.message}`);
    }
  }

  @Delete('files/:key')
  @ApiOperation({ summary: 'Delete a file from storage' })
  @ApiParam({ name: 'key', description: 'File key/path in storage' })
  @ApiResponse({ status: 200, description: 'File deleted successfully.' })
  @ApiResponse({ status: 404, description: 'File not found.' })
  @ApiResponse({ status: 401, description: 'Unauthorized.' })
  @ApiResponse({ status: 403, description: 'Forbidden - requires admin role.' })
  @Roles('admin')
  @UseGuards(RolesGuard)
  async deleteFile(@Param('key') key: string) {
    try {
      // Check if file exists
      const exists = await this.storageService.exists(key);
      if (!exists) {
        throw new NotFoundException(`File ${key} not found`);
      }

      await this.storageService.delete(key);

      return { message: `File ${key} deleted successfully` };
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      throw new BadRequestException(`Failed to delete file: ${error.message}`);
    }
  }

  @Get('metadata/:key')
  @ApiOperation({ summary: 'Get file metadata' })
  @ApiParam({ name: 'key', description: 'File key/path in storage' })
  @ApiResponse({ status: 200, description: 'Metadata retrieved successfully.' })
  @ApiResponse({ status: 404, description: 'File not found.' })
  @ApiResponse({ status: 401, description: 'Unauthorized.' })
  async getFileMetadata(@Param('key') key: string) {
    try {
      // Check if file exists
      const exists = await this.storageService.exists(key);
      if (!exists) {
        throw new NotFoundException(`File ${key} not found`);
      }

      const metadata = await this.storageService.getMetadata(key);

      return { metadata };
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      throw new BadRequestException(`Failed to get file metadata: ${error.message}`);
    }
  }
}
