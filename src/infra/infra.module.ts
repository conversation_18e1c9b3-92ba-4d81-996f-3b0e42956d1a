import { Module } from '@nestjs/common';
import { RedisModule } from './redis/redis.module';
import { RabbitMQModule } from './rabbitmq/rabbitmq.module';
import { MongoDbModule } from './mongodb/mongodb.module';
import { WebsocketModule } from './websocket/websocket.module';
import { FirebaseModule } from './firebase/firebase.module';
import { StorageModule } from './storage/storage.module';

@Module({
  imports: [
    RedisModule,
    RabbitMQModule,
    MongoDbModule,
    WebsocketModule,
    FirebaseModule,
    StorageModule,
  ],
  exports: [
    RedisModule,
    RabbitMQModule,
    MongoDbModule,
    WebsocketModule,
    FirebaseModule,
    StorageModule,
  ],
})
export class InfraModule {}
