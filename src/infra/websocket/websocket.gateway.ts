import {
  WebSocketGateway,
  OnGatewayInit,
  OnGatewayConnection,
  OnGatewayDisconnect,
  WebSocketServer,
  SubscribeMessage,
} from '@nestjs/websockets';
import { Server, Socket } from 'socket.io';
import { JwtService } from '@nestjs/jwt';
import { LoggerService } from '../../core/logger/logger.service';
import { ConfigService } from '@nestjs/config';

@WebSocketGateway({
  cors: {
    origin: '*', // In production, you should restrict this
  },
})
export class WebsocketGateway
  implements OnGatewayInit, OnGatewayConnection, OnGatewayDisconnect
{
  @WebSocketServer() server: Server;

  constructor(
    private readonly jwtService: JwtService,
    private readonly loggerService: LoggerService,
    private readonly configService: ConfigService,
  ) {
    this.loggerService.setContext('WebsocketGateway');
  }

  afterInit(server: Server) {
    this.loggerService.log('WebSocket Gateway initialized');
  }

  async handleConnection(client: Socket) {
    try {
      // Authenticate client
      const token = client.handshake.auth.token || client.handshake.headers.authorization?.split(' ')[1];
      
      if (!token) {
        this.loggerService.warn(`Client ${client.id} connected without authentication token`);
        // Allow connection but limit access
        client.join('public');
        return;
      }
      
      try {
        const payload = this.jwtService.verify(token, {
          secret: this.configService.get<string>('JWT_SECRET'),
        });
        
        // Store user info in socket
        client.data.user = payload;
        
        // Join user-specific room
        client.join(`user:${payload.sub}`);
        
        // Join role-based rooms
        if (payload.roles && Array.isArray(payload.roles)) {
          payload.roles.forEach(role => {
            client.join(`role:${role}`);
          });
        }
        
        this.loggerService.log(`Client ${client.id} authenticated as user ${payload.sub}`);
      } catch (error) {
        this.loggerService.warn(`Invalid token from client ${client.id}`);
        client.join('public');
      }
    } catch (error) {
      this.loggerService.error(`Error during client connection: ${error.message}`, error.stack);
      client.disconnect(true);
    }
  }

  handleDisconnect(client: Socket) {
    this.loggerService.log(`Client disconnected: ${client.id}`);
  }

  @SubscribeMessage('ping')
  handlePing(client: Socket) {
    return { event: 'pong', data: { timestamp: new Date().toISOString() } };
  }
  
  @SubscribeMessage('subscribe')
  handleSubscribe(client: Socket, payload: { topic: string }) {
    if (!payload || !payload.topic) {
      return { event: 'error', data: { message: 'Topic is required' } };
    }
    
    // Check if user is allowed to subscribe to this topic
    const user = client.data.user;
    
    // Example permission check (implement your own logic)
    if (payload.topic.startsWith('private:') && !user) {
      return { event: 'error', data: { message: 'Authentication required' } };
    }
    
    client.join(payload.topic);
    this.loggerService.log(`Client ${client.id} subscribed to ${payload.topic}`);
    
    return { event: 'subscribed', data: { topic: payload.topic } };
  }
  
  @SubscribeMessage('unsubscribe')
  handleUnsubscribe(client: Socket, payload: { topic: string }) {
    if (!payload || !payload.topic) {
      return { event: 'error', data: { message: 'Topic is required' } };
    }
    
    client.leave(payload.topic);
    this.loggerService.log(`Client ${client.id} unsubscribed from ${payload.topic}`);
    
    return { event: 'unsubscribed', data: { topic: payload.topic } };
  }
}
