import { Injectable } from '@nestjs/common';
import { WebsocketGateway } from './websocket.gateway';
import { LoggerService } from '../../core/logger/logger.service';

@Injectable()
export class WebsocketService {
  constructor(
    private readonly websocketGateway: WebsocketGateway,
    private readonly loggerService: LoggerService,
  ) {
    this.loggerService.setContext('WebsocketService');
  }

  /**
   * Send a notification to a specific user
   */
  sendToUser(userId: string, event: string, data: any) {
    try {
      this.websocketGateway.server.to(`user:${userId}`).emit(event, data);
      this.loggerService.log(`Event ${event} sent to user ${userId}`);
    } catch (error) {
      this.loggerService.error(`Error sending event to user ${userId}: ${error.message}`, error.stack);
    }
  }

  /**
   * Send a notification to users with a specific role
   */
  sendToRole(role: string, event: string, data: any) {
    try {
      this.websocketGateway.server.to(`role:${role}`).emit(event, data);
      this.loggerService.log(`Event ${event} sent to role ${role}`);
    } catch (error) {
      this.loggerService.error(`Error sending event to role ${role}: ${error.message}`, error.stack);
    }
  }

  /**
   * Send a notification to all authenticated users
   */
  broadcast(event: string, data: any) {
    try {
      this.websocketGateway.server.emit(event, data);
      this.loggerService.log(`Event ${event} broadcasted to all clients`);
    } catch (error) {
      this.loggerService.error(`Error broadcasting event: ${error.message}`, error.stack);
    }
  }

  /**
   * Send a notification to a specific topic
   */
  sendToTopic(topic: string, event: string, data: any) {
    try {
      this.websocketGateway.server.to(topic).emit(event, data);
      this.loggerService.log(`Event ${event} sent to topic ${topic}`);
    } catch (error) {
      this.loggerService.error(`Error sending event to topic ${topic}: ${error.message}`, error.stack);
    }
  }

  /**
   * Send a dashboard update notification
   */
  sendDashboardUpdate(data: any) {
    this.broadcast('dashboard_update', data);
  }

  /**
   * Send a notification
   */
  sendNotification(userId: string, notification: any) {
    this.sendToUser(userId, 'notification', notification);
  }
}
