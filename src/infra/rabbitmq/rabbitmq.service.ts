import { Injectable, OnModuleInit, OnModuleDestroy } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { RabbitSubscribe } from '@golevelup/nestjs-rabbitmq';
import { AmqpConnection } from '@golevelup/nestjs-rabbitmq';
import { LoggerService } from '../../core/logger/logger.service';

@Injectable()
export class RabbitMQService implements OnModuleInit, OnModuleDestroy {
  constructor(
    private readonly configService: ConfigService,
    private readonly amqpConnection: AmqpConnection,
    private readonly loggerService: LoggerService,
  ) {
    this.loggerService.setContext('RabbitMQService');
  }

  async onModuleInit() {
    this.loggerService.log('RabbitMQ service initialized');
  }

  async onModuleDestroy() {
    this.loggerService.log('RabbitMQ service destroyed');
  }

  /**
   * Publish a message to RabbitMQ
   */
  async publish(message: any, routingKey?: string): Promise<void> {
    const exchange = this.configService.get<string>('RABBITMQ_EXCHANGE') || 'default_exchange';
    const actualRoutingKey = routingKey || this.configService.get<string>('RABBITMQ_ROUTING_KEY') || 'default_routing_key';
    
    try {
      await this.amqpConnection.publish(
        exchange,
        actualRoutingKey,
        message
      );
      this.loggerService.log(`Message published to ${exchange}:${actualRoutingKey}`);
    } catch (error) {
      this.loggerService.error(`Failed to publish message: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Example subscriber method - you can create more specific ones for different message types
   */
  @RabbitSubscribe({
    exchange: process.env.RABBITMQ_EXCHANGE || 'mutant_exchange',
    routingKey: process.env.RABBITMQ_ROUTING_KEY || 'mutant_routing_key',
    queue: process.env.RABBITMQ_QUEUE || 'mutant_queue',
  })
  async handleMessage(message: any) {
    this.loggerService.log(`Received message: ${JSON.stringify(message)}`);
    
    try {
      // Process message based on type
      if (message.type) {
        switch (message.type) {
          case 'data_sync':
            this.loggerService.log('Processing data sync message');
            // Handle data sync
            break;
          case 'notification':
            this.loggerService.log('Processing notification message');
            // Handle notification
            break;
          default:
            this.loggerService.log(`Unknown message type: ${message.type}`);
        }
      }
    } catch (error) {
      this.loggerService.error(`Error processing message: ${error.message}`, error.stack);
    }
  }
}
