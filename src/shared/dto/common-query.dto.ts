import { ApiPropertyOptional } from '@nestjs/swagger';
import { IsOptional, IsInt, Min, Max, IsString, IsDateString, IsEnum } from 'class-validator';
import { Transform, Type } from 'class-transformer';

/**
 * Base pagination parameters DTO
 */
export class PaginationQueryDto {
  @ApiPropertyOptional({
    description: 'Page number (starts from 1)',
    example: 1,
    minimum: 1,
    default: 1,
  })
  @IsOptional()
  @Type(() => Number)
  @IsInt()
  @Min(1)
  page?: number = 1;

  @ApiPropertyOptional({
    description: 'Number of items per page',
    example: 20,
    minimum: 1,
    maximum: 100,
    default: 20,
  })
  @IsOptional()
  @Type(() => Number)
  @IsInt()
  @Min(1)
  @Max(100)
  limit?: number = 20;
}

/**
 * Base search parameters DTO
 */
export class SearchQueryDto extends PaginationQueryDto {
  @ApiPropertyOptional({
    description: 'Search term for filtering results',
    example: 'search term',
  })
  @IsOptional()
  @IsString()
  search?: string;
}

/**
 * Date range query parameters DTO
 */
export class DateRangeQueryDto {
  @ApiPropertyOptional({
    description: 'Start date for filtering (ISO format)',
    example: '2023-01-01T00:00:00Z',
    format: 'date-time',
  })
  @IsOptional()
  @IsDateString()
  startDate?: string;

  @ApiPropertyOptional({
    description: 'End date for filtering (ISO format)',
    example: '2023-12-31T23:59:59Z',
    format: 'date-time',
  })
  @IsOptional()
  @IsDateString()
  endDate?: string;
}

/**
 * Combined search and date range query DTO
 */
export class SearchDateRangeQueryDto extends SearchQueryDto {
  @ApiPropertyOptional({
    description: 'Start date for filtering (ISO format)',
    example: '2023-01-01T00:00:00Z',
    format: 'date-time',
  })
  @IsOptional()
  @IsDateString()
  startDate?: string;

  @ApiPropertyOptional({
    description: 'End date for filtering (ISO format)',
    example: '2023-12-31T23:59:59Z',
    format: 'date-time',
  })
  @IsOptional()
  @IsDateString()
  endDate?: string;
}

/**
 * Status filter query DTO factory
 */
export function createStatusQueryDto<T extends Record<string, any>>(statusEnum: T) {
  class StatusQueryDto extends SearchQueryDto {
    @ApiPropertyOptional({
      description: 'Filter by status',
      enum: statusEnum,
    })
    @IsOptional()
    @IsEnum(statusEnum)
    status?: keyof T;
  }
  
  return StatusQueryDto;
}

/**
 * User filter query DTO
 */
export class UserFilterQueryDto extends SearchQueryDto {
  @ApiPropertyOptional({
    description: 'Filter by user ID',
    example: '123e4567-e89b-12d3-a456-************',
    format: 'uuid',
  })
  @IsOptional()
  @IsString()
  userId?: string;
}

/**
 * Supplier filter query DTO
 */
export class SupplierFilterQueryDto extends SearchQueryDto {
  @ApiPropertyOptional({
    description: 'Filter by supplier ID',
    example: '123e4567-e89b-12d3-a456-************',
    format: 'uuid',
  })
  @IsOptional()
  @IsString()
  supplierId?: string;
}

/**
 * Category filter query DTO
 */
export class CategoryFilterQueryDto extends SearchQueryDto {
  @ApiPropertyOptional({
    description: 'Filter by category ID',
    example: '123e4567-e89b-12d3-a456-************',
    format: 'uuid',
  })
  @IsOptional()
  @IsString()
  categoryId?: string;
}

/**
 * Analytics query parameters DTO
 */
export class AnalyticsQueryDto extends DateRangeQueryDto {
  @ApiPropertyOptional({
    description: 'Grouping period for analytics',
    enum: ['DAILY', 'WEEKLY', 'MONTHLY', 'QUARTERLY', 'YEARLY'],
    example: 'MONTHLY',
    default: 'MONTHLY',
  })
  @IsOptional()
  @IsEnum(['DAILY', 'WEEKLY', 'MONTHLY', 'QUARTERLY', 'YEARLY'])
  period?: 'DAILY' | 'WEEKLY' | 'MONTHLY' | 'QUARTERLY' | 'YEARLY';

  @ApiPropertyOptional({
    description: 'Number of periods to include',
    example: 12,
    minimum: 1,
    maximum: 100,
    default: 12,
  })
  @IsOptional()
  @Type(() => Number)
  @IsInt()
  @Min(1)
  @Max(100)
  periods?: number;
}

/**
 * Standard list response DTO factory
 */
export function createListResponseDto<T>(itemType: new () => T) {
  class ListResponseDto {
    @ApiPropertyOptional({
      description: 'Array of items',
      type: [itemType],
    })
    data: T[];

    @ApiPropertyOptional({
      description: 'Pagination metadata',
      type: 'object',
      properties: {
        total: { type: 'number', description: 'Total number of items' },
        page: { type: 'number', description: 'Current page number' },
        limit: { type: 'number', description: 'Items per page' },
        totalPages: { type: 'number', description: 'Total number of pages' },
      },
    })
    meta: {
      total: number;
      page: number;
      limit: number;
      totalPages: number;
    };
  }

  return ListResponseDto;
}

/**
 * Standard summary response DTO
 */
export class SummaryResponseDto {
  @ApiPropertyOptional({
    description: 'Total count',
    example: 150,
  })
  total: number;

  @ApiPropertyOptional({
    description: 'Active count',
    example: 120,
  })
  active: number;

  @ApiPropertyOptional({
    description: 'Inactive count',
    example: 30,
  })
  inactive: number;

  @ApiPropertyOptional({
    description: 'Percentage change from previous period',
    example: '+5.2%',
  })
  changePercent?: string;

  @ApiPropertyOptional({
    description: 'Additional metrics',
    type: 'object',
  })
  metrics?: Record<string, any>;
}

/**
 * Standard ID parameter DTO
 */
export class IdParamDto {
  @ApiPropertyOptional({
    description: 'Resource ID',
    example: '123e4567-e89b-12d3-a456-************',
    format: 'uuid',
  })
  @IsString()
  id: string;
}

/**
 * Bulk operation DTO
 */
export class BulkOperationDto {
  @ApiPropertyOptional({
    description: 'Array of resource IDs to operate on',
    example: ['123e4567-e89b-12d3-a456-************', '123e4567-e89b-12d3-a456-************'],
    type: [String],
  })
  @IsString({ each: true })
  ids: string[];

  @ApiPropertyOptional({
    description: 'Operation to perform',
    enum: ['DELETE', 'ACTIVATE', 'DEACTIVATE', 'ARCHIVE'],
    example: 'ACTIVATE',
  })
  @IsEnum(['DELETE', 'ACTIVATE', 'DEACTIVATE', 'ARCHIVE'])
  operation: 'DELETE' | 'ACTIVATE' | 'DEACTIVATE' | 'ARCHIVE';

  @ApiPropertyOptional({
    description: 'Optional reason for the operation',
    example: 'Bulk activation for new season',
  })
  @IsOptional()
  @IsString()
  reason?: string;
}
