import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';

export class PaginationMetaDto {
  @ApiProperty({
    description: 'Total number of items',
    example: 150,
    type: 'integer',
    minimum: 0,
  })
  total: number;

  @ApiProperty({
    description: 'Current page number',
    example: 1,
    type: 'integer',
    minimum: 1,
  })
  page: number;

  @ApiProperty({
    description: 'Number of items per page',
    example: 10,
    type: 'integer',
    minimum: 1,
    maximum: 100,
  })
  limit: number;

  @ApiProperty({
    description: 'Total number of pages',
    example: 15,
    type: 'integer',
    minimum: 0,
  })
  totalPages: number;

  @ApiProperty({
    description: 'Whether there is a next page',
    example: true,
  })
  hasNext: boolean;

  @ApiProperty({
    description: 'Whether there is a previous page',
    example: false,
  })
  hasPrevious: boolean;
}

export function createPaginatedResponseDto<T>(classRef: new () => T) {
  class PaginatedResponseDto {
    @ApiProperty({
      description: 'Array of items for the current page',
      type: [classRef],
    })
    @Type(() => classRef)
    data: T[];

    @ApiProperty({
      description: 'Pagination metadata',
      type: PaginationMetaDto,
    })
    @Type(() => PaginationMetaDto)
    meta: PaginationMetaDto;
  }

  return PaginatedResponseDto;
}

export class ErrorResponseDto {
  @ApiProperty({
    description: 'HTTP status code',
    example: 400,
    type: 'integer',
  })
  statusCode: number;

  @ApiProperty({
    description: 'Error message or array of validation errors',
    oneOf: [
      { type: 'string', example: 'Bad Request' },
      { type: 'array', items: { type: 'string' }, example: ['Name is required', 'Email must be valid'] }
    ],
  })
  message: string | string[];

  @ApiProperty({
    description: 'Error type',
    example: 'Bad Request',
  })
  error: string;

  @ApiProperty({
    description: 'Timestamp of the error',
    example: '2023-01-15T08:30:00Z',
    format: 'date-time',
    required: false,
  })
  timestamp?: string;

  @ApiProperty({
    description: 'Request path where the error occurred',
    example: '/api/v1/products',
    required: false,
  })
  path?: string;
}

export class UnauthorizedResponseDto {
  @ApiProperty({
    description: 'HTTP status code',
    example: 401,
    type: 'integer',
  })
  statusCode: number;

  @ApiProperty({
    description: 'Error message',
    example: 'Unauthorized',
  })
  message: string;
}

export class ForbiddenResponseDto {
  @ApiProperty({
    description: 'HTTP status code',
    example: 403,
    type: 'integer',
  })
  statusCode: number;

  @ApiProperty({
    description: 'Error message',
    example: 'Forbidden resource',
  })
  message: string;
}

export class NotFoundResponseDto {
  @ApiProperty({
    description: 'HTTP status code',
    example: 404,
    type: 'integer',
  })
  statusCode: number;

  @ApiProperty({
    description: 'Error message',
    example: 'Resource not found',
  })
  message: string;
}

export class ConflictResponseDto {
  @ApiProperty({
    description: 'HTTP status code',
    example: 409,
    type: 'integer',
  })
  statusCode: number;

  @ApiProperty({
    description: 'Error message',
    example: 'Resource already exists',
  })
  message: string;
}

export class InternalServerErrorResponseDto {
  @ApiProperty({
    description: 'HTTP status code',
    example: 500,
    type: 'integer',
  })
  statusCode: number;

  @ApiProperty({
    description: 'Error message',
    example: 'Internal server error',
  })
  message: string;
}
