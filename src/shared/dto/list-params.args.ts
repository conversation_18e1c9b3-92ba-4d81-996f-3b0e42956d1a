import { ArgsType, Field, Int } from '@nestjs/graphql';
import { Type } from '@nestjs/common';
import { IsOptional, IsInt, Min, IsString, IsObject } from 'class-validator';

@ArgsType()
export class ListParamsArgs {
  @Field(() => Int, { nullable: true })
  @IsOptional()
  @IsInt()
  @Min(1)
  page?: number;

  @Field(() => Int, { nullable: true })
  @IsOptional()
  @IsInt()
  @Min(1)
  limit?: number;

  @Field({ nullable: true })
  @IsOptional()
  @IsString()
  search?: string;

  @Field(() => Object, { nullable: true })
  @IsOptional()
  @IsObject()
  where?: Record<string, any>;

  @Field(() => Object, { nullable: true })
  @IsOptional()
  @IsObject()
  order?: Record<string, 'asc' | 'desc'>;
}
