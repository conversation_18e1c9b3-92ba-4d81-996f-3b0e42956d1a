import { BadRequestException } from '@nestjs/common';

/**
 * Standard query parameters for list operations
 */
export interface StandardListParams {
  page?: number;
  limit?: number;
  where?: Record<string, any>;
  search?: string;
  order?: Record<string, 'asc' | 'desc'>;
}

/**
 * Parse and validate standard query parameters
 * 
 * @param query Raw query parameters from request
 * @returns Validated and transformed query parameters
 */
export function parseQueryParams(query: Record<string, any>): StandardListParams {
  const params: StandardListParams = {};
  
  // Handle pagination
  if (query.page !== undefined) {
    const page = parseInt(query.page, 10);
    if (isNaN(page) || page < 1) {
      throw new BadRequestException('Page must be a positive number');
    }
    params.page = page;
  }
  
  if (query.limit !== undefined) {
    const limit = parseInt(query.limit, 10);
    if (isNaN(limit) || limit < 1 || limit > 100) {
      throw new BadRequestException('Limit must be between 1 and 100');
    }
    params.limit = limit;
  }
  
  // Handle where conditions (JSON filtering)
  if (query.where) {
    try {
      params.where = typeof query.where === 'string' 
        ? JSON.parse(query.where) 
        : query.where;
    } catch (e) {
      throw new BadRequestException('Invalid where parameter format. Must be valid JSON');
    }
  }
  
  // Handle search parameter
  if (query.search) {
    params.search = query.search;
  }
  
  // Handle order parameter
  if (query.order) {
    try {
      params.order = typeof query.order === 'string' 
        ? JSON.parse(query.order) 
        : query.order;
      
      // Validate order values
      if (params.order) {
        for (const key of Object.keys(params.order)) {
          if (!['asc', 'desc'].includes(params.order[key])) {
            throw new BadRequestException(`Invalid order direction for field ${key}. Must be 'asc' or 'desc'`);
          }
        }
      }
    } catch (e) {
      throw new BadRequestException('Invalid order parameter format. Must be valid JSON');
    }
  }
  
  return params;
}

/**
 * Apply standard query parameters to MongoDB find options
 * 
 * @param params Standardized query parameters
 * @returns MongoDB options object
 */
export function toMongoDbOptions(params: StandardListParams): Record<string, any> {
  const options: Record<string, any> = {};
  
  // Pagination
  if (params.page !== undefined && params.limit !== undefined) {
    options.skip = (params.page - 1) * params.limit;
    options.limit = params.limit;
  } else if (params.limit !== undefined) {
    options.limit = params.limit;
  }
  
  // Sorting
  if (params.order) {
    options.sort = {};
    for (const [field, direction] of Object.entries(params.order)) {
      options.sort[field] = direction === 'asc' ? 1 : -1;
    }
  }
  
  return options;
}

/**
 * Apply standard query parameters to Prisma findMany options
 * 
 * @param params Standardized query parameters
 * @returns Prisma findMany options
 */
export function toPrismaOptions(params: StandardListParams): Record<string, any> {
  const options: Record<string, any> = {};
  
  // Pagination
  if (params.page !== undefined && params.limit !== undefined) {
    options.skip = (params.page - 1) * params.limit;
    options.take = params.limit;
  } else if (params.limit !== undefined) {
    options.take = params.limit;
  }
  
  // Filtering with where
  if (params.where) {
    options.where = params.where;
  }
  
  // Search could be implemented with a combination of OR conditions
  if (params.search) {
    // This is just an example, might need to be customized based on entity fields
    options.where = {
      ...options.where,
      OR: [
        { name: { contains: params.search, mode: 'insensitive' } },
        { description: { contains: params.search, mode: 'insensitive' } },
      ],
    };
  }
  
  // Sorting
  if (params.order) {
    options.orderBy = {};
    for (const [field, direction] of Object.entries(params.order)) {
      options.orderBy[field] = direction;
    }
  }
  
  return options;
}

/**
 * Generate pagination metadata
 * 
 * @param params Query parameters with pagination
 * @param totalCount Total count of items
 * @returns Pagination metadata
 */
export function getPaginationMetadata(params: StandardListParams, totalCount: number) {
  const page = params.page || 1;
  const limit = params.limit || 10;
  const totalPages = Math.ceil(totalCount / limit);
  
  return {
    page,
    limit,
    totalPages,
    totalCount,
    hasNextPage: page < totalPages,
    hasPreviousPage: page > 1,
  };
}
