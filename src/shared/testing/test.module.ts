import { Module, DynamicModule } from '@nestjs/common';
import { Test, TestingModule } from '@nestjs/testing';
import { ConfigModule } from '@nestjs/config';
import { PrismaService } from '@core/prisma/prisma.service';
import { MongoDbService } from '@infra/mongodb/mongodb.service';
import { RedisService } from '@infra/redis/redis.service';
import { RabbitMQService } from '@infra/rabbitmq/rabbitmq.service';
import { I18nModule } from '@/i18n/i18n.module';

@Module({})
export class TestModule {
  /**
   * Create a testing module with common dependencies
   * 
   * @param imports Additional modules to import
   * @param providers Additional providers to include
   * @returns Dynamic module for testing
   */
  static forRoot(imports: any[] = [], providers: any[] = []): DynamicModule {
    return {
      module: TestModule,
      imports: [
        ConfigModule.forRoot({
          isGlobal: true,
          envFilePath: '.env.test',
        }),
        I18nModule,
        ...imports,
      ],
      providers: [
        PrismaService,
        {
          provide: MongoDbService,
          useValue: {
            find: jest.fn(),
            findOne: jest.fn(),
            insertOne: jest.fn(),
            updateOne: jest.fn(),
            deleteOne: jest.fn(),
            syncDocument: jest.fn(),
          },
        },
        {
          provide: RedisService,
          useValue: {
            get: jest.fn(),
            set: jest.fn(),
            del: jest.fn(),
            flushDb: jest.fn(),
          },
        },
        {
          provide: RabbitMQService,
          useValue: {
            publish: jest.fn(),
            subscribe: jest.fn(),
          },
        },
        ...providers,
      ],
      exports: [
        PrismaService,
        MongoDbService,
        RedisService,
        RabbitMQService,
        ...providers,
      ],
    };
  }

  /**
   * Create a testing module for a specific service
   * 
   * @param service Service to test
   * @param dependencies Dependencies for the service
   * @returns Testing module for the service
   */
  static async createTestingModule(
    service: any,
    dependencies: any[] = [],
  ): Promise<TestingModule> {
    return Test.createTestingModule({
      imports: [
        TestModule.forRoot(),
      ],
      providers: [
        service,
        ...dependencies,
      ],
    }).compile();
  }
}
