import { ValidationPipe } from '@nestjs/common';
import { Test, TestingModule } from '@nestjs/testing';
import { ConfigModule } from '@nestjs/config';
import { INestApplication } from '@nestjs/common';

/**
 * Create a test application instance with common configurations
 * 
 * @param moduleFixture Testing module fixture
 * @returns Configured NestJS application for testing
 */
export async function createTestApp(moduleFixture: TestingModule): Promise<INestApplication> {
  const app = moduleFixture.createNestApplication();
  
  // Set up global pipes and filters for e2e testing
  app.useGlobalPipes(
    new ValidationPipe({
      whitelist: true,
      forbidNonWhitelisted: true,
      transform: true,
    }),
  );
  
  // Apply any additional app configuration here
  
  await app.init();
  return app;
}

/**
 * Create a mock repository with basic CRUD operations
 * 
 * @param entities Initial entities to populate the mock repository
 * @returns Mock repository with common methods
 */
export function createMockRepository<T = any>(entities: T[] = []) {
  let items = [...entities];
  
  return {
    items,
    findOne: jest.fn((id) => Promise.resolve(items.find((item: any) => item.id === id) || null)),
    find: jest.fn(() => Promise.resolve(items)),
    findAll: jest.fn(() => Promise.resolve(items)),
    create: jest.fn((data) => {
      const newItem = { id: String(Date.now()), ...data };
      items.push(newItem);
      return Promise.resolve(newItem);
    }),
    update: jest.fn((id, data) => {
      const index = items.findIndex((item: any) => item.id === id);
      if (index === -1) return Promise.resolve(null);
      
      const updatedItem = { ...items[index], ...data };
      items[index] = updatedItem;
      return Promise.resolve(updatedItem);
    }),
    remove: jest.fn((id) => {
      const index = items.findIndex((item: any) => item.id === id);
      if (index === -1) return Promise.resolve(null);
      
      const [removed] = items.splice(index, 1);
      return Promise.resolve(removed);
    }),
    // For testing purposes
    __resetItems: (newItems: T[] = []) => {
      items = [...newItems];
    },
  };
}

/**
 * Create a mock service with common methods
 * 
 * @param overrides Override specific methods
 * @returns Mock service
 */
export function createMockService(overrides: Record<string, any> = {}) {
  return {
    findOne: jest.fn().mockResolvedValue({}),
    findAll: jest.fn().mockResolvedValue([]),
    create: jest.fn().mockImplementation((dto) => Promise.resolve({ id: 'test-id', ...dto })),
    update: jest.fn().mockImplementation((id, dto) => Promise.resolve({ id, ...dto })),
    remove: jest.fn().mockResolvedValue({ id: 'test-id' }),
    ...overrides,
  };
}

/**
 * Create a test config module that loads the test environment
 * 
 * @returns ConfigModule for testing
 */
export function createTestConfigModule() {
  return ConfigModule.forRoot({
    isGlobal: true,
    envFilePath: '.env.test',
  });
}

/**
 * Generate a random testing ID
 * 
 * @param prefix Optional prefix for the ID
 * @returns Random ID string
 */
export function generateTestId(prefix = 'test') {
  return `${prefix}_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`;
}
