import { Injectable, Logger } from '@nestjs/common';
import { PrismaService } from '@core/prisma/prisma.service';
import { Resources, ResourceActions, createPermission } from '../decorators/permissions.decorator';

/**
 * Permission assignment interface
 */
export interface AssignPermissionDto {
  userId: string;
  permission: string;
}

/**
 * Service to manage user permissions
 */
@Injectable()
export class PermissionsService {
  private readonly logger = new Logger(PermissionsService.name);
  
  constructor(private readonly prismaService: PrismaService) {}
  
  /**
   * Get all permissions for a user
   * 
   * @param userId User ID
   * @returns Array of permission strings
   */
  async getUserPermissions(userId: string): Promise<string[]> {
    try {
      // Get user with roles and direct permissions
      const user = await this.prismaService.user.findUnique({
        where: { id: userId },
      });
      
      if (!user) {
        return [];
      }
      
      // User permissions are stored directly in the permissions array field
      return user.permissions || [];
    } catch (error) {
      this.logger.error(`Failed to get permissions for user ${userId}: ${error.message}`, error.stack);
      return [];
    }
  }
  
  /**
   * Assign a permission to a user directly
   * 
   * @param dto Assignment DTO with userId and permission
   * @returns Boolean indicating success
   */
  async assignPermissionToUser(dto: AssignPermissionDto): Promise<boolean> {
    try {
      // Get current user permissions
      const user = await this.prismaService.user.findUnique({
        where: { id: dto.userId },
        select: { permissions: true }
      });
      
      if (!user) {
        throw new Error(`User ${dto.userId} not found`);
      }
      
      // Add permission if it doesn't exist
      const currentPermissions = user.permissions || [];
      if (!currentPermissions.includes(dto.permission)) {
        await this.prismaService.user.update({
          where: { id: dto.userId },
          data: {
            permissions: {
              push: dto.permission
            }
          }
        });
      }
      
      this.logger.log(`Assigned permission ${dto.permission} to user ${dto.userId}`);
      return true;
    } catch (error) {
      this.logger.error(
        `Failed to assign permission ${dto.permission} to user ${dto.userId}: ${error.message}`, 
        error.stack
      );
      return false;
    }
  }
  
  /**
   * Remove a permission from a user
   * 
   * @param dto DTO with userId and permission
   * @returns Boolean indicating success
   */
  async removePermissionFromUser(dto: AssignPermissionDto): Promise<boolean> {
    try {
      // Get current user permissions
      const user = await this.prismaService.user.findUnique({
        where: { id: dto.userId },
        select: { permissions: true }
      });
      
      if (!user) {
        throw new Error(`User ${dto.userId} not found`);
      }
      
      // Remove permission if it exists
      const currentPermissions = user.permissions || [];
      const updatedPermissions = currentPermissions.filter(p => p !== dto.permission);
      
      await this.prismaService.user.update({
        where: { id: dto.userId },
        data: {
          permissions: updatedPermissions
        }
      });
      
      this.logger.log(`Removed permission ${dto.permission} from user ${dto.userId}`);
      return true;
    } catch (error) {
      this.logger.error(
        `Failed to remove permission ${dto.permission} from user ${dto.userId}: ${error.message}`, 
        error.stack
      );
      return false;
    }
  }
  
  /**
   * Get a list of all available permissions in the system
   * 
   * @returns Array of permission strings
   */
  getAvailablePermissions(): string[] {
    const permissions: string[] = [];
    
    // Generate all resource:action combinations
    Object.values(Resources).forEach(resource => {
      Object.values(ResourceActions).forEach(action => {
        permissions.push(createPermission(resource, action));
      });
    });
    
    // Add special permissions
    permissions.push('*'); // Superadmin permission
    
    return permissions;
  }
  
  /**
   * Check if a user has a specific permission
   * 
   * @param userId User ID
   * @param permission Permission to check
   * @returns Boolean indicating if user has permission
   */
  async userHasPermission(userId: string, permission: string): Promise<boolean> {
    const permissions = await this.getUserPermissions(userId);
    
    // Check for superadmin permission
    if (permissions.includes('*')) {
      return true;
    }
    
    return permissions.includes(permission);
  }
  
  /**
   * Check if a user has all of the specified permissions
   * 
   * @param userId User ID
   * @param permissions Permissions to check
   * @returns Boolean indicating if user has all permissions
   */
  async userHasAllPermissions(userId: string, permissions: string[]): Promise<boolean> {
    const userPermissions = await this.getUserPermissions(userId);
    
    // Check for superadmin permission
    if (userPermissions.includes('*')) {
      return true;
    }
    
    return permissions.every(permission => userPermissions.includes(permission));
  }
  
  /**
   * Check if a user has any of the specified permissions
   * 
   * @param userId User ID
   * @param permissions Permissions to check
   * @returns Boolean indicating if user has any permission
   */
  async userHasAnyPermission(userId: string, permissions: string[]): Promise<boolean> {
    const userPermissions = await this.getUserPermissions(userId);
    
    // Check for superadmin permission
    if (userPermissions.includes('*')) {
      return true;
    }
    
    return permissions.some(permission => userPermissions.includes(permission));
  }
}
