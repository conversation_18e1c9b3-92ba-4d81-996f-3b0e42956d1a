import { Module, Global } from '@nestjs/common';
import { APP_GUARD } from '@nestjs/core';
import { PermissionsService } from './permissions.service';
import { PermissionsGuard } from '../guards/permissions.guard';
import { PrismaService } from '@core/prisma/prisma.service';

/**
 * Global module for permission handling
 * 
 * This module provides the permission system functionality including:
 * - Permission decorators
 * - Permission guard
 * - Permission service
 */
@Global()
@Module({
  providers: [
    PermissionsService,
    PrismaService,
    {
      provide: APP_GUARD,
      useClass: PermissionsGuard,
    },
  ],
  exports: [PermissionsService],
})
export class PermissionsModule {}
