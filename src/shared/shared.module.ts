import { Module, forwardRef } from '@nestjs/common';
import { DecimalModule } from './decimal/decimal.module';
import { ValidationPipe } from './pipes/validation.pipe';
import { TimeoutInterceptor } from './interceptors/timeout.interceptor';
import { TransformInterceptor } from './interceptors/transform.interceptor';
import { ErrorsInterceptor } from './interceptors/errors.interceptor';
import { LoggingInterceptor } from './interceptors/logging.interceptor';
import { APP_PIPE, APP_INTERCEPTOR } from '@nestjs/core';
import { DataTransferModule } from './data-transfer/data-transfer.module';
import { LoggerModule } from '@core/logger/logger.module';
import { EmailService } from './services/email.service';

@Module({
  imports: [
    DecimalModule,
    forwardRef(() => DataTransferModule),
    LoggerModule,
  ],
  providers: [
    {
      provide: APP_PIPE,
      useClass: ValidationPipe,
    },
    {
      provide: APP_INTERCEPTOR,
      useClass: TimeoutInterceptor,
    },
    {
      provide: APP_INTERCEPTOR,
      useClass: TransformInterceptor,
    },
    {
      provide: APP_INTERCEPTOR,
      useClass: ErrorsInterceptor,
    },
    {
      provide: APP_INTERCEPTOR,
      useClass: LoggingInterceptor,
    },
    EmailService,
  ],
  exports: [
    DecimalModule,
    forwardRef(() => DataTransferModule),
    LoggerModule,
    EmailService,
  ],
})
export class SharedModule {}
