import { Injectable, CanActivate, ExecutionContext, ForbiddenException, Logger } from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import { PERMISSIONS_KEY } from '../decorators/permissions.decorator';

/**
 * Guard that enforces permission-based access control
 * 
 * This guard checks if the current user has the required permissions
 * to access the requested endpoint.
 */
@Injectable()
export class PermissionsGuard implements CanActivate {
  private readonly logger = new Logger(PermissionsGuard.name);

  constructor(private readonly reflector: Reflector) {}

  /**
   * Determines if the current request can proceed based on permissions
   * 
   * @param context The execution context
   * @returns Boolean indicating if access is allowed
   */
  canActivate(context: ExecutionContext): boolean {
    // Check if the route is marked as public
    // TODO:end redis deerees permission shalgadag bolgii
    const isPublic = this.reflector.getAllAndOverride<boolean>(
      'isPublic',
      [context.getHandler(), context.getClass()]
    );

    if (isPublic) {
      return true;
    }

    // Get permissions required via OR logic (any permission grants access)
    const orPermissions = this.reflector.getAllAndOverride<string[]>(
      PERMISSIONS_KEY,
      [context.getHandler(), context.getClass()]
    );

    // Get permissions required via AND logic (all permissions needed)
    const andPermissions = this.reflector.getAllAndOverride<string[]>(
      `${PERMISSIONS_KEY}:all`,
      [context.getHandler(), context.getClass()]
    );

    // If no permissions are required, allow access by default
    if (!orPermissions && !andPermissions) {
      return true;
    }

    // Get the request and extract the user
    const request = context.switchToHttp().getRequest();
    const user = request.user;

    if (!user) {
      this.logger.warn('No user found in request when checking permissions');
      throw new ForbiddenException('Access denied: authentication required');
    }

    // Check permissions
    if (orPermissions && orPermissions.length > 0) {
      const hasAnyPermission = this.checkOrPermissions(user, orPermissions);
      if (!hasAnyPermission) {
        this.logger.warn(`User ${user.id} lacks required permissions: ${orPermissions.join(', ')}`);
        throw new ForbiddenException('Insufficient permissions');
      }
    }

    if (andPermissions && andPermissions.length > 0) {
      const hasAllPermissions = this.checkAndPermissions(user, andPermissions);
      if (!hasAllPermissions) {
        this.logger.warn(`User ${user.id} lacks all required permissions: ${andPermissions.join(', ')}`);
        throw new ForbiddenException('Insufficient permissions');
      }
    }

    return true;
  }

  /**
   * Checks if the user has any of the specified permissions (OR logic)
   * 
   * @param user The user object
   * @param permissions The list of permissions to check
   * @returns Boolean indicating if the user has any permission
   */
  private checkOrPermissions(user: any, permissions: string[]): boolean {
    // If user has * (superadmin permission), always allow
    if (user.permissions && user.permissions.includes('*')) {
      return true;
    }

    // Check if user has any of the required permissions
    return permissions.some(permission => 
      user.permissions && user.permissions.includes(permission)
    );
  }

  /**
   * Checks if the user has all specified permissions (AND logic)
   * 
   * @param user The user object
   * @param permissions The list of permissions to check
   * @returns Boolean indicating if the user has all permissions
   */
  private checkAndPermissions(user: any, permissions: string[]): boolean {
    // If user has * (superadmin permission), always allow
    if (user.permissions && user.permissions.includes('*')) {
      return true;
    }

    // Check if user has all required permissions
    return permissions.every(permission => 
      user.permissions && user.permissions.includes(permission)
    );
  }
}
