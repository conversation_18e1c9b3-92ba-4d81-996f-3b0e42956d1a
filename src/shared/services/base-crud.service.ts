import { Logger, NotFoundException, ConflictException } from '@nestjs/common';
import { PrismaService } from '@core/prisma/prisma.service';
import { MongoDbService } from '@infra/mongodb/mongodb.service';
import { RabbitMQService } from '@infra/rabbitmq/rabbitmq.service';
import { WebsocketService } from '@infra/websocket/websocket.service';

/**
 * Base CRUD service with common patterns
 */
export abstract class BaseCrudService<T, CreateDto, UpdateDto> {
  protected readonly logger: Logger;
  protected abstract readonly modelName: string;
  protected abstract readonly collectionName: string;

  constructor(
    protected readonly prisma: PrismaService,
    protected readonly mongoDbService: MongoDbService,
    protected readonly rabbitMQService: RabbitMQService,
    protected readonly websocketService: WebsocketService,
  ) {
    this.logger = new Logger(this.constructor.name);
  }

  /**
   * Create a new entity with standard patterns
   */
  protected async createEntity(
    data: CreateDto,
    createdBy?: string,
    additionalData?: any,
  ): Promise<T> {
    try {
      const entity = await this.performCreate({
        ...data,
        ...additionalData,
        createdBy,
      });

      // Sync to MongoDB
      await this.syncToMongoDB(entity);

      // Send notifications
      await this.notifyEntityCreated(entity);

      // Publish event
      await this.publishEvent('created', { entity });

      return entity;
    } catch (error) {
      this.logger.error(`Failed to create ${this.modelName}: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Get entities with pagination and filtering
   */
  protected async getEntities(
    where: any = {},
    include: any = {},
    page = 1,
    limit = 20,
    orderBy: any = { createdAt: 'desc' },
  ): Promise<{ items: T[]; total: number; page: number; totalPages: number }> {
    try {
      const [items, total] = await Promise.all([
        this.performFindMany({
          where,
          include,
          orderBy,
          skip: (page - 1) * limit,
          take: limit,
        }),
        this.performCount({ where }),
      ]);

      return {
        items,
        total,
        page,
        totalPages: Math.ceil(total / limit),
      };
    } catch (error) {
      this.logger.error(`Failed to get ${this.modelName}s: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Get entity by ID with error handling
   */
  protected async getEntityById(
    id: string,
    include: any = {},
    errorMessage?: string,
  ): Promise<T> {
    try {
      const entity = await this.performFindUnique({
        where: { id },
        include,
      });

      if (!entity) {
        throw new NotFoundException(
          errorMessage || `${this.modelName} with ID ${id} not found`
        );
      }

      return entity;
    } catch (error) {
      this.logger.error(`Failed to get ${this.modelName}: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Update entity with standard patterns
   */
  protected async updateEntity(
    id: string,
    data: UpdateDto,
    updatedBy?: string,
    include: any = {},
  ): Promise<T> {
    try {
      const existingEntity = await this.getEntityById(id);

      const entity = await this.performUpdate({
        where: { id },
        data: {
          ...data,
          updatedBy,
        },
        include,
      });

      // Sync to MongoDB
      await this.syncToMongoDB(entity);

      // Send notifications
      await this.notifyEntityUpdated(entity, existingEntity);

      // Publish event
      await this.publishEvent('updated', { entity, previousData: existingEntity });

      return entity;
    } catch (error) {
      this.logger.error(`Failed to update ${this.modelName}: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Delete entity with validation
   */
  protected async deleteEntity(
    id: string,
    validationCallback?: (entity: T) => Promise<void>,
  ): Promise<void> {
    try {
      const entity = await this.getEntityById(id);

      // Run custom validation if provided
      if (validationCallback) {
        await validationCallback(entity);
      }

      await this.performDelete({ where: { id } });

      // Remove from MongoDB
      await this.mongoDbService.deleteDocument(this.collectionName, id);

      // Publish event
      await this.publishEvent('deleted', { entityId: id, entity });

      this.logger.log(`${this.modelName} ${id} deleted successfully`);
    } catch (error) {
      this.logger.error(`Failed to delete ${this.modelName}: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Check for unique constraint violations
   */
  protected async checkUniqueness(
    field: string,
    value: any,
    excludeId?: string,
    errorMessage?: string,
  ): Promise<void> {
    const where: any = { [field]: value };
    if (excludeId) {
      where.id = { not: excludeId };
    }

    const existing = await this.performFindFirst({ where });
    if (existing) {
      throw new ConflictException(
        errorMessage || `${this.modelName} with ${field} ${value} already exists`
      );
    }
  }

  /**
   * Generate unique code with prefix
   */
  protected async generateUniqueCode(prefix: string, length = 6): Promise<string> {
    const count = await this.performCount({});
    return `${prefix}-${String(count + 1).padStart(length, '0')}`;
  }

  /**
   * Sync entity to MongoDB
   */
  protected async syncToMongoDB(entity: T): Promise<void> {
    try {
      await this.mongoDbService.syncDocument(this.collectionName, entity);
    } catch (error) {
      this.logger.warn(`Failed to sync ${this.modelName} to MongoDB: ${error.message}`);
      // Don't throw error as this is not critical
    }
  }

  /**
   * Publish event to message queue
   */
  protected async publishEvent(action: string, data: any): Promise<void> {
    try {
      await this.rabbitMQService.publish({
        type: `${this.modelName.toLowerCase()}_${action}`,
        data,
      });
    } catch (error) {
      this.logger.warn(`Failed to publish ${this.modelName} event: ${error.message}`);
      // Don't throw error as this is not critical
    }
  }

  /**
   * Send notification for entity creation
   */
  protected async notifyEntityCreated(entity: T): Promise<void> {
    // Override in subclasses for specific notification logic
  }

  /**
   * Send notification for entity update
   */
  protected async notifyEntityUpdated(entity: T, previousEntity: T): Promise<void> {
    // Override in subclasses for specific notification logic
  }

  // Abstract methods to be implemented by subclasses
  protected abstract performCreate(data: any): Promise<T>;
  protected abstract performFindMany(params: any): Promise<T[]>;
  protected abstract performFindUnique(params: any): Promise<T | null>;
  protected abstract performFindFirst(params: any): Promise<T | null>;
  protected abstract performUpdate(params: any): Promise<T>;
  protected abstract performDelete(params: any): Promise<T>;
  protected abstract performCount(params: any): Promise<number>;
}

/**
 * Utility functions for common operations
 */
export class CrudUtils {
  /**
   * Build where clause for search functionality
   */
  static buildSearchWhere(search: string, fields: string[]): any {
    if (!search) return {};

    return {
      OR: fields.map(field => ({
        [field]: {
          contains: search,
          mode: 'insensitive',
        },
      })),
    };
  }

  /**
   * Build date range filter
   */
  static buildDateRangeWhere(
    startDate?: Date,
    endDate?: Date,
    field = 'createdAt',
  ): any {
    if (!startDate && !endDate) return {};

    const dateFilter: any = {};
    if (startDate) dateFilter.gte = startDate;
    if (endDate) dateFilter.lte = endDate;

    return { [field]: dateFilter };
  }

  /**
   * Merge multiple where conditions
   */
  static mergeWhereConditions(...conditions: any[]): any {
    return conditions.reduce((merged, condition) => {
      if (!condition || Object.keys(condition).length === 0) return merged;
      return { ...merged, ...condition };
    }, {});
  }

  /**
   * Validate pagination parameters
   */
  static validatePagination(page?: number, limit?: number) {
    const validatedPage = Math.max(1, page || 1);
    const validatedLimit = Math.min(100, Math.max(1, limit || 20));
    
    return {
      page: validatedPage,
      limit: validatedLimit,
      skip: (validatedPage - 1) * validatedLimit,
      take: validatedLimit,
    };
  }

  /**
   * Create standardized response format
   */
  static createListResponse<T>(
    data: T[],
    total: number,
    page: number,
    limit: number,
  ) {
    return {
      data,
      meta: {
        total,
        page,
        limit,
        totalPages: Math.ceil(total / limit),
      },
    };
  }
}
