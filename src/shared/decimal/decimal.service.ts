import { Injectable } from '@nestjs/common';
import Decimal from 'decimal.js';

@Injectable()
export class DecimalService {
  // Configure Decimal.js for precision and rounding
  constructor() {
    // Set precision and rounding mode for financial operations
    Decimal.set({ precision: 20, rounding: Decimal.ROUND_HALF_UP });
  }

  create(value: string | number): Decimal {
    return new Decimal(value);
  }

  add(a: string | number, b: string | number): Decimal {
    return new Decimal(a).plus(new Decimal(b));
  }

  subtract(a: string | number, b: string | number): Decimal {
    return new Decimal(a).minus(new Decimal(b));
  }

  multiply(a: string | number, b: string | number): Decimal {
    return new Decimal(a).times(new Decimal(b));
  }

  divide(a: string | number, b: string | number): Decimal {
    return new Decimal(a).dividedBy(new Decimal(b));
  }

  // Format as currency string with specified decimal places
  formatCurrency(value: string | number | Decimal, decimalPlaces = 2): string {
    return new Decimal(value).toFixed(decimalPlaces);
  }

  // Round to specified decimal places
  round(value: string | number | Decimal, decimalPlaces = 2): Decimal {
    return new Decimal(value).toDecimalPlaces(decimalPlaces);
  }

  // Convert Decimal to JSON-safe string
  toJSON(value: Decimal): string {
    return value.toString();
  }

  // Compare two values (return -1, 0, 1)
  compare(a: string | number | Decimal, b: string | number | Decimal): number {
    return new Decimal(a).comparedTo(new Decimal(b));
  }
}
