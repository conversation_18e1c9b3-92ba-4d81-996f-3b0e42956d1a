import { Injectable, Logger, BadRequestException } from '@nestjs/common';
import { PrismaService } from '@core/prisma/prisma.service';
import { MongoDbService } from '@infra/mongodb/mongodb.service';
import { RabbitMQService } from '@infra/rabbitmq/rabbitmq.service';
import { EventEmitter2 } from '@nestjs/event-emitter';
import * as XLSX from 'xlsx';
import * as xml2js from 'xml2js';
import { validate } from 'class-validator';
import { plainToInstance } from 'class-transformer';
import { Readable } from 'stream';

export enum ImportFormat {
  JSON = 'json',
  XML = 'xml',
  XLSX = 'xlsx',
}

export interface ImportOptions {
  format: ImportFormat;
  entityType: string;
  validateData?: boolean;
  batchSize?: number;
  dtoClass?: any;
  skipRows?: number;
  worksheet?: string;
  dateFields?: string[];
  numberFields?: string[];
  booleanFields?: string[];
}

export interface ImportProgress {
  id: string;
  total: number;
  processed: number;
  succeeded: number;
  failed: number;
  errors: Array<{ index: number; message: string }>;
  status: 'pending' | 'processing' | 'completed' | 'failed';
  startTime: Date;
  endTime?: Date;
}

@Injectable()
export class DataImportService {
  private readonly logger = new Logger(DataImportService.name);
  private readonly activeImports = new Map<string, ImportProgress>();

  constructor(
    private readonly prismaService: PrismaService,
    private readonly mongoDbService: MongoDbService,
    private readonly rabbitMQService: RabbitMQService,
    private readonly eventEmitter: EventEmitter2,
  ) {}

  /**
   * Import data from buffer based on format
   * 
   * @param buffer File buffer to import
   * @param options Import options
   * @returns Import job ID for tracking progress
   */
  async importFromBuffer(buffer: Buffer, options: ImportOptions): Promise<string> {
    try {
      const importId = `import_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`;
      
      // Parse data based on format
      const data = await this.parseData(buffer, options);
      
      if (!Array.isArray(data) || data.length === 0) {
        throw new BadRequestException('Invalid data format or empty data');
      }
      
      // Setup progress tracking
      const importProgress: ImportProgress = {
        id: importId,
        total: data.length,
        processed: 0,
        succeeded: 0,
        failed: 0,
        errors: [],
        status: 'pending',
        startTime: new Date(),
      };
      
      this.activeImports.set(importId, importProgress);
      
      // Process data asynchronously
      this.processImportAsync(importId, data, options);
      
      return importId;
    } catch (error) {
      this.logger.error(`Import failed: ${error.message}`, error.stack);
      throw new BadRequestException(`Failed to import data: ${error.message}`);
    }
  }

  /**
   * Import data from a readable stream
   * 
   * @param stream Readable stream containing data
   * @param options Import options
   * @returns Import job ID for tracking progress
   */
  async importFromStream(stream: Readable, options: ImportOptions): Promise<string> {
    return new Promise((resolve, reject) => {
      const chunks: Buffer[] = [];
      
      stream.on('data', (chunk) => chunks.push(Buffer.from(chunk)));
      stream.on('error', (error) => reject(error));
      stream.on('end', async () => {
        try {
          const buffer = Buffer.concat(chunks);
          const importId = await this.importFromBuffer(buffer, options);
          resolve(importId);
        } catch (error) {
          reject(error);
        }
      });
    });
  }

  /**
   * Get import progress by ID
   * 
   * @param importId Import job ID
   * @returns Current import progress
   */
  getImportProgress(importId: string): ImportProgress {
    const progress = this.activeImports.get(importId);
    
    if (!progress) {
      throw new BadRequestException(`Import job ${importId} not found`);
    }
    
    return { ...progress };
  }

  /**
   * Parse data based on format
   * 
   * @param buffer File buffer
   * @param options Import options
   * @returns Parsed data as array
   */
  private async parseData(buffer: Buffer, options: ImportOptions): Promise<any[]> {
    switch (options.format) {
      case ImportFormat.JSON:
        return this.parseJsonData(buffer);
      case ImportFormat.XML:
        return this.parseXmlData(buffer);
      case ImportFormat.XLSX:
        return this.parseXlsxData(buffer, options);
      default:
        throw new BadRequestException(`Unsupported import format: ${options.format}`);
    }
  }

  /**
   * Parse JSON data
   * 
   * @param buffer JSON buffer
   * @returns Parsed JSON data
   */
  private parseJsonData(buffer: Buffer): any[] {
    try {
      const data = JSON.parse(buffer.toString('utf-8'));
      
      if (Array.isArray(data)) {
        return data;
      } else if (data && typeof data === 'object') {
        // Try to find array property in root object
        const arrayProps = Object.keys(data).filter(key => Array.isArray(data[key]));
        
        if (arrayProps.length > 0) {
          // Use the first array property found
          return data[arrayProps[0]];
        } else {
          // Wrap single object in array
          return [data];
        }
      }
      
      throw new BadRequestException('Invalid JSON format: expected array or object with array property');
    } catch (error) {
      this.logger.error(`Failed to parse JSON: ${error.message}`, error.stack);
      throw new BadRequestException(`Invalid JSON format: ${error.message}`);
    }
  }

  /**
   * Parse XML data
   * 
   * @param buffer XML buffer
   * @returns Parsed XML data
   */
  private async parseXmlData(buffer: Buffer): Promise<any[]> {
    try {
      const parser = new xml2js.Parser({
        explicitArray: false,
        trim: true,
        explicitRoot: false,
        mergeAttrs: true,
        attrNameProcessors: [(name) => name.replace('$', '')],
      });
      
      const data = await parser.parseStringPromise(buffer.toString('utf-8'));
      
      if (Array.isArray(data)) {
        return data;
      } else if (data && typeof data === 'object') {
        // Try to find array property in root object
        const arrayProps = Object.keys(data).filter(key => Array.isArray(data[key]));
        
        if (arrayProps.length > 0) {
          // Use the first array property found
          return data[arrayProps[0]];
        } else {
          // Wrap single object in array
          return [data];
        }
      }
      
      throw new BadRequestException('Invalid XML format: expected array or object with array property');
    } catch (error) {
      this.logger.error(`Failed to parse XML: ${error.message}`, error.stack);
      throw new BadRequestException(`Invalid XML format: ${error.message}`);
    }
  }

  /**
   * Parse XLSX data
   * 
   * @param buffer XLSX buffer
   * @param options Import options
   * @returns Parsed XLSX data
   */
  private parseXlsxData(buffer: Buffer, options: ImportOptions): any[] {
    try {
      const workbook = XLSX.read(buffer, { type: 'buffer' });
      
      // Get worksheet name (use specified or first sheet)
      const worksheetName = options.worksheet || workbook.SheetNames[0];
      
      if (!worksheetName || !workbook.Sheets[worksheetName]) {
        throw new BadRequestException('Invalid XLSX file: worksheet not found');
      }
      
      // Parse worksheet to JSON
      const data = XLSX.utils.sheet_to_json(workbook.Sheets[worksheetName], {
        raw: false,
        dateNF: 'yyyy-mm-dd',
        defval: null,
      });
      
      // Skip rows if specified
      const skipRows = options.skipRows || 0;
      const skippedData = skipRows > 0 ? data.slice(skipRows) : data;
      
      // Convert field types
      return this.convertFieldTypes(skippedData, options);
    } catch (error) {
      this.logger.error(`Failed to parse XLSX: ${error.message}`, error.stack);
      throw new BadRequestException(`Invalid XLSX format: ${error.message}`);
    }
  }

  /**
   * Convert field types based on options
   * 
   * @param data Raw data
   * @param options Import options with field type specifications
   * @returns Data with converted field types
   */
  private convertFieldTypes(data: any[], options: ImportOptions): any[] {
    if (!data || data.length === 0) {
      return data;
    }
    
    const { dateFields = [], numberFields = [], booleanFields = [] } = options;
    
    return data.map(item => {
      const convertedItem = { ...item };
      
      // Convert date fields
      dateFields.forEach(field => {
        if (convertedItem[field] !== undefined && convertedItem[field] !== null) {
          try {
            convertedItem[field] = new Date(convertedItem[field]);
          } catch (error) {
            // Keep original value if conversion fails
          }
        }
      });
      
      // Convert number fields
      numberFields.forEach(field => {
        if (convertedItem[field] !== undefined && convertedItem[field] !== null) {
          const num = parseFloat(convertedItem[field]);
          if (!isNaN(num)) {
            convertedItem[field] = num;
          }
        }
      });
      
      // Convert boolean fields
      booleanFields.forEach(field => {
        if (convertedItem[field] !== undefined && convertedItem[field] !== null) {
          const value = String(convertedItem[field]).toLowerCase();
          if (['true', 'yes', '1', 'y'].includes(value)) {
            convertedItem[field] = true;
          } else if (['false', 'no', '0', 'n'].includes(value)) {
            convertedItem[field] = false;
          }
        }
      });
      
      return convertedItem;
    });
  }

  /**
   * Process import data asynchronously
   * 
   * @param importId Import job ID
   * @param data Data to import
   * @param options Import options
   */
  private async processImportAsync(importId: string, data: any[], options: ImportOptions): Promise<void> {
    try {
      const progress = this.activeImports.get(importId);
      
      if (!progress) {
        throw new Error(`Import job ${importId} not found`);
      }
      
      // Update status to processing
      progress.status = 'processing';
      this.activeImports.set(importId, progress);
      
      // Define batch size
      const batchSize = options.batchSize || 100;
      
      // Process in batches
      for (let i = 0; i < data.length; i += batchSize) {
        const batch = data.slice(i, i + batchSize);
        
        // Process each item in batch
        for (const [index, item] of batch.entries()) {
          const globalIndex = i + index;
          
          try {
            // Validate item if requested
            if (options.validateData && options.dtoClass) {
              const dto = plainToInstance(options.dtoClass, item);
              const errors = await validate(dto);
              
              if (errors.length > 0) {
                const errorMessages = errors.map(err => 
                  Object.values(err.constraints || {}).join(', ')
                ).join('; ');
                
                throw new Error(`Validation failed: ${errorMessages}`);
              }
            }
            
            // Save to database based on entity type
            await this.saveEntity(options.entityType, item);
            
            // Update progress
            progress.processed++;
            progress.succeeded++;
          } catch (error) {
            // Record error
            progress.processed++;
            progress.failed++;
            progress.errors.push({
              index: globalIndex,
              message: error.message,
            });
            
            this.logger.error(`Error processing import item ${globalIndex}: ${error.message}`);
          }
          
          // Update progress in map
          this.activeImports.set(importId, progress);
          
          // Emit progress event
          this.eventEmitter.emit('import.progress', {
            importId,
            progress: { ...progress },
          });
        }
      }
      
      // Mark as completed
      progress.status = progress.failed > 0 ? 'completed' : 'completed';
      progress.endTime = new Date();
      this.activeImports.set(importId, progress);
      
      // Emit completion event
      this.eventEmitter.emit('import.completed', {
        importId,
        progress: { ...progress },
      });
      
      // Publish to RabbitMQ
      await this.rabbitMQService.publish({
        type: 'import.completed',
        data: {
          importId,
          progress: { ...progress },
        },
      });
      
      this.logger.log(`Import job ${importId} completed: ${progress.succeeded} succeeded, ${progress.failed} failed`);
    } catch (error) {
      // Mark as failed
      const progress = this.activeImports.get(importId);
      
      if (progress) {
        progress.status = 'failed';
        progress.endTime = new Date();
        this.activeImports.set(importId, progress);
        
        // Emit failure event
        this.eventEmitter.emit('import.failed', {
          importId,
          progress: { ...progress },
          error: error.message,
        });
      }
      
      this.logger.error(`Import job ${importId} failed: ${error.message}`, error.stack);
    }
  }

  /**
   * Save entity to appropriate database
   * 
   * @param entityType Type of entity to save
   * @param data Entity data
   */
  private async saveEntity(entityType: string, data: any): Promise<void> {
    // For primary PostgreSQL database (source of truth)
    const prismaTable = this.getPrismaTableForEntity(entityType);
    
    try {
      // Save to PostgreSQL
      const result = await this.prismaService[prismaTable].create({
        data,
      });
      
      // Sync to MongoDB if successful
      if (result) {
        await this.mongoDbService.syncDocument(entityType, result);
      }
    } catch (error) {
      this.logger.error(`Failed to save entity ${entityType}: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Get Prisma table name for entity type
   * 
   * @param entityType Entity type
   * @returns Prisma table name
   */
  private getPrismaTableForEntity(entityType: string): string {
    // Map entity types to Prisma table names
    const entityMap: Record<string, string> = {
      users: 'user',
      products: 'product',
      categories: 'productCategory',
      orders: 'order',
      orderItems: 'orderItem',
      transactions: 'transaction',
      invoices: 'invoice',
    };
    
    const table = entityMap[entityType] || entityType;
    
    if (!this.prismaService[table]) {
      throw new BadRequestException(`Unsupported entity type: ${entityType}`);
    }
    
    return table;
  }
}
