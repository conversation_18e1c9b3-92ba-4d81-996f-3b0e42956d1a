import { Module, forwardRef } from '@nestjs/common';
import { DataImportService } from './data-import.service';
import { DataExportService } from './data-export.service';
import { ImportController } from './import.controller';
import { ExportController } from './export.controller';
import { PrismaModule } from '@core/prisma/prisma.module';
import { MongoDbModule } from '@infra/mongodb/mongodb.module';
import { SharedModule } from '@shared/shared.module';
import { RabbitMQModule } from '@infra/rabbitmq/rabbitmq.module';
import { EventEmitterModule } from '@nestjs/event-emitter';

@Module({
  imports: [
    forwardRef(() => SharedModule),
    PrismaModule,
    MongoDbModule,
    RabbitMQModule,
    EventEmitterModule.forRoot(),
  ],
  controllers: [
    ImportController,
    ExportController,
  ],
  providers: [
    DataImportService,
    DataExportService,
  ],
  exports: [
    DataImportService,
    DataExportService,
  ],
})
export class DataTransferModule {}
