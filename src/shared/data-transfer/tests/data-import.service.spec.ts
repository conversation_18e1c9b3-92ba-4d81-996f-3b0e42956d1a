import { Test, TestingModule } from '@nestjs/testing';
import { BadRequestException } from '@nestjs/common';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { Readable } from 'stream';
import { DataImportService, ImportFormat } from '../data-import.service';
import { mockProducts } from './import-export-mocks';
import { PrismaService } from '@core/prisma/prisma.service';
import { MongoDbService } from '@infra/mongodb/mongodb.service';
import { RabbitMQService } from '@infra/rabbitmq/rabbitmq.service';

// Use explicit class references
class MockPrismaService {
  user = {
    create: jest.fn().mockResolvedValue({ id: 'user-1', email: '<EMAIL>' }),
  };
  product = {
    create: jest.fn().mockResolvedValue({ id: 'product-1', name: 'Test Product' }),
  };
  $queryRaw: jest.Mock = jest.fn().mockResolvedValue([1]);
}

class MockMongoDbService {
  syncDocument = jest.fn().mockResolvedValue(true);
  find = jest.fn().mockResolvedValue(mockProducts);
  findOne = jest.fn().mockResolvedValue({ id: 'test-1', name: 'Test' });
}

class MockRabbitMQService {
  publish = jest.fn().mockResolvedValue(true);
  subscribe = jest.fn().mockImplementation(() => Promise.resolve());
}

describe('DataImportService', () => {
  let service: DataImportService;
  let mockPrismaService: MockPrismaService;
  let mockMongoDbService: MockMongoDbService;
  let mockRabbitMQService: MockRabbitMQService;
  let mockEventEmitter: EventEmitter2;

  beforeEach(async () => {
    mockPrismaService = new MockPrismaService();
    mockMongoDbService = new MockMongoDbService();
    mockRabbitMQService = new MockRabbitMQService();
    mockEventEmitter = { emit: jest.fn() } as any;

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        DataImportService,
        {
          provide: PrismaService,
          useValue: mockPrismaService
        },
        {
          provide: MongoDbService, 
          useValue: mockMongoDbService
        },
        {
          provide: RabbitMQService,
          useValue: mockRabbitMQService
        },
        {
          provide: EventEmitter2,
          useValue: mockEventEmitter
        },
      ],
    }).compile();

    service = module.get<DataImportService>(DataImportService);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('importFromBuffer', () => {
    it('should process JSON data successfully', async () => {
      // Mock JSON data
      const jsonData = [
        { name: 'Test User 1', email: '<EMAIL>' },
        { name: 'Test User 2', email: '<EMAIL>' },
      ];
      const buffer = Buffer.from(JSON.stringify(jsonData));

      // Mock private methods
      jest.spyOn<any, any>(service, 'parseData').mockResolvedValueOnce(jsonData);
      jest.spyOn<any, any>(service, 'processImportAsync').mockImplementationOnce(() => {});

      const importId = await service.importFromBuffer(buffer, {
        format: ImportFormat.JSON,
        entityType: 'users',
      });

      expect(importId).toMatch(/^import_\d+_[a-z0-9]+$/);
      expect(service['parseData']).toHaveBeenCalledWith(buffer, {
        format: ImportFormat.JSON,
        entityType: 'users',
      });
      expect(service['processImportAsync']).toHaveBeenCalled();
    });

    it('should throw BadRequestException for empty data', async () => {
      const buffer = Buffer.from('[]');
      
      jest.spyOn<any, any>(service, 'parseData').mockResolvedValueOnce([]);

      await expect(
        service.importFromBuffer(buffer, {
          format: ImportFormat.JSON,
          entityType: 'users',
        })
      ).rejects.toThrow(BadRequestException);
    });
  });

  describe('importFromStream', () => {
    it('should convert stream to buffer and call importFromBuffer', async () => {
      // Create a readable stream with JSON data
      const jsonData = [{ name: 'Stream Test', email: '<EMAIL>' }];
      const stream = Readable.from(Buffer.from(JSON.stringify(jsonData)));
      
      // Mock importFromBuffer
      jest.spyOn(service, 'importFromBuffer').mockResolvedValueOnce('import_test_123');

      const importId = await service.importFromStream(stream, {
        format: ImportFormat.JSON,
        entityType: 'users',
      });

      expect(importId).toBe('import_test_123');
      expect(service.importFromBuffer).toHaveBeenCalled();
    });
  });

  describe('getImportProgress', () => {
    it('should return import progress for valid ID', () => {
      const testProgress = {
        id: 'import_test_123',
        total: 10,
        processed: 5,
        succeeded: 4,
        failed: 1,
        errors: [{ index: 2, message: 'Test error' }],
        status: 'processing' as const,
        startTime: new Date(),
      };

      // Set mock progress in the active imports map
      service['activeImports'].set('import_test_123', testProgress);

      const progress = service.getImportProgress('import_test_123');
      
      expect(progress).toEqual(testProgress);
    });

    it('should throw BadRequestException for invalid import ID', () => {
      expect(() => service.getImportProgress('invalid_id')).toThrow(BadRequestException);
    });
  });

  describe('parseData', () => {
    it('should parse JSON data correctly', async () => {
      const jsonData = [{ name: 'JSON Test', email: '<EMAIL>' }];
      const buffer = Buffer.from(JSON.stringify(jsonData));

      jest.spyOn<any, any>(service, 'parseJsonData').mockReturnValueOnce(jsonData);

      const result = await service['parseData'](buffer, {
        format: ImportFormat.JSON,
        entityType: 'users',
      });

      expect(result).toEqual(jsonData);
      expect(service['parseJsonData']).toHaveBeenCalledWith(buffer);
    });

    it('should throw BadRequestException for unsupported format', async () => {
      const buffer = Buffer.from('test');

      await expect(
        service['parseData'](buffer, {
          format: 'unsupported' as any,
          entityType: 'users',
        })
      ).rejects.toThrow(BadRequestException);
    });
  });
});
