import { Test, TestingModule } from '@nestjs/testing';
import { BadRequestException } from '@nestjs/common';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { DataExportService, ExportFormat } from '../data-export.service';
import { mockProducts } from './import-export-mocks';
import { PrismaService } from '@core/prisma/prisma.service';
import { MongoDbService } from '@infra/mongodb/mongodb.service';

// Use explicit class references
class MockPrismaService {
  product = {
    findMany: jest.fn().mockResolvedValue([
      { id: 'product-1', name: 'Product 1', price: 100, stock: 10 },
      { id: 'product-2', name: 'Product 2', price: 200, stock: 20 },
    ]),
  };
  user = {
    findMany: jest.fn().mockResolvedValue([
      { id: 'user-1', email: '<EMAIL>' },
    ]),
  };
  $queryRaw = jest.fn().mockResolvedValue([1]);
}

class MockMongoDbService {
  find = jest.fn().mockResolvedValue([
    { id: 'product-1', name: 'Product 1', price: 100, stock: 10 },
    { id: 'product-2', name: 'Product 2', price: 200, stock: 20 },
  ]);
  findOne = jest.fn().mockResolvedValue({ id: 'test-1', name: 'Test' });
  insertOne = jest.fn().mockResolvedValue({ id: 'test-1' });
  updateOne = jest.fn().mockResolvedValue(true);
  deleteOne = jest.fn().mockResolvedValue(true);
  syncDocument = jest.fn().mockResolvedValue(true);
  count = jest.fn().mockResolvedValue(10);
  aggregate = jest.fn().mockResolvedValue([
    { _id: '2023-01', count: 5, revenue: 500, avgOrderValue: 100 }
  ]);
}

class MockRedisService {
  get = jest.fn().mockResolvedValue(null);
  set = jest.fn().mockResolvedValue(true);
  del = jest.fn().mockResolvedValue(true);
  flushDb = jest.fn().mockResolvedValue(true);
}

describe('DataExportService', () => {
  let service: DataExportService;
  let mockMongoDbService: MockMongoDbService;
  let mockPrismaService: MockPrismaService;
  let mockEventEmitter: EventEmitter2;

  beforeEach(async () => {
    mockPrismaService = new MockPrismaService();
    mockMongoDbService = new MockMongoDbService();
    mockEventEmitter = { emit: jest.fn() } as any;

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        DataExportService,
        { 
          provide: PrismaService, 
          useValue: mockPrismaService 
        },
        { 
          provide: MongoDbService, 
          useValue: mockMongoDbService 
        },
        { 
          provide: EventEmitter2, 
          useValue: mockEventEmitter 
        },
      ],
    }).compile();

    service = module.get<DataExportService>(DataExportService);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('exportData', () => {
    it('should export data as JSON successfully', async () => {
      // Mock private methods
      jest.spyOn<any, any>(service, 'fetchData').mockResolvedValueOnce(mockProducts);
      jest.spyOn<any, any>(service, 'formatJson').mockReturnValueOnce(Buffer.from(JSON.stringify(mockProducts)));

      const result = await service.exportData({
        format: ExportFormat.JSON,
        entityType: 'products',
      });

      expect(result).toBeDefined();
      expect(result.mimetype).toBe('application/json');
      expect(result.buffer).toBeDefined();
      expect(result.filename).toMatch(/products_export_.*\.json/);
      expect(service['fetchData']).toHaveBeenCalledWith({
        format: ExportFormat.JSON,
        entityType: 'products',
      });
      expect(service['formatJson']).toHaveBeenCalledWith(mockProducts);
      expect(mockEventEmitter.emit).toHaveBeenCalledWith('export.completed', expect.any(Object));
    });

    it('should export data as XLSX successfully', async () => {
      // Mock private methods
      jest.spyOn<any, any>(service, 'fetchData').mockResolvedValueOnce(mockProducts);
      jest.spyOn<any, any>(service, 'formatXlsx').mockReturnValueOnce(Buffer.from('xlsx-mock-data'));

      const result = await service.exportData({
        format: ExportFormat.XLSX,
        entityType: 'products',
        fields: ['id', 'name', 'price'],
        includeHeaders: true,
      });

      expect(result).toBeDefined();
      expect(result.mimetype).toBe('application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
      expect(result.buffer).toBeDefined();
      expect(result.filename).toMatch(/products_export_.*\.xlsx/);
      expect(service['fetchData']).toHaveBeenCalledWith({
        format: ExportFormat.XLSX,
        entityType: 'products',
        fields: ['id', 'name', 'price'],
        includeHeaders: true,
      });
      expect(service['formatXlsx']).toHaveBeenCalledWith(mockProducts, {
        format: ExportFormat.XLSX,
        entityType: 'products',
        fields: ['id', 'name', 'price'],
        includeHeaders: true,
      });
    });

    it('should throw BadRequestException for empty data', async () => {
      jest.spyOn<any, any>(service, 'fetchData').mockResolvedValueOnce([]);

      await expect(
        service.exportData({
          format: ExportFormat.JSON,
          entityType: 'empty-entity',
        })
      ).rejects.toThrow(BadRequestException);
    });
  });

  describe('createExportStream', () => {
    it('should create a readable stream with exported data', async () => {
      // Mock methods
      jest.spyOn<any, any>(service, 'fetchData').mockResolvedValueOnce(mockProducts);
      jest.spyOn<any, any>(service, 'formatJson').mockReturnValueOnce(Buffer.from(JSON.stringify(mockProducts)));

      const stream = await service.createExportStream({
        format: ExportFormat.JSON,
        entityType: 'products',
      });

      expect(stream).toBeDefined();
      expect(stream.readable).toBe(true);
    });

    it('should throw BadRequestException for empty data', async () => {
      jest.spyOn<any, any>(service, 'fetchData').mockResolvedValueOnce([]);

      await expect(
        service.createExportStream({
          format: ExportFormat.JSON,
          entityType: 'empty-entity',
        })
      ).rejects.toThrow(BadRequestException);
    });
  });

  describe('fetchData', () => {
    it('should fetch data from MongoDB first', async () => {
      const options = {
        format: ExportFormat.JSON,
        entityType: 'products',
        filter: { stock: { $gt: 0 } },
        sortBy: 'name',
        sortDirection: 'asc' as const,
      };

      const result = await service['fetchData'](options);

      expect(result).toEqual(mockProducts);
      expect(mockMongoDbService.find).toHaveBeenCalledWith(
        'products',
        { stock: { $gt: 0 } },
        expect.objectContaining({
          sort: { name: 1 },
        })
      );
    });

    it('should fall back to PostgreSQL if MongoDB returns empty', async () => {
      // Mock MongoDB to return empty
      mockMongoDbService.find.mockResolvedValueOnce([]);

      const options = {
        format: ExportFormat.JSON,
        entityType: 'products',
        sortBy: 'price',
        sortDirection: 'desc' as const,
      };

      await service['fetchData'](options);

      // Should attempt to get data from Prisma
      expect(mockPrismaService.product.findMany).toHaveBeenCalledWith(
        expect.objectContaining({
          orderBy: { price: 'desc' },
        })
      );
    });
  });

  describe('getMimeType', () => {
    it('should return correct MIME type for JSON', () => {
      const mimeType = service['getMimeType'](ExportFormat.JSON);
      expect(mimeType).toBe('application/json');
    });

    it('should return correct MIME type for XML', () => {
      const mimeType = service['getMimeType'](ExportFormat.XML);
      expect(mimeType).toBe('application/xml');
    });

    it('should return correct MIME type for XLSX', () => {
      const mimeType = service['getMimeType'](ExportFormat.XLSX);
      expect(mimeType).toBe('application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
    });

    it('should return correct MIME type for CSV', () => {
      const mimeType = service['getMimeType'](ExportFormat.CSV);
      expect(mimeType).toBe('text/csv');
    });

    it('should return default MIME type for unknown format', () => {
      const mimeType = service['getMimeType']('unknown' as ExportFormat);
      expect(mimeType).toBe('application/octet-stream');
    });
  });
});
