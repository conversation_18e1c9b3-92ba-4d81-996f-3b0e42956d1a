// Mock PrismaService
export const mockPrismaService = {
  user: {
    create: jest.fn().mockResolvedValue({ id: 'user-1', email: '<EMAIL>' }),
    count: jest.fn().mockResolvedValue(10),
    findMany: jest.fn().mockResolvedValue([
      { id: 'user-1', email: '<EMAIL>' }
    ]),
  },
  product: {
    create: jest.fn().mockResolvedValue({ id: 'product-1', name: 'Test Product' }),
    count: jest.fn().mockResolvedValue(20),
    findMany: jest.fn().mockResolvedValue([
      { id: 'product-1', name: 'Product 1', price: 100, stock: 10 },
      { id: 'product-2', name: 'Product 2', price: 200, stock: 20 },
    ]),
  },
  order: {
    create: jest.fn().mockResolvedValue({ id: 'order-1', orderNumber: 'ORD-001' }),
    count: jest.fn().mockResolvedValue(5),
    findMany: jest.fn().mockResolvedValue([
      { id: 'order-1', orderNumber: 'ORD-001', total: 100 }
    ]),
  },
  $queryRaw: jest.fn().mockResolvedValue([1]),
};

// Mock MongoDbService
export const mockMongoDbService = {
  find: jest.fn().mockResolvedValue([
    { id: 'product-1', name: 'Product 1', price: 100, stock: 10 },
    { id: 'product-2', name: 'Product 2', price: 200, stock: 20 },
  ]),
  findOne: jest.fn().mockResolvedValue({ id: 'test-1', name: 'Test' }),
  insertOne: jest.fn().mockResolvedValue({ id: 'test-1' }),
  updateOne: jest.fn().mockResolvedValue(true),
  deleteOne: jest.fn().mockResolvedValue(true),
  syncDocument: jest.fn().mockResolvedValue(true),
  count: jest.fn().mockResolvedValue(10),
  aggregate: jest.fn().mockResolvedValue([
    { _id: '2023-01', count: 5, revenue: 500, avgOrderValue: 100 }
  ]),
};

// Mock RedisService
export const mockRedisService = {
  get: jest.fn().mockResolvedValue(null),
  set: jest.fn().mockResolvedValue(true),
  del: jest.fn().mockResolvedValue(true),
  flushDb: jest.fn().mockResolvedValue(true),
};

// Mock RabbitMQService
export const mockRabbitMQService = {
  publish: jest.fn().mockResolvedValue(true),
  subscribe: jest.fn().mockImplementation((topic, callback) => {
    // Just register the callback
    return Promise.resolve();
  }),
};

// Mock EventEmitter
export const mockEventEmitter = {
  emit: jest.fn(),
};

// Mock data
export const mockProducts = [
  { id: 'product-1', name: 'Product 1', price: 100, stock: 10 },
  { id: 'product-2', name: 'Product 2', price: 200, stock: 20 },
];

export const mockUsers = [
  { id: 'user-1', email: '<EMAIL>', firstName: 'User', lastName: 'One' },
  { id: 'user-2', email: '<EMAIL>', firstName: 'User', lastName: 'Two' },
];

export const mockOrders = [
  { id: 'order-1', orderNumber: 'ORD-001', total: 100, status: 'completed' },
  { id: 'order-2', orderNumber: 'ORD-002', total: 200, status: 'pending' },
];
