import { 
  Controller, 
  Post, 
  Get, 
  Body, 
  Query,
  Param,
  Res,
  UseGuards,
  BadRequestException,
} from '@nestjs/common';
import { 
  ApiTags, 
  ApiOperation, 
  ApiResponse, 
  ApiBody, 
  ApiQuery,
  ApiBearerAuth,
  ApiParam,
} from '@nestjs/swagger';
import { Response } from 'express';
import { JwtAuthGuard } from '@core/auth/guards/jwt-auth.guard';
import { RolesGuard } from '@core/auth/guards/roles.guard';
import { Roles } from '@core/auth/decorators/roles.decorator';
import { DataExportService, ExportFormat, ExportOptions } from './data-export.service';

class ExportRequestDto {
  format: ExportFormat;
  entityType: string;
  filter?: Record<string, any>;
  fields?: string[];
  sortBy?: string;
  sortDirection?: 'asc' | 'desc';
  limit?: number;
  skip?: number;
  filename?: string;
  worksheet?: string;
  includeHeaders?: boolean;
  rootElement?: string;
  dateFormat?: string;
}

@ApiTags('data-export')
@ApiBearerAuth()
@Controller('export')
@UseGuards(JwtAuthGuard)
export class ExportController {
  constructor(private readonly dataExportService: DataExportService) {}

  @Post()
  @ApiOperation({ 
    summary: 'Export data in various formats', 
    description: 'Export data as JSON, XML, XLSX, or CSV with filtering, sorting and field selection',
  })
  @ApiBody({
    type: ExportRequestDto,
    description: 'Export options',
    examples: {
      'Export Products as JSON': {
        value: {
          format: 'json',
          entityType: 'products',
          fields: ['id', 'name', 'price', 'description', 'stockQuantity'],
          sortBy: 'name',
          sortDirection: 'asc',
        },
      },
      'Export Orders as XLSX': {
        value: {
          format: 'xlsx',
          entityType: 'orders',
          fields: ['id', 'orderNumber', 'customerName', 'total', 'createdAt', 'status'],
          sortBy: 'createdAt',
          sortDirection: 'desc',
          includeHeaders: true,
          worksheet: 'Orders',
        },
      },
    },
  })
  @ApiResponse({ 
    status: 200, 
    description: 'Exported data file stream',
  })
  @ApiResponse({ status: 400, description: 'Invalid export request.' })
  @ApiResponse({ status: 401, description: 'Unauthorized.' })
  async exportData(
    @Body() exportRequest: ExportRequestDto,
    @Res() res: Response,
  ) {
    try {
      if (!exportRequest.format || !Object.values(ExportFormat).includes(exportRequest.format)) {
        throw new BadRequestException('Invalid format specified');
      }

      if (!exportRequest.entityType) {
        throw new BadRequestException('Entity type is required');
      }

      const options: ExportOptions = {
        ...exportRequest,
      };

      const result = await this.dataExportService.exportData(options);

      // Set response headers
      res.setHeader('Content-Type', result.mimetype);
      res.setHeader('Content-Disposition', `attachment; filename="${result.filename}"`);
      res.setHeader('Content-Length', result.size);

      // Send file buffer
      return res.send(result.buffer);
    } catch (error) {
      throw new BadRequestException(`Export failed: ${error.message}`);
    }
  }

  @Get('entities')
  @ApiOperation({ 
    summary: 'Get available entities for export',
    description: 'Returns a list of entity types that can be exported',
  })
  @ApiResponse({ 
    status: 200, 
    description: 'List of available entities',
    schema: {
      type: 'object',
      properties: {
        entities: {
          type: 'array',
          items: {
            type: 'object',
            properties: {
              name: { type: 'string' },
              displayName: { type: 'string' },
              description: { type: 'string' },
              fields: { 
                type: 'array',
                items: {
                  type: 'object',
                  properties: {
                    name: { type: 'string' },
                    type: { type: 'string' },
                    description: { type: 'string' },
                  },
                },
              },
            },
          },
        },
      },
    },
  })
  @ApiResponse({ status: 401, description: 'Unauthorized.' })
  async getAvailableEntities() {
    // Return a static list of available entities and their fields
    return {
      entities: [
        {
          name: 'users',
          displayName: 'Users',
          description: 'User accounts',
          fields: [
            { name: 'id', type: 'string', description: 'Unique identifier' },
            { name: 'email', type: 'string', description: 'Email address' },
            { name: 'firstName', type: 'string', description: 'First name' },
            { name: 'lastName', type: 'string', description: 'Last name' },
            { name: 'role', type: 'string', description: 'User role' },
            { name: 'createdAt', type: 'date', description: 'Creation date' },
            { name: 'updatedAt', type: 'date', description: 'Last update date' },
          ],
        },
        {
          name: 'products',
          displayName: 'Products',
          description: 'Product catalog',
          fields: [
            { name: 'id', type: 'string', description: 'Unique identifier' },
            { name: 'name', type: 'string', description: 'Product name' },
            { name: 'sku', type: 'string', description: 'Stock keeping unit' },
            { name: 'description', type: 'string', description: 'Product description' },
            { name: 'price', type: 'decimal', description: 'Product price' },
            { name: 'stockQuantity', type: 'number', description: 'Available stock' },
            { name: 'categoryId', type: 'string', description: 'Category ID' },
            { name: 'createdAt', type: 'date', description: 'Creation date' },
            { name: 'updatedAt', type: 'date', description: 'Last update date' },
          ],
        },
        {
          name: 'orders',
          displayName: 'Orders',
          description: 'Customer orders',
          fields: [
            { name: 'id', type: 'string', description: 'Unique identifier' },
            { name: 'orderNumber', type: 'string', description: 'Order number' },
            { name: 'customerName', type: 'string', description: 'Customer name' },
            { name: 'customerEmail', type: 'string', description: 'Customer email' },
            { name: 'total', type: 'decimal', description: 'Order total' },
            { name: 'status', type: 'string', description: 'Order status' },
            { name: 'paymentStatus', type: 'string', description: 'Payment status' },
            { name: 'createdAt', type: 'date', description: 'Creation date' },
            { name: 'updatedAt', type: 'date', description: 'Last update date' },
          ],
        },
        {
          name: 'transactions',
          displayName: 'Transactions',
          description: 'Financial transactions',
          fields: [
            { name: 'id', type: 'string', description: 'Unique identifier' },
            { name: 'type', type: 'string', description: 'Transaction type' },
            { name: 'amount', type: 'decimal', description: 'Transaction amount' },
            { name: 'status', type: 'string', description: 'Transaction status' },
            { name: 'description', type: 'string', description: 'Transaction description' },
            { name: 'createdAt', type: 'date', description: 'Creation date' },
            { name: 'updatedAt', type: 'date', description: 'Last update date' },
          ],
        },
      ],
    };
  }

  @Get('formats')
  @ApiOperation({ 
    summary: 'Get available export formats',
    description: 'Returns a list of supported export formats',
  })
  @ApiResponse({ 
    status: 200, 
    description: 'List of available formats',
    schema: {
      type: 'object',
      properties: {
        formats: {
          type: 'array',
          items: {
            type: 'object',
            properties: {
              id: { type: 'string' },
              name: { type: 'string' },
              extension: { type: 'string' },
              mimetype: { type: 'string' },
              description: { type: 'string' },
            },
          },
        },
      },
    },
  })
  @ApiResponse({ status: 401, description: 'Unauthorized.' })
  async getAvailableFormats() {
    return {
      formats: [
        {
          id: ExportFormat.JSON,
          name: 'JSON',
          extension: 'json',
          mimetype: 'application/json',
          description: 'JavaScript Object Notation',
        },
        {
          id: ExportFormat.XML,
          name: 'XML',
          extension: 'xml',
          mimetype: 'application/xml',
          description: 'Extensible Markup Language',
        },
        {
          id: ExportFormat.XLSX,
          name: 'Excel',
          extension: 'xlsx',
          mimetype: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
          description: 'Microsoft Excel Spreadsheet',
        },
        {
          id: ExportFormat.CSV,
          name: 'CSV',
          extension: 'csv',
          mimetype: 'text/csv',
          description: 'Comma-Separated Values',
        },
      ],
    };
  }

  @Get('download/:entityType')
  @ApiOperation({
    summary: 'Quick download entity data',
    description: 'Simplified endpoint for downloading data in specified format',
  })
  @ApiParam({
    name: 'entityType',
    description: 'Entity type to export (e.g., users, products, orders)',
    required: true,
  })
  @ApiQuery({
    name: 'format',
    description: 'Export format',
    enum: Object.values(ExportFormat),
    required: true,
  })
  @ApiQuery({
    name: 'fields',
    description: 'Comma-separated list of fields to include',
    required: false,
  })
  @ApiQuery({
    name: 'sortBy',
    description: 'Field to sort by',
    required: false,
  })
  @ApiQuery({
    name: 'sortDirection',
    description: 'Sort direction (asc or desc)',
    enum: ['asc', 'desc'],
    required: false,
  })
  @ApiQuery({
    name: 'limit',
    description: 'Maximum number of records to export',
    required: false,
  })
  @ApiResponse({ status: 200, description: 'File download' })
  @ApiResponse({ status: 400, description: 'Invalid parameters' })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  async downloadEntity(
    @Param('entityType') entityType: string,
    @Query('format') format: ExportFormat,
    @Res() res: Response,
    @Query('fields') fields?: string,
    @Query('sortBy') sortBy?: string,
    @Query('sortDirection') sortDirection?: 'asc' | 'desc',
    @Query('limit') limit?: number,
  ) {
    try {
      if (!format || !Object.values(ExportFormat).includes(format)) {
        throw new BadRequestException('Invalid format specified');
      }

      const options: ExportOptions = {
        format,
        entityType,
        fields: fields ? fields.split(',') : undefined,
        sortBy,
        sortDirection: sortDirection || 'asc',
        limit: limit ? parseInt(limit.toString(), 10) : undefined,
        includeHeaders: true,
      };

      const result = await this.dataExportService.exportData(options);

      // Set response headers
      res.setHeader('Content-Type', result.mimetype);
      res.setHeader('Content-Disposition', `attachment; filename="${result.filename}"`);
      res.setHeader('Content-Length', result.size);

      // Send file buffer
      return res.send(result.buffer);
    } catch (error) {
      throw new BadRequestException(`Export failed: ${error.message}`);
    }
  }
}
