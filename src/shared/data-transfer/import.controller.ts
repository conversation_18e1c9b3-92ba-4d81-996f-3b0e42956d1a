import { 
  Controller, 
  Post, 
  Get, 
  Param, 
  Body, 
  UploadedFile,
  UseInterceptors,
  BadRequestException,
  Query,
  UseGuards,
} from '@nestjs/common';
import { FileInterceptor } from '@nestjs/platform-express';
import { 
  ApiTags, 
  ApiConsumes, 
  ApiBody, 
  ApiOperation, 
  ApiResponse, 
  ApiParam,
  ApiBearerAuth,
  ApiQuery,
} from '@nestjs/swagger';
import { JwtAuthGuard } from '@core/auth/guards/jwt-auth.guard';
import { RolesGuard } from '@core/auth/guards/roles.guard';
import { Roles } from '@core/auth/decorators/roles.decorator';
import { DataImportService, ImportFormat, ImportOptions } from './data-import.service';
import { Readable } from 'stream';
import { MulterFile } from '@infra/storage/multer-file.interface';

class ImportOptionsDto {
  format: ImportFormat;
  entityType: string;
  validateData?: boolean;
  batchSize?: number;
  skipRows?: number;
  worksheet?: string;
  dateFields?: string[];
  numberFields?: string[];
  booleanFields?: string[];
}

@ApiTags('data-import')
@ApiBearerAuth()
@Controller('import')
@UseGuards(JwtAuthGuard)
export class ImportController {
  constructor(private readonly dataImportService: DataImportService) {}

  @Post('upload')
  @UseInterceptors(FileInterceptor('file'))
  @ApiConsumes('multipart/form-data')
  @ApiOperation({ 
    summary: 'Import data from file',
    description: 'Upload a file (JSON, XML, XLSX) to import data into the system',
  })
  @ApiBody({
    schema: {
      type: 'object',
      required: ['file', 'format', 'entityType'],
      properties: {
        file: {
          type: 'string',
          format: 'binary',
          description: 'File to import (JSON, XML, or XLSX)',
        },
        format: {
          type: 'string',
          enum: Object.values(ImportFormat),
          description: 'File format',
        },
        entityType: {
          type: 'string',
          description: 'Entity type to import (e.g., users, products, orders)',
        },
        validateData: {
          type: 'boolean',
          description: 'Whether to validate data before importing',
        },
        batchSize: {
          type: 'number',
          description: 'Number of records to process in each batch',
        },
        skipRows: {
          type: 'number',
          description: 'Number of rows to skip (for XLSX)',
        },
        worksheet: {
          type: 'string',
          description: 'Worksheet name (for XLSX)',
        },
        dateFields: {
          type: 'array',
          items: { type: 'string' },
          description: 'Fields to convert to dates',
        },
        numberFields: {
          type: 'array',
          items: { type: 'string' },
          description: 'Fields to convert to numbers',
        },
        booleanFields: {
          type: 'array',
          items: { type: 'string' },
          description: 'Fields to convert to booleans',
        },
      },
    },
  })
  @ApiResponse({ 
    status: 201, 
    description: 'Import job started successfully.',
    schema: {
      type: 'object',
      properties: {
        importId: {
          type: 'string',
          description: 'Import job ID for tracking progress',
        },
        message: {
          type: 'string',
          description: 'Success message',
        },
      },
    },
  })
  @ApiResponse({ status: 400, description: 'Invalid input or file format.' })
  @ApiResponse({ status: 401, description: 'Unauthorized.' })
  @ApiResponse({ status: 403, description: 'Forbidden - requires admin role.' })
  @Roles('admin')
  @UseGuards(RolesGuard)
  async importFile(
    @UploadedFile() file: MulterFile,
    @Body() options: ImportOptionsDto,
  ) {
    if (!file) {
      throw new BadRequestException('No file uploaded');
    }

    if (!options.format || !Object.values(ImportFormat).includes(options.format)) {
      throw new BadRequestException('Invalid format specified');
    }

    if (!options.entityType) {
      throw new BadRequestException('Entity type is required');
    }

    try {
      const importStream = Readable.from(file.buffer);
      
      const importOptions: ImportOptions = {
        format: options.format,
        entityType: options.entityType,
        validateData: options.validateData,
        batchSize: options.batchSize,
        skipRows: options.skipRows,
        worksheet: options.worksheet,
        dateFields: options.dateFields,
        numberFields: options.numberFields,
        booleanFields: options.booleanFields,
      };
      
      const importId = await this.dataImportService.importFromStream(importStream, importOptions);

      return {
        importId,
        message: `Import job started successfully. Use the import ID to check progress.`,
      };
    } catch (error) {
      throw new BadRequestException(`Import failed: ${error.message}`);
    }
  }

  @Get('progress/:importId')
  @ApiOperation({ 
    summary: 'Get import job progress',
    description: 'Check the progress of an import job by ID',
  })
  @ApiParam({ 
    name: 'importId', 
    description: 'Import job ID',
    required: true,
  })
  @ApiResponse({ 
    status: 200, 
    description: 'Import job progress.',
    schema: {
      type: 'object',
      properties: {
        id: { type: 'string' },
        total: { type: 'number' },
        processed: { type: 'number' },
        succeeded: { type: 'number' },
        failed: { type: 'number' },
        status: { 
          type: 'string',
          enum: ['pending', 'processing', 'completed', 'failed'],
        },
        progress: { type: 'number' },
        errors: { 
          type: 'array',
          items: {
            type: 'object',
            properties: {
              index: { type: 'number' },
              message: { type: 'string' },
            },
          },
        },
        startTime: { type: 'string', format: 'date-time' },
        endTime: { type: 'string', format: 'date-time' },
      },
    },
  })
  @ApiResponse({ status: 400, description: 'Invalid import ID.' })
  @ApiResponse({ status: 401, description: 'Unauthorized.' })
  async getImportProgress(@Param('importId') importId: string) {
    try {
      const progress = this.dataImportService.getImportProgress(importId);
      
      // Calculate percentage
      const progressPercent = progress.total > 0 
        ? Math.round((progress.processed / progress.total) * 100) 
        : 0;
      
      return {
        ...progress,
        progress: progressPercent,
      };
    } catch (error) {
      throw new BadRequestException(error.message);
    }
  }
}
