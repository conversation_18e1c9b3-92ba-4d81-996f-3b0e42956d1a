import { Injectable, Logger, BadRequestException } from '@nestjs/common';
import { PrismaService } from '@core/prisma/prisma.service';
import { MongoDbService } from '@infra/mongodb/mongodb.service';
import { EventEmitter2 } from '@nestjs/event-emitter';
import * as XLSX from 'xlsx';
import * as xml2js from 'xml2js';
import { Parser } from 'json2csv';
import { Readable } from 'stream';

export enum ExportFormat {
  JSON = 'json',
  XML = 'xml',
  XLSX = 'xlsx',
  CSV = 'csv',
}

export interface ExportOptions {
  format: ExportFormat;
  entityType: string;
  filter?: Record<string, any>;
  fields?: string[];
  sortBy?: string;
  sortDirection?: 'asc' | 'desc';
  limit?: number;
  skip?: number;
  filename?: string;
  worksheet?: string;
  includeHeaders?: boolean;
  rootElement?: string; // For XML
  dateFormat?: string;
}

export interface ExportResult {
  filename: string;
  mimetype: string;
  buffer: Buffer;
  size: number;
}

@Injectable()
export class DataExportService {
  private readonly logger = new Logger(DataExportService.name);

  constructor(
    private readonly prismaService: PrismaService,
    private readonly mongoDbService: MongoDbService,
    private readonly eventEmitter: EventEmitter2,
  ) {}

  /**
   * Export data based on provided options
   * 
   * @param options Export options
   * @returns Export result with buffer and metadata
   */
  async exportData(options: ExportOptions): Promise<ExportResult> {
    try {
      // Get data from database (prefer MongoDB for fast reads)
      const data = await this.fetchData(options);
      
      if (!data || data.length === 0) {
        throw new BadRequestException('No data found matching export criteria');
      }
      
      // Format data based on requested format
      const buffer = await this.formatData(data, options);
      
      // Generate filename if not provided
      const filename = options.filename || 
        `${options.entityType}_export_${new Date().toISOString().replace(/[:.]/g, '-')}.${options.format}`;
      
      // Determine MIME type
      const mimetype = this.getMimeType(options.format);
      
      // Emit export event
      this.eventEmitter.emit('export.completed', {
        entityType: options.entityType,
        format: options.format,
        recordCount: data.length,
        size: buffer.length,
        timestamp: new Date(),
      });
      
      return {
        filename,
        mimetype,
        buffer,
        size: buffer.length,
      };
    } catch (error) {
      this.logger.error(`Export failed: ${error.message}`, error.stack);
      throw new BadRequestException(`Failed to export data: ${error.message}`);
    }
  }

  /**
   * Create a readable stream for exporting large datasets
   * 
   * @param options Export options
   * @returns Readable stream
   */
  async createExportStream(options: ExportOptions): Promise<Readable> {
    try {
      // Get data from database (prefer MongoDB for fast reads)
      const data = await this.fetchData(options);
      
      if (!data || data.length === 0) {
        throw new BadRequestException('No data found matching export criteria');
      }
      
      // Format data based on requested format
      const buffer = await this.formatData(data, options);
      
      // Create a readable stream from the buffer
      const stream = new Readable();
      stream.push(buffer);
      stream.push(null); // End of stream
      
      return stream;
    } catch (error) {
      this.logger.error(`Export stream creation failed: ${error.message}`, error.stack);
      throw new BadRequestException(`Failed to create export stream: ${error.message}`);
    }
  }

  /**
   * Fetch data from database (prefer MongoDB for reads)
   * 
   * @param options Export options
   * @returns Array of data objects
   */
  private async fetchData(options: ExportOptions): Promise<any[]> {
    const { entityType, filter = {}, fields, sortBy, sortDirection = 'asc', limit, skip } = options;
    
    try {
      // Try MongoDB first for better read performance
      const data = await this.mongoDbService.find(
        entityType,
        filter,
        {
          projection: fields ? fields.reduce((acc, field) => ({ ...acc, [field]: 1 }), {}) : undefined,
          sort: sortBy ? { [sortBy]: sortDirection === 'asc' ? 1 : -1 } : undefined,
          limit: limit || 0,
          skip: skip || 0,
        }
      );
      
      if (data && data.length > 0) {
        return data;
      }
      
      // Fall back to PostgreSQL if no data in MongoDB
      this.logger.log(`No data found in MongoDB for ${entityType}, falling back to PostgreSQL`);
      
      // Map entity type to Prisma model
      const prismaTable = this.getPrismaTableForEntity(entityType);
      
      // Handle field selection
      const select = fields?.reduce((acc, field) => ({ ...acc, [field]: true }), {});
      
      // Handle sorting
      const orderBy = sortBy ? { [sortBy]: sortDirection } : undefined;
      
      // Fetch from PostgreSQL
      const result = await this.prismaService[prismaTable].findMany({
        where: filter,
        select,
        orderBy,
        take: limit,
        skip,
      });
      
      return result;
    } catch (error) {
      this.logger.error(`Error fetching data for export: ${error.message}`, error.stack);
      throw new BadRequestException(`Failed to fetch data: ${error.message}`);
    }
  }

  /**
   * Format data based on requested export format
   * 
   * @param data Data to format
   * @param options Export options
   * @returns Buffer with formatted data
   */
  private async formatData(data: any[], options: ExportOptions): Promise<Buffer> {
    switch (options.format) {
      case ExportFormat.JSON:
        return this.formatJson(data);
      case ExportFormat.XML:
        return this.formatXml(data, options);
      case ExportFormat.XLSX:
        return this.formatXlsx(data, options);
      case ExportFormat.CSV:
        return this.formatCsv(data, options);
      default:
        throw new BadRequestException(`Unsupported export format: ${options.format}`);
    }
  }

  /**
   * Format data as JSON
   * 
   * @param data Data to format
   * @returns Buffer with JSON data
   */
  private formatJson(data: any[]): Buffer {
    try {
      const jsonString = JSON.stringify(data, null, 2);
      return Buffer.from(jsonString, 'utf-8');
    } catch (error) {
      this.logger.error(`Failed to format JSON: ${error.message}`, error.stack);
      throw new BadRequestException(`Failed to format data as JSON: ${error.message}`);
    }
  }

  /**
   * Format data as XML
   * 
   * @param data Data to format
   * @param options Export options
   * @returns Buffer with XML data
   */
  private async formatXml(data: any[], options: ExportOptions): Promise<Buffer> {
    try {
      const rootElement = options.rootElement || options.entityType || 'data';
      const builder = new xml2js.Builder({
        rootName: rootElement,
        headless: false,
        renderOpts: { pretty: true, indent: '  ' },
      });
      
      // Prepare data structure for XML
      const xmlObj = {
        [options.entityType || 'item']: data,
      };
      
      const xmlString = builder.buildObject(xmlObj);
      return Buffer.from(xmlString, 'utf-8');
    } catch (error) {
      this.logger.error(`Failed to format XML: ${error.message}`, error.stack);
      throw new BadRequestException(`Failed to format data as XML: ${error.message}`);
    }
  }

  /**
   * Format data as XLSX
   * 
   * @param data Data to format
   * @param options Export options
   * @returns Buffer with XLSX data
   */
  private formatXlsx(data: any[], options: ExportOptions): Buffer {
    try {
      // Create a new workbook
      const workbook = XLSX.utils.book_new();
      
      // Convert data to worksheet
      const worksheet = XLSX.utils.json_to_sheet(data, {
        header: options.fields,
        skipHeader: !options.includeHeaders,
      });
      
      // Add worksheet to workbook
      XLSX.utils.book_append_sheet(
        workbook,
        worksheet,
        options.worksheet || options.entityType || 'Sheet1'
      );
      
      // Write to buffer
      const xlsxBuffer = XLSX.write(workbook, { type: 'buffer', bookType: 'xlsx' });
      return xlsxBuffer;
    } catch (error) {
      this.logger.error(`Failed to format XLSX: ${error.message}`, error.stack);
      throw new BadRequestException(`Failed to format data as XLSX: ${error.message}`);
    }
  }

  /**
   * Format data as CSV
   * 
   * @param data Data to format
   * @param options Export options
   * @returns Buffer with CSV data
   */
  private formatCsv(data: any[], options: ExportOptions): Buffer {
    try {
      const fields = options.fields || Object.keys(data[0] || {});
      
      const parser = new Parser({
        fields,
        header: options.includeHeaders !== false,
      });
      
      const csv = parser.parse(data);
      return Buffer.from(csv, 'utf-8');
    } catch (error) {
      this.logger.error(`Failed to format CSV: ${error.message}`, error.stack);
      throw new BadRequestException(`Failed to format data as CSV: ${error.message}`);
    }
  }

  /**
   * Get MIME type for export format
   * 
   * @param format Export format
   * @returns MIME type string
   */
  private getMimeType(format: ExportFormat): string {
    switch (format) {
      case ExportFormat.JSON:
        return 'application/json';
      case ExportFormat.XML:
        return 'application/xml';
      case ExportFormat.XLSX:
        return 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet';
      case ExportFormat.CSV:
        return 'text/csv';
      default:
        return 'application/octet-stream';
    }
  }

  /**
   * Get Prisma table name for entity type
   * 
   * @param entityType Entity type
   * @returns Prisma table name
   */
  private getPrismaTableForEntity(entityType: string): string {
    // Map entity types to Prisma table names
    const entityMap: Record<string, string> = {
      users: 'user',
      products: 'product',
      categories: 'productCategory',
      orders: 'order',
      orderItems: 'orderItem',
      transactions: 'transaction',
      invoices: 'invoice',
    };
    
    const table = entityMap[entityType] || entityType;
    
    if (!this.prismaService[table]) {
      throw new BadRequestException(`Unsupported entity type: ${entityType}`);
    }
    
    return table;
  }
}
