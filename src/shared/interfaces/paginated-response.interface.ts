import { ObjectType, Field, Int } from '@nestjs/graphql';
import { Type } from '@nestjs/common';

export interface PaginationMeta {
  page: number;
  limit: number;
  totalPages: number;
  totalCount: number;
  hasNextPage: boolean;
  hasPreviousPage: boolean;
}

@ObjectType()
export class PaginationMetaDto {
  @Field(() => Int)
  page: number;

  @Field(() => Int)
  limit: number;

  @Field(() => Int)
  totalPages: number;

  @Field(() => Int)
  totalCount: number;

  @Field(() => Boolean)
  hasNextPage: boolean;

  @Field(() => Boolean)
  hasPreviousPage: boolean;
}

export interface PaginatedResponseInterface<T> {
  data: T[];
  pagination: PaginationMeta;
}

export function PaginatedResponse<T>(classRef: Type<T>): any {
  // Get the name of the class to create a unique name for the paginated response
  const className = classRef.name;

  @ObjectType({ isAbstract: true })
  abstract class PaginatedResponseClass {
    @Field(() => [classRef])
    data: T[];

    @Field(() => PaginationMetaDto)
    pagination: PaginationMetaDto;
  }

  // Use a dynamic name based on the entity name
  Object.defineProperty(PaginatedResponseClass, 'name', { value: `Paginated${className}Response` });
  
  return PaginatedResponseClass;
}
