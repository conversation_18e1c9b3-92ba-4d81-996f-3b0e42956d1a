import { ApiQuery, ApiResponse } from '@nestjs/swagger';
import { ParseIntPipe, DefaultValuePipe } from '@nestjs/common';
import { ErrorResponseDto, UnauthorizedResponseDto, ForbiddenResponseDto, NotFoundResponseDto, ConflictResponseDto } from '@shared/dto/paginated-response.dto';

/**
 * Base controller class with common patterns and decorators
 */
export abstract class BaseController {
  /**
   * Standard pagination parameters decorator
   */
  static PaginationParams() {
    return function (target: any, propertyName: string, descriptor: PropertyDescriptor) {
      ApiQuery({
        name: 'page',
        required: false,
        type: Number,
        description: 'Page number (default: 1)',
        example: 1,
      })(target, propertyName, descriptor);
      
      ApiQuery({
        name: 'limit',
        required: false,
        type: Number,
        description: 'Items per page (default: 20, max: 100)',
        example: 20,
      })(target, propertyName, descriptor);
    };
  }

  /**
   * Standard error responses decorator
   */
  static StandardErrorResponses() {
    return function (target: any, propertyName: string, descriptor: PropertyDescriptor) {
      ApiResponse({
        status: 400,
        description: 'Bad Request - Invalid input data',
        type: ErrorResponseDto,
      })(target, propertyName, descriptor);
      
      ApiResponse({
        status: 401,
        description: 'Unauthorized - Invalid or missing authentication token',
        type: UnauthorizedResponseDto,
      })(target, propertyName, descriptor);
      
      ApiResponse({
        status: 403,
        description: 'Forbidden - Insufficient permissions',
        type: ForbiddenResponseDto,
      })(target, propertyName, descriptor);
    };
  }

  /**
   * CRUD operation error responses
   */
  static CrudErrorResponses() {
    return function (target: any, propertyName: string, descriptor: PropertyDescriptor) {
      BaseController.StandardErrorResponses()(target, propertyName, descriptor);
      
      ApiResponse({
        status: 404,
        description: 'Resource not found',
        type: NotFoundResponseDto,
      })(target, propertyName, descriptor);
      
      ApiResponse({
        status: 409,
        description: 'Conflict - Resource already exists',
        type: ConflictResponseDto,
      })(target, propertyName, descriptor);
    };
  }

  /**
   * Standard success response for list operations
   */
  static ListResponse<T>(type: new () => T) {
    return function (target: any, propertyName: string, descriptor: PropertyDescriptor) {
      ApiResponse({
        status: 200,
        description: 'Successfully retrieved list',
        schema: {
          type: 'object',
          properties: {
            data: {
              type: 'array',
              items: { $ref: `#/components/schemas/${type.name}` },
            },
            meta: {
              type: 'object',
              properties: {
                total: { type: 'number', description: 'Total number of items' },
                page: { type: 'number', description: 'Current page number' },
                limit: { type: 'number', description: 'Items per page' },
                totalPages: { type: 'number', description: 'Total number of pages' },
              },
            },
          },
        },
      })(target, propertyName, descriptor);
    };
  }

  /**
   * Standard success response for single item operations
   */
  static ItemResponse<T>(type: new () => T, status: number = 200, description: string = 'Successfully retrieved item') {
    return function (target: any, propertyName: string, descriptor: PropertyDescriptor) {
      ApiResponse({
        status,
        description,
        type,
      })(target, propertyName, descriptor);
    };
  }

  /**
   * Standard filter parameters for status-based resources
   */
  static StatusFilter(enumType: any, description: string = 'Filter by status') {
    return function (target: any, propertyName: string, descriptor: PropertyDescriptor) {
      ApiQuery({
        name: 'status',
        required: false,
        enum: enumType,
        description,
      })(target, propertyName, descriptor);
    };
  }

  /**
   * Standard search parameter
   */
  static SearchParam(description: string = 'Search term for filtering results') {
    return function (target: any, propertyName: string, descriptor: PropertyDescriptor) {
      ApiQuery({
        name: 'search',
        required: false,
        type: String,
        description,
        example: 'search term',
      })(target, propertyName, descriptor);
    };
  }

  /**
   * Date range parameters
   */
  static DateRangeParams() {
    return function (target: any, propertyName: string, descriptor: PropertyDescriptor) {
      ApiQuery({
        name: 'startDate',
        required: false,
        type: String,
        description: 'Start date (ISO format)',
        example: '2023-01-01T00:00:00Z',
      })(target, propertyName, descriptor);
      
      ApiQuery({
        name: 'endDate',
        required: false,
        type: String,
        description: 'End date (ISO format)',
        example: '2023-12-31T23:59:59Z',
      })(target, propertyName, descriptor);
    };
  }

  /**
   * Helper method to create standardized pagination parameters
   */
  protected createPaginationParams(page?: number, limit?: number) {
    const validatedPage = Math.max(1, page || 1);
    const validatedLimit = Math.min(100, Math.max(1, limit || 20));
    
    return {
      page: validatedPage,
      limit: validatedLimit,
      skip: (validatedPage - 1) * validatedLimit,
      take: validatedLimit,
    };
  }

  /**
   * Helper method to create standardized response format
   */
  protected createListResponse<T>(data: T[], total: number, page: number, limit: number) {
    return {
      data,
      meta: {
        total,
        page,
        limit,
        totalPages: Math.ceil(total / limit),
      },
    };
  }

  /**
   * Helper method to parse date parameters
   */
  protected parseDateRange(startDate?: string, endDate?: string) {
    return {
      start: startDate ? new Date(startDate) : undefined,
      end: endDate ? new Date(endDate) : undefined,
    };
  }

  /**
   * Helper method to validate UUID parameters
   */
  protected validateUuid(id: string, fieldName: string = 'id'): void {
    const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
    if (!uuidRegex.test(id)) {
      throw new Error(`Invalid ${fieldName} format`);
    }
  }
}

/**
 * Common role combinations for supply chain operations
 */
export const SupplyChainRoles = {
  ADMIN_ONLY: ['admin'],
  SUPPLY_CHAIN_MANAGERS: ['admin', 'supply_chain_manager'],
  INVENTORY_MANAGERS: ['admin', 'inventory_manager', 'supply_chain_manager'],
  PURCHASE_MANAGERS: ['admin', 'purchase_manager', 'supply_chain_manager'],
  FINANCE_MANAGERS: ['admin', 'finance_manager'],
  WAREHOUSE_STAFF: ['admin', 'warehouse_manager', 'inventory_manager'],
  READ_ONLY_USERS: ['admin', 'supply_chain_manager', 'inventory_manager', 'purchase_manager', 'tailor'],
} as const;

/**
 * Common API tag patterns
 */
export const ApiTags = {
  SUPPLIERS: 'suppliers',
  INVENTORY: 'inventory',
  PURCHASE_ORDERS: 'purchase-orders',
  SUPPLY_CHAIN_ANALYTICS: 'supply-chain-analytics',
  WALLETS: 'wallets',
  CREDIT: 'credit',
  INSTALLMENTS: 'installments',
} as const;
