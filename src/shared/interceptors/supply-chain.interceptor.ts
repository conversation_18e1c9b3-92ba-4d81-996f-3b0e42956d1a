import {
  Injectable,
  NestInterceptor,
  Execution<PERSON><PERSON>xt,
  <PERSON><PERSON><PERSON><PERSON>,
  Lo<PERSON>,
} from '@nestjs/common';
import { Observable } from 'rxjs';
import { tap, catchError } from 'rxjs/operators';
import { Request } from 'express';

/**
 * Supply Chain specific interceptor for logging and monitoring
 */
@Injectable()
export class SupplyChainInterceptor implements NestInterceptor {
  private readonly logger = new Logger(SupplyChainInterceptor.name);

  intercept(context: ExecutionContext, next: CallHandler): Observable<any> {
    const request = context.switchToHttp().getRequest<Request>();
    const { method, url, body, query, params } = request;
    const userAgent = request.get('User-Agent') || '';
    const ip = request.ip;
    const userId = (request as any).user?.id;

    const startTime = Date.now();

    this.logger.log(
      `Supply Chain API Request: ${method} ${url} - User: ${userId} - IP: ${ip}`,
    );

    return next.handle().pipe(
      tap((data) => {
        const endTime = Date.now();
        const duration = endTime - startTime;
        
        this.logger.log(
          `Supply Chain API Response: ${method} ${url} - ${duration}ms - User: ${userId}`,
        );

        // Log specific supply chain operations
        if (method === 'POST' && url.includes('/suppliers')) {
          this.logger.log(`New supplier created by user ${userId}`);
        } else if (method === 'POST' && url.includes('/purchase-orders')) {
          this.logger.log(`New purchase order created by user ${userId}`);
        } else if (method === 'POST' && url.includes('/inventory/transactions')) {
          this.logger.log(`Inventory transaction recorded by user ${userId}`);
        }
      }),
      catchError((error) => {
        const endTime = Date.now();
        const duration = endTime - startTime;
        
        this.logger.error(
          `Supply Chain API Error: ${method} ${url} - ${duration}ms - User: ${userId} - Error: ${error.message}`,
          error.stack,
        );

        throw error;
      }),
    );
  }
}

/**
 * Performance monitoring interceptor for analytics endpoints
 */
@Injectable()
export class AnalyticsPerformanceInterceptor implements NestInterceptor {
  private readonly logger = new Logger(AnalyticsPerformanceInterceptor.name);

  intercept(context: ExecutionContext, next: CallHandler): Observable<any> {
    const request = context.switchToHttp().getRequest<Request>();
    const { method, url } = request;
    const startTime = Date.now();

    return next.handle().pipe(
      tap((data) => {
        const endTime = Date.now();
        const duration = endTime - startTime;
        
        // Log slow analytics queries
        if (duration > 5000) { // 5 seconds
          this.logger.warn(
            `Slow analytics query detected: ${method} ${url} - ${duration}ms`,
          );
        }

        // Log data size for large responses
        if (data && typeof data === 'object') {
          const dataSize = JSON.stringify(data).length;
          if (dataSize > 1000000) { // 1MB
            this.logger.warn(
              `Large analytics response: ${method} ${url} - ${dataSize} bytes`,
            );
          }
        }
      }),
    );
  }
}

/**
 * Audit trail interceptor for critical operations
 */
@Injectable()
export class AuditTrailInterceptor implements NestInterceptor {
  private readonly logger = new Logger(AuditTrailInterceptor.name);

  intercept(context: ExecutionContext, next: CallHandler): Observable<any> {
    const request = context.switchToHttp().getRequest<Request>();
    const { method, url, body } = request;
    const userId = (request as any).user?.id;
    const userEmail = (request as any).user?.email;

    // Define critical operations that need audit logging
    const criticalOperations = [
      'POST /supply-chain/suppliers/verify',
      'DELETE /supply-chain/suppliers',
      'POST /supply-chain/purchase-orders/approve',
      'PUT /supply-chain/purchase-orders',
      'POST /supply-chain/inventory/adjust',
    ];

    const operation = `${method} ${url}`;
    const isCritical = criticalOperations.some(op => 
      operation.includes(op.split(' ')[1])
    );

    if (isCritical) {
      this.logger.log(
        `AUDIT: Critical operation initiated - ${operation} by ${userEmail} (${userId})`,
      );
    }

    return next.handle().pipe(
      tap((data) => {
        if (isCritical) {
          this.logger.log(
            `AUDIT: Critical operation completed - ${operation} by ${userEmail} (${userId})`,
          );
        }
      }),
      catchError((error) => {
        if (isCritical) {
          this.logger.error(
            `AUDIT: Critical operation failed - ${operation} by ${userEmail} (${userId}) - Error: ${error.message}`,
          );
        }
        throw error;
      }),
    );
  }
}
