import { Injectable, NestInterceptor, ExecutionContext, CallHandler } from '@nestjs/common';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';
import { GqlExecutionContext } from '@nestjs/graphql';

export interface Response<T> {
  data: T;
  meta?: Record<string, any>;
}

@Injectable()
export class TransformInterceptor<T> implements NestInterceptor<T, Response<T>> {
  intercept(context: ExecutionContext, next: CallHandler): Observable<Response<T>> {
    return next.handle().pipe(
      map(data => {
        // Skip transformation for GraphQL responses
        try {
          // Try to create a GraphQL execution context
          const gqlContext = GqlExecutionContext.create(context);
          const info = gqlContext.getInfo();
          
          // If we can get GraphQL info, this is a GraphQL request
          if (info && info.fieldName) {
            return data;
          }
        } catch (error) {
          // Not a GraphQL context, continue with transformation
        }
        
        // For REST responses, wrap in standard format
        return {
          statusCode: context.switchToHttp().getResponse().statusCode,
          data,
          timestamp: new Date().toISOString(),
        };
      }),
    );
  }
}
