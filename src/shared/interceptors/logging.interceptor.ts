import { Injectable, NestInterceptor, ExecutionContext, CallHandler } from '@nestjs/common';
import { Observable } from 'rxjs';
import { tap } from 'rxjs/operators';
import { LoggerService } from '@core/logger/logger.service';
import { GqlExecutionContext } from '@nestjs/graphql';

@Injectable()
export class LoggingInterceptor implements NestInterceptor {
  constructor(private readonly loggerService: LoggerService) {}

  intercept(context: ExecutionContext, next: CallHandler): Observable<any> {
    const now = Date.now();
    const contextType = context.getType();
    let request;
    let isGraphQLContext = false;
    
    if (contextType === 'http') {
      request = context.switchToHttp().getRequest();
      const method = request.method;
      const url = request.url;
      const user = request.user ? request.user.email : 'anonymous';
      
      this.loggerService.setContext('HTTP').log(
        `${method} ${url} - User: ${user}`,
      );
    } else {
      // Try to get GraphQL context
      try {
        const gqlContext = GqlExecutionContext.create(context);
        const gqlInfo = gqlContext.getInfo();
        request = gqlContext.getContext().req;
        const user = request.user ? request.user.email : 'anonymous';
        
        if (gqlInfo && gqlInfo.fieldName) {
          isGraphQLContext = true;
          this.loggerService.setContext('GraphQL').log(
            `${gqlInfo.fieldName} - User: ${user}`,
          );
        }
      } catch (error) {
        // Not a GraphQL context, skip
      }
    }

    return next.handle().pipe(
      tap({
        next: () => {
          const delay = Date.now() - now;
          if (contextType === 'http') {
            const method = request.method;
            const url = request.url;
            this.loggerService.setContext('HTTP').log(
              `${method} ${url} - ${delay}ms`,
            );
          } else if (isGraphQLContext) {
            try {
              const gqlContext = GqlExecutionContext.create(context);
              const gqlInfo = gqlContext.getInfo();
              
              if (gqlInfo && gqlInfo.fieldName) {
                this.loggerService.setContext('GraphQL').log(
                  `${gqlInfo.fieldName} - ${delay}ms`,
                );
              }
            } catch (error) {
              // Not a GraphQL context or error occurred
            }
          }
        },
        error: (err) => {
          const delay = Date.now() - now;
          if (contextType === 'http') {
            const method = request.method;
            const url = request.url;
            this.loggerService.setContext('HTTP').error(
              `${method} ${url} - ${delay}ms - Error: ${err.message}`,
            );
          } else if (isGraphQLContext) {
            try {
              const gqlContext = GqlExecutionContext.create(context);
              const gqlInfo = gqlContext.getInfo();
              
              if (gqlInfo && gqlInfo.fieldName) {
                this.loggerService.setContext('GraphQL').error(
                  `${gqlInfo.fieldName} - ${delay}ms - Error: ${err.message}`,
                );
              }
            } catch (error) {
              // Not a GraphQL context or error occurred
            }
          }
        },
      }),
    );
  }
}
