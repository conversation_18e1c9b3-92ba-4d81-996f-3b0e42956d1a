import { ApiProperty, ApiPropertyOptions } from '@nestjs/swagger';
import { applyDecorators } from '@nestjs/common';
import { IsString, Matches, IsOptional } from 'class-validator';
import { Transform } from 'class-transformer';
import Decimal from 'decimal.js';

/**
 * Decorator for decimal currency fields that ensures proper validation and documentation
 */
export function ApiDecimalProperty(options: {
  description: string;
  example: string;
  required?: boolean;
  minimum?: string;
  maximum?: string;
  currency?: string;
} & Partial<ApiPropertyOptions>) {
  const { description, example, required = true, minimum, maximum, currency = 'USD', ...apiOptions } = options;

  const decorators = [
    ApiProperty({
      description: `${description} (stored as string for decimal precision)`,
      example,
      type: 'string',
      pattern: '^\\d+(\\.\\d{1,4})?$',
      format: 'decimal',
      ...apiOptions,
      ...(minimum && { minimum: parseFloat(minimum) }),
      ...(maximum && { maximum: parseFloat(maximum) }),
    }),
    IsString({ message: 'Amount must be a string for decimal precision' }),
    Matches(/^\d+(\.\d{1,4})?$/, {
      message: 'Amount must be a valid decimal number with up to 4 decimal places',
    }),
    Transform(({ value }) => {
      if (typeof value === 'string') {
        // Validate that it's a valid decimal
        try {
          const decimal = new Decimal(value);
          return decimal.toString();
        } catch {
          return value; // Let validation handle the error
        }
      }
      return value;
    }),
  ];

  if (!required) {
    decorators.push(IsOptional());
  }

  return applyDecorators(...decorators);
}

/**
 * Decorator for optional decimal currency fields
 */
export function ApiOptionalDecimalProperty(options: {
  description: string;
  example: string;
  minimum?: string;
  maximum?: string;
  currency?: string;
} & Partial<ApiPropertyOptions>) {
  return ApiDecimalProperty({ ...options, required: false });
}

/**
 * Decorator for percentage fields (0-100 with decimal precision)
 */
export function ApiPercentageProperty(options: {
  description: string;
  example: string;
  required?: boolean;
} & Partial<ApiPropertyOptions>) {
  const { description, example, required = true, ...apiOptions } = options;

  const decorators = [
    ApiProperty({
      description: `${description} (percentage as decimal string, e.g., "15.5" for 15.5%)`,
      example,
      type: 'string',
      pattern: '^(100(\\.0{1,2})?|\\d{1,2}(\\.\\d{1,2})?)$',
      format: 'percentage',
      minimum: 0,
      maximum: 100,
      ...apiOptions,
    }),
    IsString({ message: 'Percentage must be a string for decimal precision' }),
    Matches(/^(100(\.0{1,2})?|\d{1,2}(\.\d{1,2})?)$/, {
      message: 'Percentage must be between 0 and 100 with up to 2 decimal places',
    }),
  ];

  if (!required) {
    decorators.push(IsOptional());
  }

  return applyDecorators(...decorators);
}

/**
 * Decorator for quantity fields that can be decimal (for fractional quantities)
 */
export function ApiQuantityProperty(options: {
  description: string;
  example: string;
  required?: boolean;
  allowFractional?: boolean;
} & Partial<ApiPropertyOptions>) {
  const { description, example, required = true, allowFractional = false, ...apiOptions } = options;

  const pattern = allowFractional ? '^\\d+(\\.\\d{1,3})?$' : '^\\d+$';
  const patternMessage = allowFractional 
    ? 'Quantity must be a positive number with up to 3 decimal places'
    : 'Quantity must be a positive integer';

  const decorators = [
    ApiProperty({
      description: `${description}${allowFractional ? ' (supports fractional quantities)' : ' (integer only)'}`,
      example,
      type: allowFractional ? 'string' : 'integer',
      pattern: allowFractional ? pattern : undefined,
      minimum: 0,
      ...apiOptions,
    }),
    allowFractional ? IsString() : IsString(),
    Matches(new RegExp(pattern), { message: patternMessage }),
  ];

  if (!required) {
    decorators.push(IsOptional());
  }

  return applyDecorators(...decorators);
}
