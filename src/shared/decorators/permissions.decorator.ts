import { SetMetadata } from '@nestjs/common';

/**
 * Permission metadata key used by the guard to extract permission requirements
 */
export const PERMISSIONS_KEY = 'permissions';

/**
 * Resource-based permission pattern map for consistent permission naming
 */
export enum ResourceActions {
  CREATE = 'create',
  READ = 'read',
  UPDATE = 'update',
  DELETE = 'delete',
  LIST = 'list',
  APPROVE = 'approve',
  REJECT = 'reject',
  EXPORT = 'export',
  IMPORT = 'import',
  MANAGE = 'manage',
  ASSIGN = 'assign',
}

/**
 * Core resources in the system
 */
export enum Resources {
  USERS = 'users',
  PRODUCTS = 'products',
  ORDERS = 'orders',
  INVOICES = 'invoices',
  CUSTOMERS = 'customers',
  REPORTS = 'reports',
  SETTINGS = 'settings',
  DASHBOARD = 'dashboard',
  TRAININGS = 'trainings',
}

/**
 * Helper to create permission strings in the 'resource:action' format
 *
 * @param resource The resource being accessed
 * @param action The action being performed
 * @returns Formatted permission string
 */
export const createPermission = (
  resource: Resources | string,
  action: ResourceActions | string,
): string => `${resource}:${action}`;

/**
 * Decorator to require specific permissions to access an endpoint
 * 
 * @param permissions Array of permission strings required
 * @returns Decorator function
 * 
 * @example
 * // Require a single permission
 * @RequirePermissions(createPermission(Resources.USERS, ResourceActions.READ))
 * 
 * @example
 * // Require multiple permissions (ANY of them - OR logic)
 * @RequirePermissions(
 *   createPermission(Resources.USERS, ResourceActions.CREATE),
 *   createPermission(Resources.USERS, ResourceActions.UPDATE)
 * )
 */
export const RequirePermissions = (...permissions: string[]) => 
  SetMetadata(PERMISSIONS_KEY, permissions);

/**
 * Decorator to require all specified permissions to access an endpoint
 * 
 * @param permissions Array of permission strings required
 * @returns Decorator function
 * 
 * @example
 * // Require multiple permissions (ALL of them - AND logic)
 * @RequireAllPermissions([
 *   createPermission(Resources.USERS, ResourceActions.DELETE),
 *   createPermission(Resources.SETTINGS, ResourceActions.MANAGE)
 * ])
 */
export const RequireAllPermissions = (permissions: string[]) => 
  SetMetadata(`${PERMISSIONS_KEY}:all`, permissions);

/**
 * Decorator to exempt a route from permission checks
 * Use this carefully only for public routes
 * 
 * @returns Decorator function
 */
export const PublicRoute = () => SetMetadata('isPublic', true);
