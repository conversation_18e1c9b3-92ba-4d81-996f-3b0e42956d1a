import { <PERSON><PERSON><PERSON>, Logger } from '@nestjs/common';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { GraphQLModule } from '@nestjs/graphql';
import { ApolloDriver, ApolloDriverConfig } from '@nestjs/apollo';
import { MongooseModule } from '@nestjs/mongoose';
import { CacheModule } from '@nestjs/cache-manager';
import { RabbitMQModule } from '@golevelup/nestjs-rabbitmq';
import { EventEmitterModule } from '@nestjs/event-emitter';
import { ScheduleModule } from '@nestjs/schedule';
import { join } from 'path';
import * as redisStore from 'cache-manager-ioredis';

// Core modules
import { CoreModule } from '@core/core.module';
import { SharedModule } from '@shared/shared.module';
import { InfraModule } from '@infra/infra.module';
import { GraphqlModule } from '@graphql/graphql.module';

// App modules
import { UserModule } from '@apps/user/user.module';
import { ProductModule } from '@apps/product/product.module';
import { OrderModule } from '@apps/order/order.module';
import { FinanceModule } from '@apps/finance/finance.module';
import { TailorModule } from '@apps/tailor/tailor.module';
import { MessagingModule } from '@apps/messaging/messaging.module';
import { TrainingModule } from '@apps/training/training.module';
import { SupplyChainModule } from '@apps/supply-chain/supply-chain.module';

@Module({
  imports: [
    // Configuration
    ConfigModule.forRoot({
      isGlobal: true,
      envFilePath: '.env',
    }),
    
    // Schedule Module for cron jobs
    ScheduleModule.forRoot(),
    
    // GraphQL
    GraphQLModule.forRootAsync<ApolloDriverConfig>({
      driver: ApolloDriver,
      imports: [ConfigModule],
      useFactory: (configService: ConfigService) => ({
        autoSchemaFile: join(process.cwd(), 'src/schema.gql'),
        sortSchema: true,
        playground: true,
        debug: configService.get('NODE_ENV') !== 'production',
      }),
      inject: [ConfigService],
    }),
    
    // MongoDB
    MongooseModule.forRootAsync({
      imports: [ConfigModule],
      useFactory: async (configService: ConfigService) => ({
        uri: configService.get<string>('MONGODB_URI'),
      }),
      inject: [ConfigService],
    }),
    
    // Redis Cache
    CacheModule.registerAsync({
      isGlobal: true,
      imports: [ConfigModule],
      useFactory: (configService: ConfigService) => ({
        store: redisStore,
        host: configService.get('REDIS_HOST') || 'localhost',
        port: configService.get('REDIS_PORT') || 6379,
        ttl: configService.get('CACHE_TTL') || 300,
      }),
      inject: [ConfigService],
    }),
    
    // RabbitMQ
    RabbitMQModule.forRootAsync({
      imports: [ConfigModule],
      useFactory: (configService: ConfigService) => {
        const logger = new Logger('RabbitMQConfig');
        const uri = configService.get<string>('RABBITMQ_URL');
        const exchangeName = configService.get<string>('RABBITMQ_EXCHANGE') || 'erp_exchange';
        const queueName = configService.get<string>('RABBITMQ_QUEUE') || 'erp_queue';
        const routingKey = configService.get<string>('RABBITMQ_ROUTING_KEY') || 'erp_routing_key';
        
        logger.log(`RabbitMQ Configuration: URI=${uri}, Exchange=${exchangeName}, Queue=${queueName}, RoutingKey=${routingKey}`);
        
        // Check if RabbitMQ is required or optional based on environment
        const isRabbitMQRequired = configService.get<string>('RABBITMQ_REQUIRED') !== 'false';
        
        if (!uri) {
          const message = 'RABBITMQ_URL environment variable is not set';
          if (isRabbitMQRequired) {
            logger.error(message);
            throw new Error(message);
          } else {
            logger.warn(`${message}, but RabbitMQ is marked as optional. Continuing without RabbitMQ.`);
            // Return minimal config to allow the app to start without RabbitMQ
            return {
              uri: 'amqp://guest:guest@localhost:5672',
              exchanges: [{ name: exchangeName, type: 'topic' }],
              connectionInitOptions: { wait: false },
              defaultRpcTimeout: 10000,
            };
          }
        }
        
        return {
          uri,
          exchanges: [
            {
              name: exchangeName,
              type: 'topic',
              options: { durable: true },
            },
          ],
          queues: [
            {
              name: queueName,
              exchange: exchangeName,
              routingKey,
              options: { durable: true },
            },
          ],
          defaultRpcTimeout: 10000,
          connectionInitOptions: { wait: false },
          enableControllerDiscovery: true,
          connectionManagerOptions: {
            heartbeatIntervalInSeconds: 5,
            reconnectTimeInSeconds: 5,
          },
          // Add handlers for connection events
          onConnectionError: (err) => {
            logger.error(`RabbitMQ Connection Error: ${err.message}`, err.stack);
          },
          onConnectionClose: (err) => {
            logger.warn(`RabbitMQ Connection Closed: ${err ? err.message : 'No error provided'}`);
          },
        };
      },
      inject: [ConfigService],
    }),
    
    // Event Emitter
    EventEmitterModule.forRoot(),
    
    // Core modules
    CoreModule,
    SharedModule,
    InfraModule,
    GraphqlModule,
    
    // App modules
    UserModule,
    ProductModule,
    OrderModule,
    FinanceModule,
    TailorModule,
    MessagingModule,
    TrainingModule,
    SupplyChainModule,
  ],
})
export class AppModule {}
