import { Module, Global } from '@nestjs/common';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { I18nModule as NestI18nModule, I18nJsonLoader, HeaderResolver, CookieResolver, AcceptLanguageResolver } from 'nestjs-i18n';
import { join } from 'path';
import { I18nService } from './i18n.service';
import { I18nController } from './i18n.controller';

@Global()
@Module({
  imports: [
    NestI18nModule.forRootAsync({
      imports: [ConfigModule],
      useFactory: (configService: ConfigService) => ({
        fallbackLanguage: configService.get<string>('I18N_DEFAULT_LANG', 'en'),
        loaderOptions: {
          path: join(__dirname, '/locales/'),
          watch: configService.get<string>('NODE_ENV') !== 'production',
        },
        typesOutputPath: configService.get<string>('NODE_ENV') !== 'production'
          ? join(__dirname, '../../src/i18n/generated/i18n.types.ts')
          : undefined,
      }),
      resolvers: [
        { use: HeaderResolver, options: ['x-custom-lang', 'x-lang', 'Accept-Language'] },
        { use: CookieResolver, options: ['lang', 'locale'] },
        AcceptLanguageResolver,
      ],
      inject: [ConfigService],
      loader: I18nJsonLoader,
    }),
  ],
  providers: [I18nService],
  controllers: [I18nController],
  exports: [NestI18nModule, I18nService],
})
export class I18nModule {}
