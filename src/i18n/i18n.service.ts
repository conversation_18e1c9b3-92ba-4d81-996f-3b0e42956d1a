import { Injectable } from '@nestjs/common';
import { I18nService as NestI18nService, I18nTranslation } from 'nestjs-i18n';

@Injectable()
export class I18nService {
  constructor(private readonly i18n: NestI18nService) {}

  /**
   * Translate a key
   * 
   * @param key Translation key
   * @param args Optional arguments for interpolation
   * @param lang Optional language code (defaults to request language or fallback)
   * @returns Translated string
   */
  translate(key: string, args?: Record<string, any>, lang?: string): string {
    return this.i18n.translate(key, { args, lang });
  }

  /**
   * Get available languages
   * 
   * @returns Array of available language codes
   */
  getLanguages(): string[] {
    return this.i18n.getSupportedLanguages();
  }

  /**
   * Get translation for current language
   * 
   * @param namespace Namespace (e.g., 'common')
   * @param lang Language code
   * @returns Complete translations for namespace
   */
  getTranslations(namespace: string, lang: string): Record<string, any> {
    const translations = this.i18n.getTranslations();
    // Check if language exists and has the namespace
    if (translations && translations[lang] && translations[lang][namespace]) {
      return translations[lang][namespace] as Record<string, any>;
    }
    return {};
  }

  /**
   * Get all translations for all languages for a namespace
   * 
   * @param namespace Namespace (e.g., 'common')
   * @returns Translations by language
   */
  getAllTranslations(namespace: string): Record<string, Record<string, any>> {
    const languages = this.getLanguages();
    const translations: Record<string, Record<string, any>> = {};

    for (const lang of languages) {
      translations[lang] = this.getTranslations(namespace, lang);
    }

    return translations;
  }
}
