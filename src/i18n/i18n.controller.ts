import { Controller, Get, Param, Query, UseGuards } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiParam, ApiQuery, ApiBearerAuth } from '@nestjs/swagger';
import { JwtAuthGuard } from '@core/auth/guards/jwt-auth.guard';
import { I18nService } from './i18n.service';

@ApiTags('i18n')
@ApiBearerAuth()
@Controller('i18n')
export class I18nController {
  constructor(private readonly i18nService: I18nService) {}

  @Get('languages')
  @ApiOperation({ 
    summary: 'Get available languages', 
    description: 'Returns a list of all supported language codes in the system' 
  })
  @ApiResponse({ 
    status: 200, 
    description: 'List of available languages',
    schema: {
      type: 'object',
      properties: {
        languages: {
          type: 'array',
          items: {
            type: 'string',
            example: 'en',
          },
        },
      },
    },
  })
  getLanguages() {
    const languages = this.i18nService.getLanguages();
    return { languages };
  }

  @Get('translations/:lang')
  @ApiOperation({ 
    summary: 'Get translations for a language', 
    description: 'Returns all translations for a specific language and namespace' 
  })
  @ApiParam({ 
    name: 'lang', 
    description: 'Language code (e.g., en, es, ru)', 
    required: true 
  })
  @ApiQuery({ 
    name: 'namespace', 
    description: 'Translation namespace (default: common)', 
    required: false 
  })
  @ApiResponse({ 
    status: 200, 
    description: 'Translation data for the specified language and namespace',
    schema: {
      type: 'object',
      properties: {
        language: {
          type: 'string',
          example: 'en',
        },
        namespace: {
          type: 'string',
          example: 'common',
        },
        translations: {
          type: 'object',
          example: {
            welcome: 'Welcome to the ERP System',
            common: {
              save: 'Save',
              cancel: 'Cancel',
            },
          },
        },
      },
    },
  })
  @ApiResponse({ status: 404, description: 'Language or namespace not found' })
  getTranslations(
    @Param('lang') lang: string, 
    @Query('namespace') namespace: string = 'common'
  ) {
    const translations = this.i18nService.getTranslations(namespace, lang);
    return {
      language: lang,
      namespace,
      translations,
    };
  }

  @Get('translations')
  @ApiOperation({ 
    summary: 'Get all translations', 
    description: 'Returns all translations for all languages for a specific namespace' 
  })
  @ApiQuery({ 
    name: 'namespace', 
    description: 'Translation namespace (default: common)', 
    required: false 
  })
  @ApiResponse({ 
    status: 200, 
    description: 'All translation data for all languages for the specified namespace',
    schema: {
      type: 'object',
      properties: {
        namespace: {
          type: 'string',
          example: 'common',
        },
        translations: {
          type: 'object',
          example: {
            en: {
              welcome: 'Welcome to the ERP System',
            },
            es: {
              welcome: 'Bienvenido al Sistema ERP',
            },
            ru: {
              welcome: 'Добро пожаловать в систему ERP',
            },
          },
        },
      },
    },
  })
  @UseGuards(JwtAuthGuard)
  getAllTranslations(@Query('namespace') namespace: string = 'common') {
    const translations = this.i18nService.getAllTranslations(namespace);
    return {
      namespace,
      translations,
    };
  }
}
