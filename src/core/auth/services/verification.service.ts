import { Injectable, Logger, NotFoundException, BadRequestException } from '@nestjs/common';
import { PrismaService } from '@core/prisma/prisma.service';
import { ConfigService } from '@nestjs/config';
import { JwtService } from '@nestjs/jwt';
import { v4 as uuidv4 } from 'uuid';
import { addDays, isPast } from 'date-fns';

@Injectable()
export class VerificationService {
  private readonly logger = new Logger(VerificationService.name);

  constructor(
    private readonly prismaService: PrismaService,
    private readonly configService: ConfigService,
    private readonly jwtService: JwtService,
  ) {}

  /**
   * Create an email verification token for a user
   * @param userId The user ID to create a token for
   * @returns The created verification token
   */
  async createEmailVerificationToken(userId: string) {
    // Check if user exists
    const user = await this.prismaService.user.findUnique({
      where: { id: userId },
    });

    if (!user) {
      throw new NotFoundException(`User with ID ${userId} not found`);
    }

    // Delete any existing email verification tokens for this user
    await this.prismaService.verificationToken.deleteMany({
      where: {
        userId,
        type: 'email_verification',
      },
    });

    // Create a new token
    const token = uuidv4();
    const expiresAt = addDays(new Date(), 3); // Token expires in 3 days

    // Save token to database
    const verificationToken = await this.prismaService.verificationToken.create({
      data: {
        userId,
        token,
        type: 'email_verification',
        expiresAt,
      },
    });

    this.logger.log(`Created email verification token for user ${userId}`);
    return verificationToken;
  }

  /**
   * Verify an email verification token
   * @param token The token to verify
   * @returns The user ID if verification is successful
   */
  async verifyEmailToken(token: string) {
    // Find the token in the database
    const verificationToken = await this.prismaService.verificationToken.findUnique({
      where: { token },
    });

    if (!verificationToken) {
      throw new BadRequestException('Invalid verification token');
    }

    if (verificationToken.type !== 'email_verification') {
      throw new BadRequestException('Invalid token type');
    }

    if (isPast(verificationToken.expiresAt)) {
      // Delete expired token
      await this.prismaService.verificationToken.delete({
        where: { id: verificationToken.id },
      });
      throw new BadRequestException('Verification token has expired');
    }

    // Update user's email verification status
    await this.prismaService.user.update({
      where: { id: verificationToken.userId },
      data: { emailVerified: true },
    });

    // Delete the token after successful verification
    await this.prismaService.verificationToken.delete({
      where: { id: verificationToken.id },
    });

    this.logger.log(`Email verified for user ${verificationToken.userId}`);
    return verificationToken.userId;
  }
}
