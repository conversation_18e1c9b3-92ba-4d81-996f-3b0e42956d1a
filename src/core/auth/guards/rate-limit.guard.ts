import { Injectable, CanActivate, ExecutionContext, HttpException, HttpStatus } from '@nestjs/common';
import { Observable } from 'rxjs';
import { RedisService } from '@infra/redis/redis.service';

@Injectable()
export class RateLimitGuard implements CanActivate {
  constructor(private readonly redisService: RedisService) {}

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const request = context.switchToHttp().getRequest();
    const ip = request.ip;
    const endpoint = request.path;
    const key = `rate-limit:${ip}:${endpoint}`;
    
    // Get current count
    const current = await this.redisService.get(key);
    const count = current ? parseInt(current, 10) : 0;
    
    // Set limits based on endpoint
    let limit = 60; // Default limit per minute
    let ttl = 60; // Default TTL in seconds
    
    if (endpoint.includes('/auth/login')) {
      limit = 5; // Stricter limit for login attempts
      ttl = 60; // 1 minute
    } else if (endpoint.includes('/auth/register')) {
      limit = 3; // Even stricter for registration
      ttl = 300; // 5 minutes
    }
    
    if (count >= limit) {
      throw new HttpException(
        'Too many requests, please try again later',
        HttpStatus.TOO_MANY_REQUESTS,
      );
    }
    
    // Increment count and set expiry
    if (count === 0) {
      // First request - set initial value with expiry
      await this.redisService.set(key, '1', ttl);
    } else {
      // Increment the counter
      await this.redisService.increment(key);
      // Ensure TTL is set (in case it was lost)
      await this.redisService.expire(key, ttl);
    }
    
    return true;
  }
}
