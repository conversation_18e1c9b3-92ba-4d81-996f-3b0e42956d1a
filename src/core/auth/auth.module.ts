import { Module } from '@nestjs/common';
import { JwtModule } from '@nestjs/jwt';
import { PassportModule } from '@nestjs/passport';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { AuthService } from './auth.service';
import { AuthController } from './auth.controller';
import { JwtStrategy } from './strategies/jwt.strategy';
import { PrismaModule } from '@core/prisma/prisma.module';
import { RabbitMQModule } from '@infra/rabbitmq/rabbitmq.module';
import { WebsocketModule } from '@infra/websocket/websocket.module';
import { VerificationService } from './services/verification.service';
import { SharedModule } from '@shared/shared.module';
import { RedisModule } from '@infra/redis/redis.module';
import { RateLimitGuard } from './guards/rate-limit.guard';
import { LoggerModule } from '@core/logger/logger.module';

@Module({
  imports: [
    PassportModule,
    JwtModule.registerAsync({
      imports: [ConfigModule],
      useFactory: async (configService: ConfigService) => ({
        secret: configService.get<string>('JWT_SECRET'),
        signOptions: {
          expiresIn: configService.get<string>('JWT_EXPIRES_IN', '1d'),
        },
      }),
      inject: [ConfigService],
    }),
    PrismaModule,
    LoggerModule,
    ConfigModule,
    RabbitMQModule,
    WebsocketModule,
    SharedModule,
    RedisModule,
  ],
  providers: [AuthService, JwtStrategy, VerificationService, RateLimitGuard],
  controllers: [AuthController],
  exports: [AuthService, VerificationService],
})
export class AuthModule {}
