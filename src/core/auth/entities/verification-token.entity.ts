import { ObjectType, Field, ID } from '@nestjs/graphql';
import { ApiProperty } from '@nestjs/swagger';

@ObjectType()
export class VerificationToken {
  @Field(() => ID)
  @ApiProperty({
    description: 'Verification token ID',
    example: 'a1b2c3d4-e5f6-7890-abcd-ef1234567890',
  })
  id: string;

  @Field()
  @ApiProperty({
    description: 'User ID associated with this token',
    example: 'a1b2c3d4-e5f6-7890-abcd-ef1234567890',
  })
  userId: string;

  @Field()
  @ApiProperty({
    description: 'Token value',
    example: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...',
  })
  token: string;

  @Field()
  @ApiProperty({
    description: 'Token type (email verification, password reset, etc.)',
    example: 'email_verification',
  })
  type: string;

  @Field()
  @ApiProperty({
    description: 'Token expiration date',
    example: '2023-01-01T00:00:00.000Z',
  })
  expiresAt: Date;

  @Field()
  @ApiProperty({
    description: 'Token creation date',
    example: '2023-01-01T00:00:00.000Z',
  })
  createdAt: Date;
}
