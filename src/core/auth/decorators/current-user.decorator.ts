import { createParamDecorator, ExecutionContext } from '@nestjs/common';
import { GqlExecutionContext } from '@nestjs/graphql';

export const CurrentUser = createParamDecorator(
  (data: unknown, context: ExecutionContext) => {
    // Check if it's a GraphQL context
    const gqlContext = GqlExecutionContext.create(context);
    if (gqlContext.getType() === 'graphql') {
      const ctx = gqlContext.getContext();
      return ctx.req.user;
    }
    
    // For REST API
    const request = context.switchToHttp().getRequest();
    return request.user;
  },
);
