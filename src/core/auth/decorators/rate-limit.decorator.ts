import { applyDecorators } from '@nestjs/common';
import { ApiTooManyRequestsResponse } from '@nestjs/swagger';
import { UseGuards } from '@nestjs/common';
import { RateLimitGuard } from '../guards/rate-limit.guard';

/**
 * Applies rate limiting to an endpoint with proper Swagger documentation
 * @returns Decorator that applies rate limiting and documents it in Swagger
 */
export function RateLimit() {
  return applyDecorators(
    UseGuards(RateLimitGuard),
    ApiTooManyRequestsResponse({
      description: 'Too many requests, please try again later',
    }),
  );
}
