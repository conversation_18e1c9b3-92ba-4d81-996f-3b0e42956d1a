import { Injectable, BadRequestException, ConflictException, UnauthorizedException, NotFoundException, Logger } from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';
import { PrismaService } from '@core/prisma/prisma.service';
import { RabbitMQService } from '@infra/rabbitmq/rabbitmq.service';
import { WebsocketService } from '@infra/websocket/websocket.service';
import { EmailService } from '@shared/services/email.service';
import { VerificationService } from './services/verification.service';
import { plainToInstance } from 'class-transformer';
import * as bcrypt from 'bcrypt';

import { RegisterDto } from './dto/register.dto';
import { AuthResponseDto } from './dto/auth-response.dto';
import { UserDto } from '@apps/user/dto/user.dto';

@Injectable()
export class AuthService {
  private readonly logger = new Logger(AuthService.name);

  constructor(
    private readonly jwtService: JwtService,
    private readonly prismaService: PrismaService,
    private readonly rabbitMQService: RabbitMQService,
    private readonly websocketService: WebsocketService,
    private readonly emailService: EmailService,
    private readonly verificationService: VerificationService,
  ) {}

  async validateUser(email: string, password: string): Promise<any> {
    const user = await this.prismaService.user.findUnique({
      where: { email },
    });

    if (user && (await bcrypt.compare(password, user.password))) {
      const { password, ...result } = user;
      return result;
    }
    return null;
  }

  async login(user: any): Promise<AuthResponseDto> {
    const payload = { email: user.email, sub: user.id, roles: user.roles };
    
    // Convert to UserDto to ensure we only return allowed fields
    const userDto = plainToInstance(UserDto, user, { excludeExtraneousValues: true });
    
    return {
      access_token: this.jwtService.sign(payload),
      user: userDto,
    };
  }

  async register(registerDto: RegisterDto): Promise<AuthResponseDto> {
    // Check if email already exists
    const existingUser = await this.prismaService.user.findUnique({
      where: { email: registerDto.email },
    });

    if (existingUser) {
      throw new ConflictException('Email already exists');
    }

    // Hash password
    const hashedPassword = await bcrypt.hash(registerDto.password, 10);

    // Create new user with default role
    const newUser = await this.prismaService.user.create({
      data: {
        email: registerDto.email,
        password: hashedPassword,
        firstName: registerDto.firstName,
        lastName: registerDto.lastName,
        phone: registerDto.phoneNumber, // Map phoneNumber from DTO to phone in DB
        roles: ['USER'], // Default role for new users
        emailVerified: false, // New users start with unverified email
      },
    });

    // Create verification token and send verification email
    try {
      const verificationToken = await this.verificationService.createEmailVerificationToken(newUser.id);
      await this.emailService.sendVerificationEmail(newUser, verificationToken.token);
      this.logger.log(`Verification email sent to ${newUser.email}`);
    } catch (error) {
      this.logger.error(`Failed to send verification email to ${newUser.email}`, error.stack);
      // We don't want to fail registration if email sending fails
      // The user can request a new verification email later
    }

    // Notify about new user registration
    await this.notifyUserRegistration(newUser);

    // Generate JWT token and return user data without password
    const { password, ...userData } = newUser;
    return this.login(userData);
  }

  /**
   * Resend verification email to a user
   * @param email The email address of the user
   */
  async resendVerificationEmail(email: string): Promise<void> {
    // Find user by email
    const user = await this.prismaService.user.findUnique({
      where: { email },
    });

    if (!user) {
      throw new NotFoundException(`User with email ${email} not found`);
    }

    // Check if email is already verified
    if (user.emailVerified) {
      throw new ConflictException('Email is already verified');
    }

    // Create new verification token and send email
    const verificationToken = await this.verificationService.createEmailVerificationToken(user.id);
    await this.emailService.sendVerificationEmail(user, verificationToken.token);
    
    this.logger.log(`Verification email resent to ${email}`);
  }

  /**
   * Notify relevant systems about new user registration
   */
  private async notifyUserRegistration(user: any): Promise<void> {
    // Remove sensitive data
    const { password, ...userData } = user;
    
    // Publish event to RabbitMQ
    await this.rabbitMQService.publish({
      type: 'user_registered',
      data: userData,
    });

    // Send notification to admin users via WebSocket
    this.websocketService.sendToRole('ADMIN', 'user_registered', {
      userId: user.id,
      email: user.email,
      name: `${user.firstName} ${user.lastName}`,
      timestamp: new Date(),
    });
  }
}
