import { Injectable, Logger } from '@nestjs/common';
import { PassportStrategy } from '@nestjs/passport';
import { ExtractJwt, Strategy } from 'passport-jwt';
import { ConfigService } from '@nestjs/config';

@Injectable()
export class JwtStrategy extends PassportStrategy(Strategy) {
  private readonly logger = new Logger(JwtStrategy.name);
  
  constructor(private configService: ConfigService) {
    const secretKey = configService.get<string>('JWT_SECRET') || 'default_jwt_secret_for_development';
    
    super({
      jwtFromRequest: ExtractJwt.fromAuthHeaderAsBearerToken(),
      ignoreExpiration: false,
      secretOrKey: secretKey,
    });
    
    // Log warning after super() is called
    if (!configService.get<string>('JWT_SECRET')) {
      this.logger.warn('JWT_SECRET not found in environment variables. Using default secret for development. This is not secure for production!');
    }
  }

  async validate(payload: any) {
    return { 
      userId: payload.sub, 
      username: payload.username,
      roles: payload.roles || [],
    };
  }
}
