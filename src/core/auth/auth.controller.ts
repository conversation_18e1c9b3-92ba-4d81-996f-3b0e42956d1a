import { Controller, Post, Body, HttpCode, HttpStatus, UseGuards, BadRequestException, ConflictException, Get, Param, Query, NotFoundException, UnauthorizedException } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiUnauthorizedResponse, ApiBadRequestResponse, ApiConflictResponse, ApiParam, ApiQuery } from '@nestjs/swagger';
import { AuthService } from './auth.service';
import { RegisterDto } from './dto/register.dto';
import { LoginDto } from './dto/login.dto';
import { AuthResponseDto } from './dto/auth-response.dto';
import { RateLimit } from './decorators/rate-limit.decorator';
import { VerifyEmailDto } from './dto/verify-email.dto';
import { VerificationService } from './services/verification.service';

@ApiTags('Authentication')
@Controller('auth')
export class AuthController {
  constructor(
    private readonly authService: AuthService,
    private readonly verificationService: VerificationService,
  ) {}

  @Post('login')
  @HttpCode(HttpStatus.OK)
  @RateLimit()
  @ApiOperation({ summary: 'User login', description: 'Authenticates a user and returns a JWT token' })
  @ApiResponse({ status: 200, description: 'Login successful', type: AuthResponseDto })
  @ApiUnauthorizedResponse({ description: 'Invalid credentials' })
  @ApiBadRequestResponse({ description: 'Invalid input data' })
  async login(@Body() loginDto: LoginDto) {
    try {
      const user = await this.authService.validateUser(
        loginDto.email,
        loginDto.password,
      );
      
      if (!user) {
        throw new UnauthorizedException('Invalid credentials');
      }
      
      return this.authService.login(user);
    } catch (error) {
      if (error instanceof UnauthorizedException) {
        throw error;
      }
      throw new BadRequestException(error.message);
    }
  }

  @Post('register')
  @HttpCode(HttpStatus.CREATED)
  @RateLimit()
  @ApiOperation({ summary: 'User registration', description: 'Registers a new user and returns a JWT token' })
  @ApiResponse({ status: 201, description: 'User successfully registered', type: AuthResponseDto })
  @ApiBadRequestResponse({ description: 'Invalid input data' })
  @ApiConflictResponse({ description: 'Email already exists' })
  async register(@Body() registerDto: RegisterDto) {
    try {
      return await this.authService.register(registerDto);
    } catch (error) {
      if (error.code === 'P2002') { // Prisma unique constraint violation
        throw new ConflictException('Email already exists');
      }
      throw new BadRequestException(error.message);
    }
  }
  
  @Post('verify-email')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: 'Verify email address', description: 'Verifies a user\'s email address using the token sent to their email' })
  @ApiResponse({ status: 200, description: 'Email successfully verified' })
  @ApiBadRequestResponse({ description: 'Invalid or expired token' })
  async verifyEmail(@Body() verifyEmailDto: VerifyEmailDto) {
    try {
      const userId = await this.verificationService.verifyEmailToken(verifyEmailDto.token);
      return { message: 'Email verified successfully' };
    } catch (error) {
      throw new BadRequestException(error.message);
    }
  }
  
  @Post('resend-verification')
  @HttpCode(HttpStatus.OK)
  @RateLimit()
  @ApiOperation({ summary: 'Resend verification email', description: 'Resends the verification email to the user' })
  @ApiResponse({ status: 200, description: 'Verification email sent' })
  @ApiBadRequestResponse({ description: 'Invalid email' })
  @ApiQuery({ name: 'email', required: true, description: 'User email address' })
  async resendVerificationEmail(@Query('email') email: string) {
    try {
      await this.authService.resendVerificationEmail(email);
      return { message: 'Verification email sent' };
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw new NotFoundException(error.message);
      }
      throw new BadRequestException(error.message);
    }
  }
}
