import { Module } from '@nestjs/common';
import { PrismaService } from './prisma/prisma.service';
import { ConfigModule } from '@nestjs/config';
import { AuthModule } from './auth/auth.module';
import { LoggerModule } from './logger/logger.module';
import { SchedulerModule } from './scheduler/scheduler.module';

@Module({
  imports: [
    ConfigModule,
    AuthModule,
    LoggerModule,
    SchedulerModule,
  ],
  providers: [PrismaService],
  exports: [PrismaService, AuthModule, LoggerModule, SchedulerModule],
})
export class CoreModule {}
