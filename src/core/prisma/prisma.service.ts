import { Injectable, OnM<PERSON>uleInit, OnM<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON> } from '@nestjs/common';
import { PrismaClient } from '@prisma/client';

@Injectable()
export class PrismaService extends PrismaClient implements OnModuleInit, OnModuleDestroy {
  private readonly logger = new Logger(PrismaService.name);

  constructor() {
    super({
      log: [
        { level: 'query', emit: 'stdout' },
        { level: 'info', emit: 'stdout' },
        { level: 'warn', emit: 'stdout' },
        { level: 'error', emit: 'stdout' },
      ],
    });
  }

  async onModuleInit() {
    try {
      this.logger.log('Connecting to PostgreSQL database...');
      await this.$connect();
      this.logger.log('Successfully connected to PostgreSQL database');
    } catch (error) {
      this.logger.error(`Failed to connect to PostgreSQL database: ${error.message}`, error.stack);
      throw error;
    }
  }

  async onModule<PERSON><PERSON>roy() {
    try {
      this.logger.log('Disconnecting from PostgreSQL database...');
      await this.$disconnect();
      this.logger.log('Successfully disconnected from PostgreSQL database');
    } catch (error) {
      this.logger.error(`Error disconnecting from PostgreSQL database: ${error.message}`, error.stack);
      // We don't rethrow here as we're shutting down anyway
    }
  }
}
