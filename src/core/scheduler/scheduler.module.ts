import { Modu<PERSON> } from '@nestjs/common';
import { ScheduleModule } from '@nestjs/schedule';
import { NotificationSchedulerService } from './notification.scheduler.service';
import { DataSyncSchedulerService } from './data-sync.scheduler.service';
import { InvoiceReminderSchedulerService } from './invoice-reminder.scheduler.service';
import { StockAlertSchedulerService } from './stock-alert.scheduler.service';
import { InfraModule } from '../../infra/infra.module';
import { SharedModule } from '../../shared/shared.module';
import { PrismaService } from '../prisma/prisma.service';

@Module({
  imports: [
    ScheduleModule.forRoot(),
    InfraModule,
    SharedModule,
  ],
  providers: [
    NotificationSchedulerService,
    DataSyncSchedulerService,
    InvoiceReminderSchedulerService,
    StockAlertSchedulerService,
    PrismaService,
  ],
  exports: [
    NotificationSchedulerService,
    DataSyncSchedulerService,
    InvoiceReminderSchedulerService,
    StockAlertSchedulerService,
  ],
})
export class SchedulerModule {}
