import { Injectable, Logger } from '@nestjs/common';
import { Cron, CronExpression } from '@nestjs/schedule';
import { PrismaService } from '../prisma/prisma.service';
import { WebsocketService } from '../../infra/websocket/websocket.service';
import { RabbitMQService } from '../../infra/rabbitmq/rabbitmq.service';
import { OrderStatus } from '@prisma/client';

interface ProductStock {
  id: string;
  name: string;
  sku: string;
  quantity: number;
  reorderLevel?: number;
  categoryId?: string;
  updatedAt: Date;
}

interface ReorderSuggestion {
  id: string;
  name: string;
  sku: string;
  currentStock: number;
  dailySalesRate: string;
  daysOfInventoryLeft: number;
  suggestedOrderQuantity: number;
}

interface CategoryReport {
  id: string;
  name: string;
  productCount: number;
  inventoryValue: string;
  lowStockCount: number;
  outOfStockCount: number;
  stockHealth: string;
}

interface InventoryReport {
  generatedAt: Date;
  summary: {
    totalProducts: number;
    totalValue: string;
    lowStockProducts: number;
    outOfStockProducts: number;
    stockHealth?: string;
  };
  byCategory: CategoryReport[];
}

@Injectable()
export class StockAlertSchedulerService {
  private readonly logger = new Logger(StockAlertSchedulerService.name);

  constructor(
    private readonly prismaService: PrismaService,
    private readonly websocketService: WebsocketService,
    private readonly rabbitMQService: RabbitMQService,
  ) {}

  /**
   * Check inventory levels and send alerts for low stock
   * Runs three times a day: 8 AM, 2 PM, and 8 PM
   */
  @Cron('0 8,14,20 * * *')
  async checkLowStockLevels() {
    this.logger.log('Checking low stock inventory levels');
    
    try {
      // Get products with stock below their respective reorder levels
      const lowStockProducts: ProductStock[] = await this.prismaService.product.findMany({
        where: {
          quantity: {
            lte: 10, // Default reorder level if not specified
          },
          isActive: true,
        },
        orderBy: {
          quantity: 'asc',
        },
      });
      
      this.logger.log(`Found ${lowStockProducts.length} products with low stock`);
      
      if (lowStockProducts.length > 0) {
        // Categorize products by urgency level
        const criticalProducts = lowStockProducts.filter(p => p.quantity === 0);
        const urgentProducts = lowStockProducts.filter(p => p.quantity > 0 && p.quantity <= 5); // Half of default reorder level
        const warningProducts = lowStockProducts.filter(p => p.quantity > 5 && p.quantity <= 10); // Up to default reorder level
        
        // Send alert via WebSocket to inventory managers
        this.websocketService.sendToRole('inventory_manager', 'low_stock_alert', {
          message: `${lowStockProducts.length} products need attention: ${criticalProducts.length} critical, ${urgentProducts.length} urgent, ${warningProducts.length} warning`,
          timestamp: new Date(),
          summary: {
            total: lowStockProducts.length,
            critical: criticalProducts.length,
            urgent: urgentProducts.length,
            warning: warningProducts.length,
          },
          products: {
            critical: criticalProducts.map(p => ({
              id: p.id,
              name: p.name,
              sku: p.sku,
              quantity: p.quantity,
              reorderLevel: p.reorderLevel || 10, // Default reorder level
              category: p.categoryId,
            })),
            urgent: urgentProducts.map(p => ({
              id: p.id,
              name: p.name,
              sku: p.sku,
              quantity: p.quantity,
              reorderLevel: p.reorderLevel || 10, // Default reorder level
              category: p.categoryId,
            })),
            warning: warningProducts.map(p => ({
              id: p.id,
              name: p.name,
              sku: p.sku,
              quantity: p.quantity,
              reorderLevel: p.reorderLevel || 10, // Default reorder level
              category: p.categoryId,
            })),
          },
        });
        
        // Send alert to admin dashboard
        this.websocketService.sendToRole('admin', 'inventory_status', {
          message: `Inventory Status: ${criticalProducts.length} products out of stock, ${urgentProducts.length} critically low`,
          critical: criticalProducts.length,
          urgent: urgentProducts.length,
          warning: warningProducts.length,
        });
        
        // For critical products (out of stock), send specialized notifications
        if (criticalProducts.length > 0) {
          // Send to RabbitMQ for email processing
          await this.rabbitMQService.publish({
            type: 'stock_critical_notification',
            data: {
              count: criticalProducts.length,
              products: criticalProducts.map(p => ({
                id: p.id,
                name: p.name,
                sku: p.sku,
                lastRestocked: p.updatedAt,
              })),
            },
          });
        }
        
        // Also publish the full report to RabbitMQ for further processing
        await this.rabbitMQService.publish({
          type: 'low_stock_report',
          data: {
            timestamp: new Date(),
            summary: {
              total: lowStockProducts.length,
              critical: criticalProducts.length,
              urgent: urgentProducts.length,
              warning: warningProducts.length,
            },
            products: lowStockProducts.map(p => ({
              id: p.id,
              name: p.name,
              sku: p.sku,
              quantity: p.quantity,
              reorderLevel: p.reorderLevel || 10, // Default reorder level
              category: p.categoryId,
              status: p.quantity === 0 ? 'critical' : 
                     (p.quantity <= 5 ? 'urgent' : 'warning'),
            })),
          },
        });
      }
    } catch (error) {
      this.logger.error(`Error checking low stock levels: ${error.message}`, error.stack);
    }
  }

  /**
   * Check for products that need to be reordered based on sales velocity
   * Runs once a day at 7 AM
   */
  @Cron(CronExpression.EVERY_DAY_AT_7AM)
  async analyzeSalesVelocityAndReorderNeeds() {
    this.logger.log('Analyzing sales velocity and reordering needs');
    
    try {
      // Get all active products with their current stock
      const products = await this.prismaService.product.findMany({
        where: {
          isActive: true,
        },
      });
      
      // Calculate the last 30 days from current date
      const thirtyDaysAgo = new Date();
      thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
      
      const reorderSuggestions: ReorderSuggestion[] = [];
      
      // For each product, analyze order history to calculate sales velocity
      for (const product of products) {
        // Find order items containing this product from the last 30 days
        const orderItems = await this.prismaService.orderItem.findMany({
          where: {
            productId: product.id,
            order: {
              createdAt: {
                gte: thirtyDaysAgo,
              },
              status: {
                in: [OrderStatus.DELIVERED, OrderStatus.COMPLETE],
              },
            },
          },
          include: {
            order: {
              select: {
                createdAt: true,
              },
            },
          },
        });
        
        // Calculate total sold quantity in the last 30 days
        const totalSoldQuantity = orderItems.reduce((sum, item) => sum + item.quantity, 0);
        
        // Calculate daily sales rate
        const dailySalesRate = totalSoldQuantity / 30;
        
        // Determine days of inventory left
        const daysOfInventoryLeft = product.quantity / (dailySalesRate || 0.01); // Avoid division by zero
        
        // If less than 14 days of inventory left and it's not already below reorder level, suggest reorder
        const defaultReorderLevel = 10; // Default reorder level
        if (daysOfInventoryLeft < 14 && product.quantity > defaultReorderLevel) {
          // Calculate suggested reorder quantity (enough for 30 days plus safety stock)
          const suggestedOrderQuantity = Math.ceil(dailySalesRate * 30 * 1.2) - product.quantity;
          
          if (suggestedOrderQuantity > 0) {
            reorderSuggestions.push({
              id: product.id,
              name: product.name,
              sku: product.sku,
              currentStock: product.quantity,
              dailySalesRate: dailySalesRate.toFixed(2),
              daysOfInventoryLeft: Math.round(daysOfInventoryLeft),
              suggestedOrderQuantity,
            });
          }
        }
      }
      
      // If there are any reorder suggestions, send notifications
      if (reorderSuggestions.length > 0) {
        this.logger.log(`Generated ${reorderSuggestions.length} product reorder suggestions based on sales velocity`);
        
        // Send to inventory managers via WebSocket
        this.websocketService.sendToRole('inventory_manager', 'reorder_suggestions', {
          message: `${reorderSuggestions.length} products suggested for reorder based on sales velocity`,
          timestamp: new Date(),
          suggestions: reorderSuggestions,
        });
        
        // Send to RabbitMQ for further processing
        await this.rabbitMQService.publish({
          type: 'product_reorder_suggestions',
          data: {
            timestamp: new Date(),
            count: reorderSuggestions.length,
            suggestions: reorderSuggestions,
          },
        });
      }
    } catch (error) {
      this.logger.error(`Error analyzing sales velocity: ${error.message}`, error.stack);
    }
  }

  /**
   * Generate weekly inventory status report
   * Runs every Monday at 6 AM
   */
  @Cron('0 6 * * 1')
  async generateWeeklyInventoryReport() {
    this.logger.log('Generating weekly inventory status report');
    
    try {
      // Get all product categories
      const categories = await this.prismaService.productCategory.findMany();
      
      const report: InventoryReport = {
        generatedAt: new Date(),
        summary: {
          totalProducts: 0,
          totalValue: '0',
          lowStockProducts: 0,
          outOfStockProducts: 0,
        },
        byCategory: [],
      };
      
      // For each category, analyze inventory
      for (const category of categories) {
        // Get products in this category
        const products = await this.prismaService.product.findMany({
          where: {
            category: category.id,
          },
        });
        
        if (products.length === 0) continue;
        
        let categoryTotalValue = 0;
        let categoryLowStock = 0;
        let categoryOutOfStock = 0;
        
        products.forEach(product => {
          // Calculate inventory value
          const inventoryValue = parseFloat(product.price) * product.quantity;
          categoryTotalValue += inventoryValue;
          
          // Count low and out of stock products
          if (product.quantity === 0) {
            categoryOutOfStock++;
          } else if (product.quantity <= 10) { // Using default reorder level of 10
            categoryLowStock++;
          }
        });
        
        // Update summary totals
        report.summary.totalProducts += products.length;
        report.summary.totalValue = (parseFloat(report.summary.totalValue) + categoryTotalValue).toFixed(2);
        report.summary.lowStockProducts += categoryLowStock;
        report.summary.outOfStockProducts += categoryOutOfStock;
        
        // Add category details to report
        report.byCategory.push({
          id: category.id,
          name: category.name,
          productCount: products.length,
          inventoryValue: categoryTotalValue.toFixed(2),
          lowStockCount: categoryLowStock,
          outOfStockCount: categoryOutOfStock,
          stockHealth: products.length > 0 
            ? (((products.length - categoryLowStock - categoryOutOfStock) / products.length) * 100).toFixed(2)
            : '0.00',
        });
      }
      
      // Calculate overall stock health percentage
      const healthyProducts = report.summary.totalProducts - report.summary.lowStockProducts - report.summary.outOfStockProducts;
      report.summary.stockHealth = report.summary.totalProducts > 0
        ? ((healthyProducts / report.summary.totalProducts) * 100).toFixed(2)
        : '0.00';
      
      // Send report via WebSocket
      this.websocketService.sendToRole('inventory_manager', 'weekly_inventory_report', {
        message: 'Weekly inventory status report is available',
        report,
      });
      
      this.websocketService.sendToRole('admin', 'weekly_inventory_report', {
        message: 'Weekly inventory status report is available',
        report,
      });
      
      // Send to RabbitMQ for further processing (PDF generation, email distribution)
      await this.rabbitMQService.publish({
        type: 'weekly_inventory_report',
        data: report,
      });
      
      this.logger.log('Successfully generated weekly inventory report');
    } catch (error) {
      this.logger.error(`Error generating weekly inventory report: ${error.message}`, error.stack);
    }
  }
}
