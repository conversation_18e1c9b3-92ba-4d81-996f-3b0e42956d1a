import { Injectable, Logger } from '@nestjs/common';
import { Cron, CronExpression } from '@nestjs/schedule';
import { WebsocketService } from '../../infra/websocket/websocket.service';
import { RabbitMQService } from '../../infra/rabbitmq/rabbitmq.service';
import { FirebaseMessagingService, NotificationPayload } from '../../infra/firebase/firebase-messaging.service';
import { PrismaService } from '../prisma/prisma.service';

@Injectable()
export class NotificationSchedulerService {
  private readonly logger = new Logger(NotificationSchedulerService.name);

  constructor(
    private readonly websocketService: WebsocketService,
    private readonly rabbitMQService: RabbitMQService,
    private readonly prismaService: PrismaService,
    private readonly firebaseMessagingService: FirebaseMessagingService,
  ) {}

  /**
   * Send daily summary notifications to admin users
   * Runs every day at 9:00 AM
   */
  @Cron(CronExpression.EVERY_DAY_AT_9AM)
  async sendDailySummaryNotification() {
    this.logger.log('Running daily summary notification task');
    
    try {
      // Get statistics for the past 24 hours
      const yesterday = new Date();
      yesterday.setDate(yesterday.getDate() - 1);
      
      // Count new orders
      const newOrdersCount = await this.prismaService.order.count({
        where: {
          createdAt: {
            gte: yesterday,
          },
        },
      });
      
      // Count new users
      const newUsersCount = await this.prismaService.user.count({
        where: {
          createdAt: {
            gte: yesterday,
          },
        },
      });
      
      // Count revenue for the day
      const transactions = await this.prismaService.transaction.findMany({
        where: {
          type: 'SALE',
          status: 'COMPLETED',
          createdAt: {
            gte: yesterday,
          },
        },
      });
      
      let totalRevenue = 0;
      transactions.forEach(transaction => {
        totalRevenue += parseFloat(transaction.amount);
      });
      
      const summaryData = {
        date: new Date().toISOString().split('T')[0],
        newOrders: newOrdersCount,
        newUsers: newUsersCount,
        revenue: totalRevenue.toFixed(2),
        transactionCount: transactions.length,
      };
      
      // Send notification via WebSocket to admin users
      this.websocketService.sendToRole('admin', 'daily_summary', {
        message: 'Daily business summary',
        data: summaryData,
      });
      
      // Send mobile push notification via Firebase
      await this.sendFirebasePushToAdmins({
        title: 'Daily Business Summary',
        body: `Today's overview: ${newOrdersCount} new orders, ${totalRevenue.toFixed(2)} revenue`,
        data: {
          type: 'daily_summary',
          date: summaryData.date,
          newOrders: summaryData.newOrders.toString(),
          newUsers: summaryData.newUsers.toString(),
          revenue: summaryData.revenue,
          transactionCount: summaryData.transactionCount.toString(),
        },
      });
      
      // Also publish to RabbitMQ for further processing (e.g., email sending)
      await this.rabbitMQService.publish({
        type: 'daily_summary_notification',
        data: summaryData,
      });
      
      this.logger.log('Daily summary notification sent successfully');
    } catch (error) {
      this.logger.error(`Error sending daily summary notification: ${error.message}`, error.stack);
    }
  }

  /**
   * Check for system events and send notifications
   * Runs every 30 minutes
   */
  @Cron(CronExpression.EVERY_30_MINUTES)
  async checkSystemEvents() {
    this.logger.log('Checking system events for notification');
    
    try {
      // Example: Check for pending orders that haven't been processed in 2 hours
      const twoHoursAgo = new Date();
      twoHoursAgo.setHours(twoHoursAgo.getHours() - 2);
      
      const pendingOrders = await this.prismaService.order.findMany({
        where: {
          status: 'NEW',
          createdAt: {
            lte: twoHoursAgo,
          },
        },
      });
      
      if (pendingOrders.length > 0) {
        const notificationData = {
          count: pendingOrders.length,
          orders: pendingOrders.map(order => ({
            id: order.id,
            customerName: order.customerName || 'Unknown',
            total: order.total,
            createdAt: order.createdAt,
          })),
        };

        // Send WebSocket notification
        this.websocketService.sendToRole('admin', 'pending_orders_alert', {
          message: `${pendingOrders.length} orders have been pending for more than 2 hours`,
          orders: notificationData.orders,
        });
        
        // Send Firebase push notification
        if (pendingOrders.length > 5) {
          await this.sendFirebasePushToAdmins({
            title: 'Pending Orders Alert',
            body: `${pendingOrders.length} orders have been pending for more than 2 hours`,
            data: {
              type: 'pending_orders_alert',
              count: pendingOrders.length.toString(),
            },
          });
        }
        
        // Send to RabbitMQ for other processing
        await this.rabbitMQService.publish({
          type: 'pending_orders_notification',
          data: notificationData,
        });
      }
    } catch (error) {
      this.logger.error(`Error checking system events: ${error.message}`, error.stack);
    }
  }

  /**
   * Send real-time activity feed updates to users
   * Runs every 5 minutes
   */
  @Cron(CronExpression.EVERY_5_MINUTES)
  async updateActivityFeed() {
    this.logger.log('Updating activity feed');
    
    try {
      // Get recent transactions
      const fiveMinutesAgo = new Date();
      fiveMinutesAgo.setMinutes(fiveMinutesAgo.getMinutes() - 5);
      
      const recentTransactions = await this.prismaService.transaction.findMany({
        where: {
          createdAt: {
            gte: fiveMinutesAgo,
          },
        },
        orderBy: {
          createdAt: 'desc',
        },
        take: 10,
      });
      
      // Get recent orders
      const recentOrders = await this.prismaService.order.findMany({
        where: {
          createdAt: {
            gte: fiveMinutesAgo,
          },
        },
        orderBy: {
          createdAt: 'desc',
        },
        take: 10,
      });
      
      // Format activity data
      const activities = [
        ...recentTransactions.map(tx => ({
          type: 'transaction',
          id: tx.id,
          description: tx.description,
          timestamp: tx.createdAt,
          amount: tx.amount,
        })),
        ...recentOrders.map(order => ({
          type: 'order',
          id: order.id,
          description: `New order ${order.id}`,
          timestamp: order.createdAt,
          amount: order.total,
          customer: order.customerName,
        })),
      ].sort((a, b) => b.timestamp.getTime() - a.timestamp.getTime());
      
      if (activities.length > 0) {
        // Send to all admin and sales users via WebSocket
        this.websocketService.sendToRole('admin', 'activity_feed_update', {
          activities,
        });
        
        this.websocketService.sendToRole('sales', 'activity_feed_update', {
          activities,
        });
        
        // Send a mobile notification for high-value transactions
        const highValueTransactions = activities.filter(
          activity => activity.type === 'transaction' && parseFloat(activity.amount) > 5000
        );
        
        if (highValueTransactions.length > 0) {
          await this.sendFirebasePushToTopic('sales-notifications', {
            title: 'High-Value Transaction Alert',
            body: `${highValueTransactions.length} high-value transactions just occurred`,
            data: {
              type: 'high_value_transactions',
              count: highValueTransactions.length.toString(),
            },
          });
        }
      }
    } catch (error) {
      this.logger.error(`Error updating activity feed: ${error.message}`, error.stack);
    }
  }

  /**
   * Send inventory alert notifications
   * Runs once a day at 10:00 AM
   */
  @Cron(CronExpression.EVERY_DAY_AT_10AM)
  async sendInventoryAlerts() {
    this.logger.log('Sending inventory alerts');
    
    try {
      // Find products with low stock
      const lowStockProducts = await this.prismaService.product.findMany({
        where: {
          quantity: {
            lte: this.prismaService.product.fields.quantity,
          },
          isActive: true,
        },
        orderBy: {
          quantity: 'asc',
        },
      });
      
      if (lowStockProducts.length > 0) {
        // Critical products (out of stock)
        const criticalProducts = lowStockProducts.filter(p => p.quantity === 0);
        
        if (criticalProducts.length > 0) {
          // Send Firebase push notification for critical stock levels
          await this.sendFirebasePushToTopic('inventory-alerts', {
            title: 'CRITICAL: Out of Stock Products',
            body: `${criticalProducts.length} products are completely out of stock`,
            data: {
              type: 'out_of_stock',
              count: criticalProducts.length.toString(),
              productIds: criticalProducts.map(p => p.id.toString()).join(','),
            },
          });
        }
        
        // Send a general low stock notification
        await this.sendFirebasePushToTopic('inventory-alerts', {
          title: 'Low Stock Alert',
          body: `${lowStockProducts.length} products need attention`,
          data: {
            type: 'low_stock',
            count: lowStockProducts.length.toString(),
          },
        });
      }
    } catch (error) {
      this.logger.error(`Error sending inventory alerts: ${error.message}`, error.stack);
    }
  }

  /**
   * Helper method to send Firebase push notifications to admin users
   */
  private async sendFirebasePushToAdmins(notification: NotificationPayload): Promise<void> {
    try {
      // Get admin users with FCM tokens
      const adminUsers = await this.prismaService.user.findMany({
        where: {
          roles: {
            has: 'admin',
          },
          deviceToken: {
            not: null,
          },
        },
        select: {
          deviceToken: true,
        },
      });
      
      const tokens = adminUsers
        .map(user => user.deviceToken)
        .filter((token): token is string => !!token);
      
      if (tokens.length === 0) {
        this.logger.warn('No admin users with FCM tokens found');
        return;
      }
      
      // Send notification to all admin devices
      const result = await this.firebaseMessagingService.sendToDevices(tokens, notification);
      
      this.logger.log(`Firebase notification sent to ${result.successCount} admin devices with ${result.failureCount} failures`);
      
      // Handle failed tokens if needed
      if (result.failureCount > 0 && result.failedTokens.length > 0) {
        // Log or handle invalid tokens (could remove them from user records)
        this.logger.warn(`Failed to send to ${result.failureCount} tokens: ${result.failedTokens.join(', ')}`);
      }
    } catch (error) {
      this.logger.error(`Error sending push notification to admins: ${error.message}`, error.stack);
    }
  }

  /**
   * Helper method to send Firebase push notifications to a topic
   */
  private async sendFirebasePushToTopic(topic: string, notification: NotificationPayload): Promise<void> {
    try {
      await this.firebaseMessagingService.sendToTopic(topic, notification);
      this.logger.log(`Firebase notification sent to topic: ${topic}`);
    } catch (error) {
      this.logger.error(`Error sending push notification to topic ${topic}: ${error.message}`, error.stack);
    }
  }
}
