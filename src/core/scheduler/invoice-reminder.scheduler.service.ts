import { Injectable, Logger } from '@nestjs/common';
import { Cron, CronExpression } from '@nestjs/schedule';
import { PrismaService } from '../prisma/prisma.service';
import { WebsocketService } from '../../infra/websocket/websocket.service';
import { RabbitMQService } from '../../infra/rabbitmq/rabbitmq.service';
import { DecimalService } from '../../shared/decimal/decimal.service';

@Injectable()
export class InvoiceReminderSchedulerService {
  private readonly logger = new Logger(InvoiceReminderSchedulerService.name);

  constructor(
    private readonly prismaService: PrismaService,
    private readonly websocketService: WebsocketService,
    private readonly rabbitMQService: RabbitMQService,
    private readonly decimalService: DecimalService,
  ) {}

  /**
   * Send reminders for upcoming due invoices
   * Runs every day at 8:00 AM
   */
  @Cron(CronExpression.EVERY_DAY_AT_8AM)
  async sendUpcomingDueInvoiceReminders() {
    this.logger.log('Checking for upcoming due invoices');
    
    try {
      // Get invoices due in the next 3 days
      const threeDaysFromNow = new Date();
      threeDaysFromNow.setDate(threeDaysFromNow.getDate() + 3);
      
      const today = new Date();
      
      const upcomingInvoices = await this.prismaService.invoice.findMany({
        where: {
          status: 'PENDING',
          dueDate: {
            gte: today,
            lte: threeDaysFromNow,
          },
        },
        include: {
          order: true,
        },
      });
      
      this.logger.log(`Found ${upcomingInvoices.length} upcoming due invoices`);
      
      for (const invoice of upcomingInvoices) {
        // Get the user/customer associated with the invoice
        const user = invoice.order?.customerId 
          ? await this.prismaService.user.findUnique({
              where: { id: invoice.order.customerId },
            })
          : null;
        
        if (user) {
          // Send notification to user via WebSocket
          this.websocketService.sendToUser(user.id, 'invoice_reminder', {
            message: `Invoice #${invoice.invoiceNumber} is due on ${invoice.dueDate.toLocaleDateString()}`,
            invoice: {
              id: invoice.id,
              invoiceNumber: invoice.invoiceNumber,
              amount: invoice.total,
              dueDate: invoice.dueDate,
              status: invoice.status,
            },
          });
          
          // Send to RabbitMQ for email processing
          await this.rabbitMQService.publish({
            type: 'invoice_reminder_email',
            data: {
              to: user.email,
              name: user.firstName,
              invoiceId: invoice.id,
              invoiceNumber: invoice.invoiceNumber,
              amount: invoice.total,
              dueDate: invoice.dueDate,
              daysRemaining: Math.ceil((invoice.dueDate.getTime() - today.getTime()) / (1000 * 60 * 60 * 24)),
            },
          });
        }
        
        // Send notification to admin dashboard
        this.websocketService.sendToRole('finance', 'upcoming_due_invoice', {
          invoice: {
            id: invoice.id,
            invoiceNumber: invoice.invoiceNumber,
            amount: invoice.total,
            dueDate: invoice.dueDate,
            status: invoice.status,
            customer: user ? `${user.firstName} ${user.lastName}` : 'Unknown',
            email: user?.email || 'N/A',
          },
        });
      }
    } catch (error) {
      this.logger.error(`Error sending invoice reminders: ${error.message}`, error.stack);
    }
  }

  /**
   * Send notifications for overdue invoices
   * Runs every day at 9:00 AM
   */
  @Cron(CronExpression.EVERY_DAY_AT_9AM)
  async sendOverdueInvoiceNotifications() {
    this.logger.log('Checking for overdue invoices');
    
    try {
      const yesterday = new Date();
      yesterday.setDate(yesterday.getDate() - 1);
      
      const overdueInvoices = await this.prismaService.invoice.findMany({
        where: {
          status: 'PENDING',
          dueDate: {
            lt: yesterday,
          },
        },
        include: {
          order: true,
        },
      });
      
      this.logger.log(`Found ${overdueInvoices.length} overdue invoices`);
      
      // Group overdue invoices by user
      const overdueByUser = {};
      
      for (const invoice of overdueInvoices) {
        // Calculate days overdue
        const today = new Date();
        const daysOverdue = Math.ceil((today.getTime() - invoice.dueDate.getTime()) / (1000 * 60 * 60 * 24));
        
        // Get the user associated with the invoice
        const user = invoice.order?.customerId 
          ? await this.prismaService.user.findUnique({
              where: { id: invoice.order.customerId },
            })
          : null;
        
        if (user) {
          if (!overdueByUser[user.id]) {
            overdueByUser[user.id] = {
              user,
              invoices: [],
              totalAmount: '0',
            };
          }
          
          overdueByUser[user.id].invoices.push({
            id: invoice.id,
            invoiceNumber: invoice.invoiceNumber,
            amount: invoice.total,
            dueDate: invoice.dueDate,
            daysOverdue,
          });
          
          // Sum the total overdue amount using DecimalService for precision
          overdueByUser[user.id].totalAmount = this.decimalService
            .add(overdueByUser[user.id].totalAmount, invoice.total)
            .toString();
        }
        
        // Notify finance team about each overdue invoice
        this.websocketService.sendToRole('finance', 'overdue_invoice', {
          invoice: {
            id: invoice.id,
            invoiceNumber: invoice.invoiceNumber,
            amount: invoice.total,
            dueDate: invoice.dueDate,
            status: invoice.status,
            daysOverdue,
            customer: user ? `${user.firstName} ${user.lastName}` : 'Unknown',
            email: user?.email || 'N/A',
          },
        });
      }
      
      // Send consolidated overdue notifications to each user
      for (const userId in overdueByUser) {
        const { user, invoices, totalAmount } = overdueByUser[userId];
        
        // Send via WebSocket
        this.websocketService.sendToUser(userId, 'overdue_invoices', {
          message: `You have ${invoices.length} overdue invoice(s) totaling ${totalAmount}`,
          invoices,
          totalAmount,
        });
        
        // Send to RabbitMQ for email processing
        await this.rabbitMQService.publish({
          type: 'overdue_invoices_email',
          data: {
            to: user.email,
            name: user.firstName,
            invoiceCount: invoices.length,
            totalAmount,
            invoices,
          },
        });
      }
      
      // Send summary to finance department
      this.websocketService.sendToRole('finance', 'overdue_invoices_summary', {
        message: `There are ${overdueInvoices.length} overdue invoices`,
        count: overdueInvoices.length,
        totalAmount: overdueInvoices.reduce((sum, invoice) => 
          this.decimalService.add(sum, invoice.total).toString(), '0'),
        customers: Object.values(overdueByUser).length,
      });
    } catch (error) {
      this.logger.error(`Error sending overdue invoice notifications: ${error.message}`, error.stack);
    }
  }

  /**
   * Generate monthly invoice reports
   * Runs on the 1st day of each month at 1:00 AM
   */
  @Cron('0 1 1 * *')
  async generateMonthlyInvoiceReport() {
    this.logger.log('Generating monthly invoice report');
    
    try {
      // Get the previous month
      const now = new Date();
      const previousMonth = new Date(now.getFullYear(), now.getMonth() - 1, 1);
      const lastDayOfPreviousMonth = new Date(now.getFullYear(), now.getMonth(), 0);
      
      // Format month name and year
      const monthName = previousMonth.toLocaleString('default', { month: 'long' });
      const year = previousMonth.getFullYear();
      
      // Query all invoices for the previous month
      const invoices = await this.prismaService.invoice.findMany({
        where: {
          createdAt: {
            gte: previousMonth,
            lte: lastDayOfPreviousMonth,
          },
        },
      });
      
      // Calculate statistics
      const totalInvoices = invoices.length;
      const paidInvoices = invoices.filter(i => i.status === 'PAID').length;
      const pendingInvoices = invoices.filter(i => i.status === 'PENDING').length;
      const cancelledInvoices = invoices.filter(i => i.status === 'CANCELLED').length;
      
      let totalAmount = '0';
      let paidAmount = '0';
      let pendingAmount = '0';
      
      for (const invoice of invoices) {
        totalAmount = this.decimalService.add(totalAmount, invoice.total).toString();
        
        if (invoice.status === 'PAID') {
          paidAmount = this.decimalService.add(paidAmount, invoice.total).toString();
        } else if (invoice.status === 'PENDING') {
          pendingAmount = this.decimalService.add(pendingAmount, invoice.total).toString();
        }
      }
      
      // Create report data
      const reportData = {
        period: `${monthName} ${year}`,
        generatedAt: new Date(),
        statistics: {
          totalInvoices,
          paidInvoices,
          pendingInvoices,
          cancelledInvoices,
          totalAmount,
          paidAmount,
          pendingAmount,
          collectionRate: totalInvoices > 0 
            ? (paidInvoices / totalInvoices * 100).toFixed(2) 
            : '0.00',
        },
      };
      
      // Send report to finance team
      this.websocketService.sendToRole('finance', 'monthly_invoice_report', {
        message: `Monthly Invoice Report for ${monthName} ${year} is ready`,
        report: reportData,
      });
      
      // Publish to RabbitMQ for further processing (PDF generation, email distribution)
      await this.rabbitMQService.publish({
        type: 'monthly_invoice_report',
        data: reportData,
      });
      
      this.logger.log(`Successfully generated monthly invoice report for ${monthName} ${year}`);
    } catch (error) {
      this.logger.error(`Error generating monthly invoice report: ${error.message}`, error.stack);
    }
  }
}
