import { Injectable, Logger } from '@nestjs/common';
import { Cron, CronExpression } from '@nestjs/schedule';
import { PrismaService } from '../prisma/prisma.service';
import { MongoDbService } from '../../infra/mongodb/mongodb.service';

// Extended CronExpression enum to add missing expressions
enum ExtendedCronExpression {
  EVERY_15_MINUTES = '*/15 * * * *',
}

@Injectable()
export class DataSyncSchedulerService {
  private readonly logger = new Logger(DataSyncSchedulerService.name);

  constructor(
    private readonly prismaService: PrismaService,
    private readonly mongoDbService: MongoDbService,
  ) {}

  /**
   * Synchronize users data from PostgreSQL to MongoDB
   * Runs every hour
   */
  @Cron(CronExpression.EVERY_HOUR)
  async syncUsersData() {
    this.logger.log('Starting user data synchronization from PostgreSQL to MongoDB');
    
    try {
      const users = await this.prismaService.user.findMany();
      
      for (const user of users) {
        // Remove sensitive data like password before syncing
        const { password, ...userWithoutPassword } = user;
        
        await this.mongoDbService.syncDocument('users', userWithoutPassword);
      }
      
      this.logger.log(`Successfully synchronized ${users.length} users to MongoDB`);
    } catch (error) {
      this.logger.error(`Error synchronizing user data: ${error.message}`, error.stack);
    }
  }

  /**
   * Synchronize products data from PostgreSQL to MongoDB
   * Runs every 30 minutes
   */
  @Cron(CronExpression.EVERY_30_MINUTES)
  async syncProductsData() {
    this.logger.log('Starting product data synchronization from PostgreSQL to MongoDB');
    
    try {
      const products = await this.prismaService.product.findMany();
      
      for (const product of products) {
        await this.mongoDbService.syncDocument('products', product);
      }
      
      this.logger.log(`Successfully synchronized ${products.length} products to MongoDB`);
    } catch (error) {
      this.logger.error(`Error synchronizing product data: ${error.message}`, error.stack);
    }
  }

  /**
   * Synchronize orders data from PostgreSQL to MongoDB
   * Runs every 15 minutes
   */
  @Cron(ExtendedCronExpression.EVERY_15_MINUTES)
  async syncOrdersData() {
    this.logger.log('Starting order data synchronization from PostgreSQL to MongoDB');
    
    try {
      const orders = await this.prismaService.order.findMany({
        include: {
          items: true,
        },
      });
      
      for (const order of orders) {
        await this.mongoDbService.syncDocument('orders', order);
      }
      
      this.logger.log(`Successfully synchronized ${orders.length} orders to MongoDB`);
    } catch (error) {
      this.logger.error(`Error synchronizing order data: ${error.message}`, error.stack);
    }
  }

  /**
   * Synchronize financial data from PostgreSQL to MongoDB
   * Runs every hour
   */
  @Cron(CronExpression.EVERY_HOUR)
  async syncFinancialData() {
    this.logger.log('Starting financial data synchronization from PostgreSQL to MongoDB');
    
    try {
      // Sync transactions
      const transactions = await this.prismaService.transaction.findMany();
      
      for (const transaction of transactions) {
        await this.mongoDbService.syncDocument('transactions', transaction);
      }
      
      // Sync invoices
      const invoices = await this.prismaService.invoice.findMany();
      
      for (const invoice of invoices) {
        await this.mongoDbService.syncDocument('invoices', invoice);
      }
      
      this.logger.log(`Successfully synchronized ${transactions.length} transactions and ${invoices.length} invoices to MongoDB`);
    } catch (error) {
      this.logger.error(`Error synchronizing financial data: ${error.message}`, error.stack);
    }
  }

  /**
   * Validate data consistency between PostgreSQL and MongoDB
   * Runs every day at midnight
   */
  @Cron(CronExpression.EVERY_DAY_AT_MIDNIGHT)
  async validateDataConsistency() {
    this.logger.log('Starting data consistency validation between PostgreSQL and MongoDB');
    
    try {
      // Check user counts
      const pgUserCount = await this.prismaService.user.count();
      const mongoUserCount = await this.mongoDbService.count('users', {});
      
      if (pgUserCount !== mongoUserCount) {
        this.logger.warn(`User count mismatch: PostgreSQL=${pgUserCount}, MongoDB=${mongoUserCount}`);
        // Trigger full sync
        await this.syncUsersData();
      }
      
      // Check product counts
      const pgProductCount = await this.prismaService.product.count();
      const mongoProductCount = await this.mongoDbService.count('products', {});
      
      if (pgProductCount !== mongoProductCount) {
        this.logger.warn(`Product count mismatch: PostgreSQL=${pgProductCount}, MongoDB=${mongoProductCount}`);
        // Trigger full sync
        await this.syncProductsData();
      }
      
      // Check order counts
      const pgOrderCount = await this.prismaService.order.count();
      const mongoOrderCount = await this.mongoDbService.count('orders', {});
      
      if (pgOrderCount !== mongoOrderCount) {
        this.logger.warn(`Order count mismatch: PostgreSQL=${pgOrderCount}, MongoDB=${mongoOrderCount}`);
        // Trigger full sync
        await this.syncOrdersData();
      }
      
      this.logger.log('Data consistency validation completed');
    } catch (error) {
      this.logger.error(`Error validating data consistency: ${error.message}`, error.stack);
    }
  }
}
