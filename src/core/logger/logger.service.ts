import { Injectable, LoggerService as NestLoggerService } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';

@Injectable()
export class LoggerService implements NestLoggerService {
  private context?: string;

  constructor(private configService: ConfigService) {}

  setContext(context: string) {
    this.context = context;
    return this;
  }

  log(message: any, context?: string) {
    const logContext = context || this.context;
    console.log(`[${new Date().toISOString()}] [${logContext}] [LOG] ${message}`);
  }

  error(message: any, trace?: string, context?: string) {
    const logContext = context || this.context;
    console.error(`[${new Date().toISOString()}] [${logContext}] [ERROR] ${message}`);
    if (trace) {
      console.error(trace);
    }
  }

  warn(message: any, context?: string) {
    const logContext = context || this.context;
    console.warn(`[${new Date().toISOString()}] [${logContext}] [WARN] ${message}`);
  }

  debug(message: any, context?: string) {
    if (this.configService.get('NODE_ENV') !== 'production') {
      const logContext = context || this.context;
      console.debug(`[${new Date().toISOString()}] [${logContext}] [DEBUG] ${message}`);
    }
  }

  verbose(message: any, context?: string) {
    if (this.configService.get('NODE_ENV') !== 'production') {
      const logContext = context || this.context;
      console.log(`[${new Date().toISOString()}] [${logContext}] [VERBOSE] ${message}`);
    }
  }
}
