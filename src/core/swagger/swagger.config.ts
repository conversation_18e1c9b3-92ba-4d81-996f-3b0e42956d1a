import { DocumentBuilder, SwaggerModule } from '@nestjs/swagger';
import { INestApplication } from '@nestjs/common';
import { Request, Response } from 'express';

/**
 * Configures and initializes Swagger documentation for the application
 * @param app NestJS application instance
 */
export function setupSwagger(app: INestApplication): void {
  try {
    const config = new DocumentBuilder()
      .setTitle('TailorLink API')
      .setDescription('The TailorLink platform API documentation')
      .setVersion('1.0')
      .addBearerAuth(
        {
          type: 'http',
          scheme: 'bearer',
          bearerFormat: 'JWT',
          name: 'Authorization',
          description: 'Enter JWT token',
          in: 'header',
        },
        'JWT-auth',
      )
      .addTag('auth', 'Authentication endpoints')
      .addTag('users', 'User management endpoints')
      .addTag('tailors', 'Tailor profile management endpoints')
      .addTag('measurements', 'Measurement management endpoints')
      .addTag('portfolio', 'Portfolio item management endpoints')
      .addTag('messages', 'Messaging endpoints')
      .addTag('trainings', 'Training management endpoints')
      .addTag('products', 'Product management endpoints')
      .addTag('orders', 'Order management endpoints')
      .addTag('finance', 'Finance management endpoints')
      .build();

    // Create Swagger document with explicit options to exclude GraphQL schemas if they have errors
    const document = SwaggerModule.createDocument(app, config, {
      ignoreGlobalPrefix: false,
      extraModels: [],
      deepScanRoutes: true,
      include: [], // This will include only the controllers you specify, empty means all
    });
    
    // Setup Swagger UI
    SwaggerModule.setup('api/docs', app, document, {
      swaggerOptions: {
        persistAuthorization: true,
        tagsSorter: 'alpha',
        operationsSorter: 'alpha',
        docExpansion: 'none',
        filter: true,
        displayRequestDuration: true,
      },
      customSiteTitle: 'TailorLink API Documentation',
    });
    
    // Add endpoint to download Swagger JSON
    const httpAdapter = app.getHttpAdapter();
    httpAdapter.get('/api/docs-json', (req: Request, res: Response) => {
      res.setHeader('Content-Type', 'application/json');
      res.setHeader('Content-Disposition', 'attachment; filename=swagger-spec.json');
      res.send(document);
    });
    
    // Add endpoint to download Swagger YAML
    httpAdapter.get('/api/docs-yaml', (req: Request, res: Response) => {
      const yaml = require('js-yaml');
      const yamlString = yaml.dump(document);
      res.setHeader('Content-Type', 'text/yaml');
      res.setHeader('Content-Disposition', 'attachment; filename=swagger-spec.yaml');
      res.send(yamlString);
    });
    
    console.log('Swagger documentation setup successfully!');
  } catch (error) {
    console.error('Failed to setup Swagger documentation:', error.message);
  }
}
